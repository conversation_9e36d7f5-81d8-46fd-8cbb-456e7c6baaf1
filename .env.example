# =============================================================================
# API KEYS - CONFIGURACIÓN PRINCIPAL DE LLM
# =============================================================================

# OpenRouter API Key (REQUERIDA) - Principal proveedor LLM
# Obténla en: https://openrouter.ai/keys
OPENROUTER_API_KEY=apikey

# Modelo principal para todas las operaciones de QAK
# Opciones: google/gemini-2.0-flash-exp, anthropic/claude-3.5-sonnet, etc.
LLM_MODEL=google/gemini-2.0-flash-exp

# Proveedores alternativos de IA (opcionales)
# Solo necesarios si usas fallback o comparación de modelos
GOOGLE_API_KEY=
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=

# =============================================================================
# OPENROUTER - CONFIGURACIÓN AVANZADA (YA CONFIGURADO ARRIBA COMO PRINCIPAL)
# =============================================================================

# Configuraciones adicionales de OpenRouter (API Key ya configurada arriba)

# Feature flags para migración gradual a OpenRouter (todas habilitadas)
USE_OPENROUTER_FOR_GHERKIN=true      # Generación de Gherkin desde user stories
USE_OPENROUTER_FOR_VALIDATION=true   # Validación de resultados de tests
USE_OPENROUTER_FOR_TRANSLATION=true  # Traducción de prompts entre idiomas
USE_OPENROUTER_FOR_ENHANCEMENT=true  # Mejora y refinamiento de tests

# Configuraciones de optimización de costos
# 🚀 MODO CHEAP MODELS HABILITADO - Permite modelos de bajo costo además de gratuitos
# Ejemplos de modelos cheap disponibles: GPT-4o mini, Claude 3.5 Haiku, Gemini 1.5 Flash
OPENROUTER_PREFER_FREE_MODELS=false       # Permitir modelos de pago (cheap models)
OPENROUTER_MAX_MONTHLY_SPEND=5.0          # Límite máximo de gasto mensual en USD (0.0 = solo gratuitos)
OPENROUTER_MAX_COST_PER_1K_TOKENS=0.05    # Límite máximo de costo por 1K tokens (aumentado para cheap models)
RESPECT_RATE_LIMITS=true                  # Respetar límites de tasa de API
RATE_LIMIT_BUFFER=0.2                     # Buffer de 20% para rate limits

# =============================================================================
# CONFIGURACIÓN DE IDIOMA Y LOCALIZACIÓN
# =============================================================================

# Idioma principal para prompts y respuestas
# Opciones: en (English), es (Spanish)
PROMPT_LANGUAGE=es

# =============================================================================
# GITHUB INTEGRATION - PARA COMMITS Y REPORTES
# =============================================================================

# Token de GitHub para crear commits automáticos y reportes
GITHUB_TOKEN=****************************************
GITHUB_REPO=nahuelcio/qak

# =============================================================================
# OPTIMIZACIÓN DE RENDIMIENTO Y RATE LIMITING
# =============================================================================

# Delay en segundos entre llamadas a API LLM para evitar rate limits
# Ajusta según tu plan de API (menor para planes premium)
QAK_API_CALL_DELAY=10

# =============================================================================
# REDIS CONFIGURATION - CELERY BACKGROUND JOBS & MULTI-AGENT COORDINATION
# =============================================================================

# Para DESARROLLO LOCAL (recomendado) - Descomenta para usar Redis local:
REDIS_URL=redis://localhost:6379/0# Requiere: redis-server redis-local.conf

# Para PRODUCCIÓN con Upstash Redis (configuración actual):
# IMPORTANTE: Upstash requiere SSL (rediss://) en puerto 6380, no 6379
# REDIS_URL=rediss://default:<EMAIL>:6380

# Configuración con certificados SSL relajados (recomendada para Upstash)
# REDIS_URL=rediss://default:<EMAIL>:6380?ssl_cert_reqs=none

# REST API configuration para browser-use y otras integraciones directas
# UPSTASH_REDIS_REST_URL=https://still-narwhal-44706.upstash.io
# UPSTASH_REDIS_REST_TOKEN=Aa6iAAIjcDFmYzU0NmFmNDY2ODY0MTg3ODliMWMwOWM0MmI5Y2E4YnAxMA

# Backend de almacenamiento para multi-agent system
# STORAGE_BACKEND=upstash
STORAGE_BACKEND=redis




# Browser Use - Configuración Optimizada para Gemini 2.5 Flash Preview
# Copia este archivo a .env y completa tus valores reales

# =============================================================================
# API KEYS - REQUERIDAS
# =============================================================================

# =============================================================================
# RATE LIMITING - OPTIMIZADO PARA GEMINI 2.5 FLASH PREVIEW
# =============================================================================
# Gemini 2.5 Flash Preview tiene límites: 15 RPM, 1M TPM
# Configuración conservadora para evitar rate limit errors
BROWSER_USE_RATE_LIMIT_AGGRESSIVE=false
BROWSER_USE_GEMINI_RPM=12          # 15 RPM real - 3 de margen
BROWSER_USE_GEMINI_TPM=800000      # 1M TPM real - 200k de margen

# =============================================================================
# EMBEDDINGS - MEMORIA PROCEDURAL
# =============================================================================
# Configuración para embeddings usados en memoria a largo plazo
EMBEDDING_PROVIDER=huggingface
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMS=384

# =============================================================================
# PLANNER - RAZONAMIENTO ESTRATÉGICO
# =============================================================================
# El planner ayuda a tomar decisiones más inteligentes
BROWSER_USE_PLANNER_INTERVAL=3     # Ejecutar planner cada 3 steps
BROWSER_USE_PLANNER_REASONING=true # Habilitar chain-of-thought

# =============================================================================
# CONTEXTO Y MEMORIA
# =============================================================================
# Optimizaciones de contexto para mejor rendimiento
BROWSER_USE_MAX_CONTEXT_TOKENS=120000  # Ajustado para Gemini (128k max)
BROWSER_USE_VISION_QUALITY=medium      # low, medium, high

# =============================================================================
# CONFIGURACIONES GENERALES BROWSER-USE
# =============================================================================

# Browser-use specific LLM configuration (override defaults)
# Use OpenRouter instead of Google AI for browser automation
# BROWSER_USE_LLM_MODEL=openai/gpt-4o-mini
# BROWSER_USE_MODEL_PROVIDER=openrouter
# BROWSER_USE_MODEL_NAME=openai/gpt-4o-mini

# Logging level
LMNR_LOGGING_LEVEL=debug

# =============================================================================
# BROWSER-USE CLOUD SYNC - CONFIGURACIÓN LOCAL
# =============================================================================
# Configuración para enviar eventos de browser-use a tu API local
# en lugar de a los servidores externos de la librería
# 🚀 BENEFICIOS: Privacidad total, control completo, sin dependencias externas

# Habilitar sincronización en la nube (con tu API local)
# Cambia a 'false' si no quieres sincronización
BROWSER_USE_CLOUD_SYNC=true

# URL de tu API local para recibir eventos de browser-use
# Cambia el puerto si tu API usa uno diferente
BROWSER_USE_CLOUD_API_URL=http://localhost:8000

# URL de la interfaz web para monitorear eventos (opcional)
# Puedes usar /docs para la documentación interactiva de FastAPI
BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui


# =============================================================================
# CONFIGURACIONES AVANZADAS (OPCIONAL)
# =============================================================================
# Saltar verificación de API keys (solo para testing)
# SKIP_LLM_API_KEY_VERIFICATION=false

# Configuración de directorios
# XDG_CACHE_HOME=~/.cache
# XDG_CONFIG_HOME=~/.config
# BROWSER_USE_CONFIG_DIR=~/.config/browseruse

# =============================================================================
# NOTAS DE USO
# =============================================================================
# 1. GOOGLE_API_KEY es obligatoria - obténla en Google AI Studio
# 2. Los límites de rate son conservadores, puedes ajustarlos según tu uso
# 3. El planner consume más API calls pero mejora la precisión
# 4. La memoria requiere: pip install "browser-use[memory]"
# 5. Para desarrollo, puedes desactivar planner y memoria
#    para ahorrar API calls
# 6. Usando gemini-2.5-flash-preview-05-20 para máximo rendimiento

# =============================================================================
# CONFIGURACIÓN PARA DESARROLLO
# =============================================================================
# Para testing/desarrollo (menos API calls)
# BROWSER_USE_PLANNER_INTERVAL=5
# BROWSER_USE_GEMINI_RPM=8
# EMBEDDING_PROVIDER=ollama  # Si tienes Ollama local

# =============================================================================
# CONFIGURACIÓN PARA PRODUCCIÓN
# =============================================================================  
# Para uso intensivo (más agresivo pero controlado)
# BROWSER_USE_GEMINI_RPM=15
# BROWSER_USE_PLANNER_INTERVAL=2
# BROWSER_USE_MAX_CONTEXT_TOKENS=130000

# =============================================================================
# MODELOS ESPECÍFICOS - GEMINI 2.5 FLASH PREVIEW
# =============================================================================
# Modelo principal optimizado
GEMINI_MAIN_MODEL=gemini-2.5-flash
# Modelo para planner (más rápido)
GEMINI_PLANNER_MODEL=gemini-2.5-flash
# Modelo de embeddings para memoria
GEMINI_EMBEDDING_MODEL=models/text-embedding-004 

SAVE_SMOKE_TEST_CONVERSATIONS=true
SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests"

BROWSER_USE_SEMANTIC_MEMORY=false

## LOGFIRE CONFIG 
BROWSER_USE_LOGFIRE=true
# 2) Crea un write token en la UI de Logfire (Settings → Write tokens)
LOGFIRE_TOKEN="pylf_v1_us_pVGm5gPGrRzlN4bV4Nv3QghM5X8sKTRp7PK3KP2VcjBm"
# Opcionales
LOGFIRE_SERVICE_NAME="aery-browser"          # ya por defecto
LOGFIRE_ENVIRONMENT="dev"        
LOGFIRE_SERVICE_VERSION="0.1.0"
LOGFIRE_PROJECT=nahuelcio/qak     # nombre del proyecto
LOGFIRE_LEVEL=DEBUG                # nivel mínimo

DISABLE_LLM_VALIDATION=true

# =============================================================================
# MONGODB - BASE DE DATOS PRINCIPAL QAK
# =============================================================================

# URI de conexión a MongoDB Atlas (producción)
# Contiene credenciales y configuración de cluster
MONGODB_URI="mongodb+srv://qakdmin:<EMAIL>/?retryWrites=true&w=majority&appName=AeryQak"

# Entorno de base de datos (production/development/testing)
MONGODB_ENVIRONMENT=production

# =============================================================================
# QAK DATABASE FEATURE FLAGS
# =============================================================================

# Habilitar uso de base de datos MongoDB (vs almacenamiento local)
QAK_USE_DATABASE=true 

# Habilitar sistema de proyectos en base de datos
QAK_DATABASE_PROJECTS=true

# =============================================================================
# CLOUDFLARE R2 CONFIGURATION - QAK ARTIFACTS STORAGE
# =============================================================================
# Configuración para almacenar artifacts de test en Cloudflare R2
# 🚀 Beneficios: Sin costos de egress, performance global, 90% más barato que S3

# R2 Bucket Configuration (YA CONFIGURADO)
R2_BUCKET_NAME=qak-yoiz
R2_ACCOUNT_ID=0276ae9ddbc7008af3717e363e958fd8

# R2 API Credentials (CONFIGURADO ✅)
# Ve a: Cloudflare R2 → Manage R2 API tokens → Create token
# Permisos: Admin Read & Write en bucket qak-yoiz
R2_ACCESS_KEY_ID=7348224074e023af28781b77fc616fc5
R2_SECRET_ACCESS_KEY=a04ad05862379db295b523fee4a9c289b094e890f48c8b3b84161a09ed3f7f3f

# Optional: Custom domain for direct access (configurar en Cloudflare)
# R2_PUBLIC_URL=https://artifacts.yourdomain.com

# QAK Artifact Configuration
ARTIFACT_STORAGE_BACKEND=r2
ARTIFACT_AUTO_MIGRATE=true
ARTIFACT_MIGRATE_AFTER_MINUTES=30
ARTIFACT_RETENTION_DAYS=90
ARTIFACT_MAX_STORAGE_GB=100

# Enhanced Features
ARTIFACT_AUTO_COMPRESS=true
ARTIFACT_GENERATE_THUMBNAILS=true
ARTIFACT_MAX_CONCURRENT_UPLOADS=5
ARTIFACT_REMOVE_LOCAL_AFTER_MIGRATION=true

# R2-Only Storage Configuration (NO LOCAL FILES)
ARTIFACT_R2_ONLY_MODE=true
ARTIFACT_SKIP_LOCAL_STORAGE=true

# =============================================================================
# CLOUDFLARE R2 - ENDPOINTS ESPECÍFICOS
# =============================================================================
# Endpoint URL específico para tu account (CONFIGURADO ✅)
R2_ENDPOINT_URL=https://0276ae9ddbc7008af3717e363e958fd8.r2.cloudflarestorage.com

# Configuraciones S3 compatibles para R2
R2_USE_PATH_STYLE=false
R2_SIGNATURE_VERSION=s3v4
R2_REGION_HINT=auto 

AI_ANALYSIS_COST_OPTIMIZATION=high  

# ---------------------------------------------------------------
# OPTIONAL: Background Jobs Configuration
# ---------------------------------------------------------------
# Enable background processing for AI analysis (requires Redis + Celery)
# When enabled, AI analysis runs asynchronously and doesn't block the API
USE_BACKGROUND_JOBS=true

# Celery worker configuration
CELERY_LOG_LEVEL=INFO