#!/usr/bin/env python3
"""Test script to verify BrowserConfiguration model allows empty execution_types."""

import sys
sys.path.append('/Users/<USER>/Proyectos/qak')

from src.database.models.browser_configuration import BrowserConfiguration, ExecutionType

# Test 1: Create with empty execution types
print("Test 1: Creating configuration with empty execution_types")
try:
    config_data = {
        'name': 'test-empty',
        'execution_types': []
    }
    config = BrowserConfiguration(**config_data)
    print(f"✅ Success: execution_types = {config.execution_types}")
    print(f"   Length: {len(config.execution_types)}")
except Exception as e:
    print(f"❌ Error: {type(e).__name__}: {e}")

# Test 2: Create with specific execution types  
print("\nTest 2: Creating configuration with specific execution_types")
try:
    config_data = {
        'name': 'test-specific',
        'execution_types': [ExecutionType.SMOKE, ExecutionType.FULL]
    }
    config = BrowserConfiguration(**config_data)
    print(f"✅ Success: execution_types = {config.execution_types}")
    print(f"   Length: {len(config.execution_types)}")
except Exception as e:
    print(f"❌ Error: {type(e).__name__}: {e}")

# Test 3: Create with no execution_types field (should use defaults)
print("\nTest 3: Creating configuration without execution_types field")
try:
    config_data = {
        'name': 'test-default'
    }
    config = BrowserConfiguration(**config_data)
    print(f"✅ Success: execution_types = {config.execution_types}")
    print(f"   Length: {len(config.execution_types)}")
except Exception as e:
    print(f"❌ Error: {type(e).__name__}: {e}")

# Test 4: Check model dict representation
print("\nTest 4: Model dict representation with empty execution_types")
try:
    config_data = {
        'name': 'test-dict',
        'execution_types': []
    }
    config = BrowserConfiguration(**config_data)
    config_dict = config.model_dump()
    print(f"✅ Success: model_dump execution_types = {config_dict.get('execution_types')}")
except Exception as e:
    print(f"❌ Error: {type(e).__name__}: {e}")

print("\nAll tests completed!")
