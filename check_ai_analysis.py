#!/usr/bin/env python3
"""Check AI analysis in MongoDB executions"""

from src.database.repositories.execution_repository import ExecutionRepository
from src.database.connection import get_database
import asyncio

async def check_recent_executions():
    db = await get_database()
    collection = db['executions']
    
    # Get the most recent 3 executions
    recent_executions = []
    async for doc in collection.find({}).sort('created_at', -1).limit(3):
        recent_executions.append(doc)
    
    print(f'Found {len(recent_executions)} recent executions')
    for i, exec_doc in enumerate(recent_executions):
        print(f'\n=== Execution {i+1} ===')
        print(f'ID: {exec_doc.get("_id")}')
        print(f'Status: {exec_doc.get("status")}')
        print(f'Created: {exec_doc.get("created_at")}')
        
        # Check for AI analysis
        metadata = exec_doc.get('metadata', {})
        ai_analysis = metadata.get('ai_analysis')
        ai_analysis_status = metadata.get('ai_analysis_status')
        ai_analysis_job_id = metadata.get('ai_analysis_job_id')
        
        print(f'AI Analysis Status: {ai_analysis_status}')
        print(f'AI Analysis Job ID: {ai_analysis_job_id}')
        print(f'Has AI Analysis: {bool(ai_analysis)}')
        
        if ai_analysis:
            print(f'AI Analysis keys: {list(ai_analysis.keys())}')
            if 'completion_analysis' in ai_analysis:
                comp_analysis = ai_analysis['completion_analysis']
                print(f'Completion Analysis verdict: {comp_analysis.get("final_verdict")}')
                print(f'Completion Analysis confidence: {comp_analysis.get("confidence_level")}')
        
        print('-' * 50)

if __name__ == "__main__":
    asyncio.run(check_recent_executions())
