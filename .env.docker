# =============================================================================
# CONFIGURACIÓN DOCKER PARA QAK - BASADA EN .env.example
# =============================================================================
# Este archivo contiene variables preconfiguradas para el entorno Docker
# Copia este archivo a .env y ajusta los valores según tus necesidades

# =============================================================================
# API KEYS - CONFIGURACIÓN PRINCIPAL DE LLM
# =============================================================================

# OpenRouter API Key (REQUERIDA) - Principal proveedor LLM
# Obténla en: https://openrouter.ai/keys
OPENROUTER_API_KEY=

# Modelo principal para todas las operaciones de QAK
# Opciones: openai/gpt-4o-mini, google/gemini-2.0-flash-exp, anthropic/claude-3.5-sonnet, etc.
LLM_MODEL=openai/gpt-4.1-mini

# Proveedores alternativos de IA (opcionales)
# Solo necesarios si usas fallback o comparación de modelos
GOOGLE_API_KEY=
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GROQ_API_KEY=

# =============================================================================
# OPENROUTER - CONFIGURACIÓN AVANZADA
# =============================================================================

# Feature flags para migración gradual a OpenRouter (todas habilitadas)
USE_OPENROUTER_FOR_GHERKIN=true
USE_OPENROUTER_FOR_VALIDATION=true
USE_OPENROUTER_FOR_TRANSLATION=true
USE_OPENROUTER_FOR_ENHANCEMENT=true

# Configuraciones de optimización de costos
OPENROUTER_PREFER_FREE_MODELS=true        # Preferir modelos gratuitos cuando sea posible
OPENROUTER_MAX_MONTHLY_SPEND=1.0          # Límite máximo de gasto mensual en USD (0.0 = solo gratuitos)
OPENROUTER_MAX_COST_PER_1K_TOKENS=0.01    # Límite máximo de costo por 1K tokens
RESPECT_RATE_LIMITS=true                  # Respetar límites de tasa de API
RATE_LIMIT_BUFFER=0.2                     # Buffer de 20% para rate limits

# =============================================================================
# CONFIGURACIÓN DE IDIOMA Y LOCALIZACIÓN
# =============================================================================

# Idioma principal para prompts y respuestas
PROMPT_LANGUAGE=es

# =============================================================================
# GITHUB INTEGRATION - PARA COMMITS Y REPORTES
# =============================================================================

# Token de GitHub para crear commits automáticos y reportes
GITHUB_TOKEN=****************************************
GITHUB_REPO=nahuelcio/qak

# =============================================================================
# OPTIMIZACIÓN DE RENDIMIENTO Y RATE LIMITING
# =============================================================================

# Delay en segundos entre llamadas a API LLM
QAK_API_CALL_DELAY=10

# =============================================================================
# REDIS CONFIGURATION - CELERY BACKGROUND JOBS & MULTI-AGENT COORDINATION
# =============================================================================

# Para DOCKER COMPOSE (configuración actual):
REDIS_URL=redis://redis:6379/0

# Para DESARROLLO LOCAL (alternativo) - Descomenta para usar Redis local:
# REDIS_URL=redis://localhost:6379/0# Requiere: redis-server redis-local.conf

# Para PRODUCCIÓN con Upstash Redis (configuración actual):
# IMPORTANTE: Upstash requiere SSL (rediss://) en puerto 6380, no 6379
# REDIS_URL=rediss://default:<EMAIL>:6380

# Configuración con certificados SSL relajados (recomendada para Upstash)
# REDIS_URL=rediss://default:<EMAIL>:6380?ssl_cert_reqs=none

# REST API configuration para browser-use y otras integraciones directas
# UPSTASH_REDIS_REST_URL=https://still-narwhal-44706.upstash.io
# UPSTASH_REDIS_REST_TOKEN=Aa6iAAIjcDFmYzU0NmFmNDY2ODY0MTg3ODliMWMwOWM0MmI5Y2E4YnAxMA

# Backend de almacenamiento para multi-agent system
# STORAGE_BACKEND=upstash
STORAGE_BACKEND=redis

# =============================================================================
# BROWSER-USE CONFIGURATION - OPENAI GPT-4.1-MINI
# =============================================================================
# Configuración específica para browser-use usando OpenAI GPT-4.1-mini
# en lugar de Gemini para mejor rendimiento en automatización web

# Browser-use specific LLM configuration (override defaults)
BROWSER_USE_LLM_MODEL=openai/gpt-4.1-mini
BROWSER_USE_MODEL_PROVIDER=openrouter
BROWSER_USE_MODEL_NAME=openai/gpt-4.1-mini

# Rate limiting optimizado para GPT-4.1-mini via OpenRouter
BROWSER_USE_RATE_LIMIT_AGGRESSIVE=false
BROWSER_USE_OPENAI_RPM=30          # GPT-4.1-mini tiene mejores límites
BROWSER_USE_OPENAI_TPM=2000000     # 2M TPM para GPT-4.1-mini
BROWSER_USE_GEMINI_RPM=12          # 15 RPM real - 3 de margen
BROWSER_USE_GEMINI_TPM=800000      # 1M TPM real - 200k de margen

# Embeddings para memoria procedural
EMBEDDING_PROVIDER=huggingface
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMS=384

# Planner - razonamiento estratégico
BROWSER_USE_PLANNER_INTERVAL=3
BROWSER_USE_PLANNER_REASONING=true

# Contexto y memoria
BROWSER_USE_MAX_CONTEXT_TOKENS=120000
BROWSER_USE_VISION_QUALITY=medium

# Logging level
LMNR_LOGGING_LEVEL=debug

# =============================================================================
# BROWSER-USE CLOUD SYNC - CONFIGURACIÓN DOCKER
# =============================================================================

# Habilitar sincronización en la nube (con API interna)
BROWSER_USE_CLOUD_SYNC=true

# URL de la API interna para recibir eventos de browser-use
BROWSER_USE_CLOUD_API_URL=http://qak-api:8000

# URL de la interfaz web para monitorear eventos
BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui

# =============================================================================
# MODELOS ESPECÍFICOS - GEMINI 2.5 FLASH PREVIEW
# =============================================================================

GEMINI_MAIN_MODEL=gemini-2.5-flash
GEMINI_PLANNER_MODEL=gemini-2.5-flash
GEMINI_EMBEDDING_MODEL=models/text-embedding-004

# =============================================================================
# CONFIGURACIONES ADICIONALES
# =============================================================================

SAVE_SMOKE_TEST_CONVERSATIONS=true
SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests"
BROWSER_USE_SEMANTIC_MEMORY=false

# Force headless mode for all browser-use operations
# This overrides any display detection logic in browser-use
BROWSER_USE_HEADLESS=true

# Force test_case environment to ensure headless mode
# Prevents development environment from overriding headless=False
BROWSER_TEST_ENV=test_case

# =============================================================================
# LOGFIRE CONFIG
# =============================================================================

BROWSER_USE_LOGFIRE=true
LOGFIRE_TOKEN="pylf_v1_us_pVGm5gPGrRzlN4bV4Nv3QghM5X8sKTRp7PK3KP2VcjBm"
LOGFIRE_SERVICE_NAME="aery-browser"
LOGFIRE_ENVIRONMENT="dev"
LOGFIRE_SERVICE_VERSION="0.1.0"
LOGFIRE_PROJECT=nahuelcio/qak
LOGFIRE_LEVEL=DEBUG

DISABLE_LLM_VALIDATION=true

# =============================================================================
# MONGODB - BASE DE DATOS PRINCIPAL QAK
# =============================================================================

# URI de conexión a MongoDB Atlas (producción)
# Contiene credenciales y configuración de cluster
MONGODB_URI="mongodb+srv://qakdmin:<EMAIL>/?retryWrites=true&w=majority&appName=AeryQak"

# Entorno de base de datos (production/development/testing)
MONGODB_ENVIRONMENT=development
MONGODB_DATABASE=qak_dev

# =============================================================================
# QAK DATABASE FEATURE FLAGS
# =============================================================================

QAK_USE_DATABASE=true
QAK_DATABASE_PROJECTS=true

# =============================================================================
# CLOUDFLARE R2 CONFIGURATION - QAK ARTIFACTS STORAGE
# =============================================================================

# R2 Bucket Configuration (YA CONFIGURADO)
R2_BUCKET_NAME=qak-yoiz
R2_ACCOUNT_ID=0276ae9ddbc7008af3717e363e958fd8

# R2 API Credentials (CONFIGURADO ✅)
# Ve a: Cloudflare R2 → Manage R2 API tokens → Create token
# Permisos: Admin Read & Write en bucket qak-yoiz
R2_ACCESS_KEY_ID=7348224074e023af28781b77fc616fc5
R2_SECRET_ACCESS_KEY=a04ad05862379db295b523fee4a9c289b094e890f48c8b3b84161a09ed3f7f3f

# Endpoint URL específico para tu account (CONFIGURADO ✅)
R2_ENDPOINT_URL=https://0276ae9ddbc7008af3717e363e958fd8.r2.cloudflarestorage.com

# QAK Artifact Configuration
ARTIFACT_STORAGE_BACKEND=r2
ARTIFACT_AUTO_MIGRATE=true
ARTIFACT_MIGRATE_AFTER_MINUTES=30
ARTIFACT_RETENTION_DAYS=90
ARTIFACT_MAX_STORAGE_GB=100

# Enhanced Features
ARTIFACT_AUTO_COMPRESS=true
ARTIFACT_GENERATE_THUMBNAILS=true
ARTIFACT_MAX_CONCURRENT_UPLOADS=5
ARTIFACT_REMOVE_LOCAL_AFTER_MIGRATION=true

# R2-Only Storage Configuration
ARTIFACT_R2_ONLY_MODE=true
ARTIFACT_SKIP_LOCAL_STORAGE=true

# Configuraciones S3 compatibles para R2
R2_USE_PATH_STYLE=false
R2_SIGNATURE_VERSION=s3v4
R2_REGION_HINT=auto

AI_ANALYSIS_COST_OPTIMIZATION=high

# =============================================================================
# AI ANALYSIS CONFIGURATION - OPENAI GPT-4.1-MINI
# =============================================================================
# Configuración específica para análisis de AI usando OpenAI GPT-4.1-mini
# para mejor precisión y velocidad en análisis de tests y código

# AI Analysis specific model (override LLM_MODEL for analysis tasks)
AI_ANALYSIS_MODEL=openai/gpt-4.1-mini
AI_ANALYSIS_PROVIDER=openrouter
AI_ANALYSIS_USE_OPENROUTER=true

# Rate limiting para análisis de AI
AI_ANALYSIS_RPM=30
AI_ANALYSIS_TPM=2000000
AI_ANALYSIS_MAX_RETRIES=3
AI_ANALYSIS_RETRY_DELAY=2

# =============================================================================
# BACKGROUND JOBS CONFIGURATION - DOCKER OPTIMIZADO
# =============================================================================

# Habilitar procesamiento en background (requiere Redis + Celery)
USE_BACKGROUND_JOBS=true

# Configuración de Celery worker
CELERY_LOG_LEVEL=INFO

# Permitir ejecutar Celery como root (necesario en Docker)
C_FORCE_ROOT=1

# =============================================================================
# CONFIGURACIONES DE API
# =============================================================================

# Configuración del host y puerto de la API
API_HOST=0.0.0.0
API_PORT=8000