# Start QAK System Locally (No Docker) - Windows PowerShell Script
# This script starts <PERSON><PERSON>, Celery worker, and QAK API in background

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "🚀 QAK Local Startup Script for Windows" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\start_qak_local.ps1"
    Write-Host ""
    Write-Host "Prerequisites:"
    Write-Host "  - Python virtual environment (.venv)"
    Write-Host "  - Redis (via Chocolatey, WSL, or Docker)"
    Write-Host "  - All Python dependencies installed"
    Write-Host ""
    Write-Host "Installation commands:"
    Write-Host "  choco install redis-64        # Install Redis via Chocolatey"
    Write-Host "  python -m venv .venv          # Create virtual environment"
    Write-Host "  .\.venv\Scripts\Activate.ps1  # Activate virtual environment"
    Write-Host "  pip install -r requirements.txt  # Install dependencies"
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting QAK System Locally on Windows..." -ForegroundColor Cyan

# Check if virtual environment exists
if (!(Test-Path ".venv")) {
    Write-Host "❌ Virtual environment not found. Please run 'python -m venv .venv' first." -ForegroundColor Red
    exit 1
}

# Activate virtual environment
Write-Host "📦 Activating virtual environment..." -ForegroundColor Yellow
& ".\.venv\Scripts\Activate.ps1"

# Check if Redis is available
Write-Host "🔍 Checking Redis availability..." -ForegroundColor Yellow

$redisAvailable = $false
$redisCommand = ""

# Check for Redis via Chocolatey installation
if (Get-Command "redis-server" -ErrorAction SilentlyContinue) {
    $redisAvailable = $true
    $redisCommand = "redis-server"
    Write-Host "✅ Found Redis via system installation" -ForegroundColor Green
}
# Check for Redis via WSL
elseif (Get-Command "wsl" -ErrorAction SilentlyContinue) {
    try {
        $wslRedisTest = wsl redis-server --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $redisAvailable = $true
            $redisCommand = "wsl redis-server"
            Write-Host "✅ Found Redis via WSL" -ForegroundColor Green
        }
    }
    catch {
        # WSL Redis not available
    }
}

if (!$redisAvailable) {
    Write-Host "❌ Redis not found. Please install Redis:" -ForegroundColor Red
    Write-Host "   Option 1 (Chocolatey): choco install redis-64" -ForegroundColor Yellow
    Write-Host "   Option 2 (WSL): wsl sudo apt-get install redis-server" -ForegroundColor Yellow
    Write-Host "   Option 3 (Docker): docker run -d -p 6379:6379 redis:latest" -ForegroundColor Yellow
    exit 1
}

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Start Redis if not running
if (!(Test-Port 6379)) {
    Write-Host "🔴 Starting Redis server..." -ForegroundColor Red
    
    if ($redisCommand -eq "redis-server") {
        # Windows Redis
        Start-Process -FilePath "redis-server" -ArgumentList "--port 6379" -WindowStyle Hidden
    }
    elseif ($redisCommand -eq "wsl redis-server") {
        # WSL Redis
        Start-Process -FilePath "wsl" -ArgumentList "redis-server --daemonize yes --port 6379" -WindowStyle Hidden
    }
    
    Start-Sleep -Seconds 3
    
    if (Test-Port 6379) {
        Write-Host "✅ Redis started on port 6379" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Failed to start Redis" -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "✅ Redis already running on port 6379" -ForegroundColor Green
}

# Test Redis connection
try {
    $redisTest = redis-cli ping 2>$null
    if ($redisTest -eq "PONG") {
        Write-Host "✅ Redis connection test successful" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Redis connection test inconclusive" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠️ Redis connection test failed, but server may still be running" -ForegroundColor Yellow
}

# Start Celery worker in background
Write-Host "🔧 Starting Celery worker..." -ForegroundColor Yellow
$celeryJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    & ".\.venv\Scripts\Activate.ps1"
    python celery_worker.py
}
Write-Host "✅ Celery worker started with Job ID: $($celeryJob.Id)" -ForegroundColor Green

# Give worker time to start
Start-Sleep -Seconds 3

# Test worker
Write-Host "🧪 Testing Celery worker..." -ForegroundColor Yellow
try {
    $celeryTest = celery -A src.core.background_jobs.celery_app inspect ping 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Celery worker is responding" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ Celery worker may not be fully ready yet" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠️ Celery worker test failed, but worker may still be starting" -ForegroundColor Yellow
}

# Start QAK API in background
Write-Host "🌐 Starting QAK API server..." -ForegroundColor Yellow
$apiJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    & ".\.venv\Scripts\Activate.ps1"
    python app.py
}
Write-Host "✅ QAK API started with Job ID: $($apiJob.Id)" -ForegroundColor Green

# Give API time to start
Start-Sleep -Seconds 5

# Test API
try {
    $apiTest = Invoke-RestMethod -Uri "http://localhost:8000/health" -TimeoutSec 10
    Write-Host "✅ QAK API is responding" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ QAK API may not be fully ready yet" -ForegroundColor Yellow
}

# Test background jobs
Write-Host "🧪 Testing background jobs system..." -ForegroundColor Yellow
try {
    $bgJobsTest = Invoke-RestMethod -Uri "http://localhost:8000/api/v2/background-jobs/health" -TimeoutSec 10
    Write-Host "✅ Background jobs system is ready" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Background jobs system may not be ready" -ForegroundColor Yellow
}

# Get Redis process ID
$redisProcess = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
$redisPID = if ($redisProcess) { $redisProcess.Id } else { "N/A" }

Write-Host ""
Write-Host "🎉 QAK System Started Successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Service Status:" -ForegroundColor Cyan
Write-Host "   Redis:         http://localhost:6379 (PID: $redisPID)" -ForegroundColor White
Write-Host "   Celery Worker: Job ID $($celeryJob.Id)" -ForegroundColor White
Write-Host "   QAK API:       http://localhost:8000 (Job ID $($apiJob.Id))" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Quick Links:" -ForegroundColor Cyan
Write-Host "   API Health:     Invoke-RestMethod http://localhost:8000/health" -ForegroundColor White
Write-Host "   BG Jobs Health: Invoke-RestMethod http://localhost:8000/api/v2/background-jobs/health" -ForegroundColor White
Write-Host "   API Docs:       http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "📝 Log Management:" -ForegroundColor Cyan
Write-Host "   View Celery logs:  Receive-Job -Id $($celeryJob.Id) -Keep" -ForegroundColor White
Write-Host "   View API logs:     Receive-Job -Id $($apiJob.Id) -Keep" -ForegroundColor White
Write-Host ""
Write-Host "🛑 To stop services:" -ForegroundColor Cyan
Write-Host "   Stop-Job -Id $($celeryJob.Id); Remove-Job -Id $($celeryJob.Id)" -ForegroundColor White
Write-Host "   Stop-Job -Id $($apiJob.Id); Remove-Job -Id $($apiJob.Id)" -ForegroundColor White
Write-Host "   Get-Process redis-server | Stop-Process" -ForegroundColor White
Write-Host ""

# Save job IDs for easy stopping
$jobInfo = @{
    CeleryJobId = $celeryJob.Id
    ApiJobId = $apiJob.Id
    RedisPID = $redisPID
    StartTime = Get-Date
}

$jobInfo | ConvertTo-Json | Out-File -FilePath ".qak_jobs.json" -Encoding UTF8

Write-Host "💾 Job information saved to .qak_jobs.json" -ForegroundColor Green
Write-Host ""
Write-Host "🔄 System is ready for background job processing!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Pro Tips:" -ForegroundColor Cyan
Write-Host "   • Use 'Get-Job' to see all running jobs" -ForegroundColor White
Write-Host "   • Use 'Receive-Job -Id <JobId>' to see job output" -ForegroundColor White
Write-Host "   • Use '.\stop_qak_local.ps1' to stop all services" -ForegroundColor White
