#!/usr/bin/env python3
"""
Script para limpiar eventos existentes que contengan imágenes base64 grandes.

Este script ayuda a resolver problemas de memoria de MongoDB causados por
eventos que contienen imágenes base64 grandes en el campo 'data'.

Uso:
    python scripts/cleanup_base64_events.py [--dry-run] [--batch-size=100]
"""

import asyncio
import sys
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Agregar el directorio raíz al path
sys.path.append(str(Path(__file__).parent.parent))

from src.database.connection import get_database
from src.database.repositories.browser_use_event_repository import BrowserUseEventRepository
from src.api.browser_use_sync_routes import filter_event_data_for_storage


async def cleanup_base64_events(dry_run: bool = True, batch_size: int = 100):
    """
    Limpia eventos existentes removiendo imágenes base64 grandes.
    
    Args:
        dry_run: Si True, solo reporta lo que haría sin modificar datos
        batch_size: Número de eventos a procesar por lote
    """
    print(f"🧹 Iniciando limpieza de eventos con imágenes base64...")
    print(f"📊 Modo: {'SIMULACIÓN (dry-run)' if dry_run else 'EJECUCIÓN REAL'}")
    print(f"📦 Tamaño de lote: {batch_size}")
    print("-" * 60)
    
    # Inicializar repositorio
    event_repo = BrowserUseEventRepository()
    
    # Estadísticas
    total_events = 0
    events_with_base64 = 0
    total_size_before = 0
    total_size_after = 0
    
    try:
        # Obtener el total de eventos para mostrar progreso
        collection = await event_repo.collection
        total_count = await collection.count_documents({})
        print(f"📈 Total de eventos en la base de datos: {total_count}")
        
        # Procesar eventos en lotes
        skip = 0
        while True:
            # Obtener lote de eventos
            events = await collection.find({}).skip(skip).limit(batch_size).to_list(length=batch_size)
            
            if not events:
                break
                
            print(f"🔄 Procesando eventos {skip + 1} - {skip + len(events)}...")
            
            for event_doc in events:
                total_events += 1
                event_data = event_doc.get('data', {})
                
                if not isinstance(event_data, dict):
                    continue
                    
                # Calcular tamaño original
                original_size = len(str(event_data))
                total_size_before += original_size
                
                # Filtrar datos
                filtered_data = filter_event_data_for_storage(event_data)
                filtered_size = len(str(filtered_data))
                total_size_after += filtered_size
                
                # Verificar si había imágenes base64
                if original_size > filtered_size:
                    events_with_base64 += 1
                    size_reduction = original_size - filtered_size
                    size_reduction_pct = (size_reduction / original_size) * 100
                    
                    print(f"  📸 Evento {event_doc['_id']}: {size_reduction} bytes removidos ({size_reduction_pct:.1f}%)")
                    
                    # Actualizar evento si no es dry-run
                    if not dry_run:
                        await collection.update_one(
                            {"_id": event_doc["_id"]},
                            {"$set": {"data": filtered_data, "updated_at": datetime.utcnow()}}
                        )
            
            skip += len(events)
            
            # Mostrar progreso
            progress = (skip / total_count) * 100
            print(f"📊 Progreso: {progress:.1f}% ({skip}/{total_count})")
    
    except Exception as e:
        print(f"❌ Error durante la limpieza: {str(e)}")
        return False
    
    # Mostrar estadísticas finales
    print("\n" + "=" * 60)
    print("📈 ESTADÍSTICAS FINALES")
    print("=" * 60)
    print(f"📋 Total de eventos procesados: {total_events}")
    print(f"📸 Eventos con imágenes base64: {events_with_base64}")
    print(f"💾 Tamaño total antes: {total_size_before:,} bytes")
    print(f"💾 Tamaño total después: {total_size_after:,} bytes")
    
    if total_size_before > 0:
        total_reduction = total_size_before - total_size_after
        total_reduction_pct = (total_reduction / total_size_before) * 100
        print(f"📉 Reducción total: {total_reduction:,} bytes ({total_reduction_pct:.1f}%)")
    
    if dry_run:
        print("\n⚠️  MODO SIMULACIÓN - No se modificaron datos reales")
        print("💡 Ejecuta con --no-dry-run para aplicar los cambios")
    else:
        print("\n✅ Limpieza completada exitosamente")
        
    return True


async def main():
    """Función principal del script."""
    parser = argparse.ArgumentParser(description="Limpiar eventos con imágenes base64 grandes")
    parser.add_argument("--no-dry-run", action="store_true", help="Ejecutar cambios reales (por defecto es simulación)")
    parser.add_argument("--batch-size", type=int, default=100, help="Tamaño del lote para procesamiento")
    
    args = parser.parse_args()
    
    dry_run = not args.no_dry_run
    batch_size = args.batch_size
    
    # Validar batch size
    if batch_size < 1 or batch_size > 1000:
        print("❌ Error: batch-size debe estar entre 1 y 1000")
        return 1
    
    # Ejecutar limpieza
    success = await cleanup_base64_events(dry_run=dry_run, batch_size=batch_size)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
