# Check QAK System Status - Windows PowerShell Script
# This script checks the status of <PERSON><PERSON>, Celery worker, and QAK API services

param(
    [switch]$Detailed,
    [switch]$Help
)

if ($Help) {
    Write-Host "📊 QAK System Status Check for Windows" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\check_qak_status.ps1 [-Detailed]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -Detailed Show detailed service information"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    exit 0
}

Write-Host "📊 Checking QAK System Status..." -ForegroundColor Cyan
Write-Host ""

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to make HTTP request with timeout
function Test-HttpEndpoint {
    param([string]$Url, [int]$TimeoutSeconds = 5)
    try {
        $response = Invoke-RestMethod -Uri $Url -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        return @{ Success = $true; Response = $response }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Check Redis Status
Write-Host "🔴 Redis Status:" -ForegroundColor Red
$redisPort = Test-Port 6379
$redisProcess = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue

if ($redisPort -and $redisProcess) {
    Write-Host "   ✅ Running (Port 6379, PID: $($redisProcess.Id))" -ForegroundColor Green
    
    # Test Redis connection
    try {
        $redisTest = redis-cli ping 2>$null
        if ($redisTest -eq "PONG") {
            Write-Host "   ✅ Connection test: PASSED" -ForegroundColor Green
        }
        else {
            Write-Host "   ⚠️ Connection test: UNCLEAR" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "   ❌ Connection test: FAILED" -ForegroundColor Red
    }
}
elseif ($redisPort -and !$redisProcess) {
    Write-Host "   ⚠️ Port 6379 in use but redis-server process not found" -ForegroundColor Yellow
}
elseif (!$redisPort -and $redisProcess) {
    Write-Host "   ⚠️ Process found but port 6379 not responding" -ForegroundColor Yellow
}
else {
    Write-Host "   ❌ Not running" -ForegroundColor Red
}

# Check QAK API Status
Write-Host ""
Write-Host "🌐 QAK API Status:" -ForegroundColor Blue
$apiPort = Test-Port 8000

if ($apiPort) {
    Write-Host "   ✅ Port 8000 is active" -ForegroundColor Green
    
    # Test API endpoints
    $healthCheck = Test-HttpEndpoint "http://localhost:8000/health"
    if ($healthCheck.Success) {
        Write-Host "   ✅ Health check: PASSED" -ForegroundColor Green
        if ($Detailed -and $healthCheck.Response) {
            Write-Host "      Response: $($healthCheck.Response | ConvertTo-Json -Compress)" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "   ❌ Health check: FAILED - $($healthCheck.Error)" -ForegroundColor Red
    }
    
    # Test API docs
    $docsCheck = Test-HttpEndpoint "http://localhost:8000/docs"
    if ($docsCheck.Success) {
        Write-Host "   ✅ API docs: ACCESSIBLE" -ForegroundColor Green
    }
    else {
        Write-Host "   ❌ API docs: NOT ACCESSIBLE" -ForegroundColor Red
    }
}
else {
    Write-Host "   ❌ Not running (Port 8000 not responding)" -ForegroundColor Red
}

# Check Background Jobs Status
Write-Host ""
Write-Host "🔧 Background Jobs Status:" -ForegroundColor Yellow
if ($apiPort) {
    $bgJobsCheck = Test-HttpEndpoint "http://localhost:8000/api/v2/background-jobs/health"
    if ($bgJobsCheck.Success) {
        Write-Host "   ✅ Background jobs: ACTIVE" -ForegroundColor Green
        if ($Detailed -and $bgJobsCheck.Response) {
            Write-Host "      Response: $($bgJobsCheck.Response | ConvertTo-Json -Compress)" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "   ❌ Background jobs: NOT ACTIVE - $($bgJobsCheck.Error)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ❌ Cannot check (API not running)" -ForegroundColor Red
}

# Check Celery Worker Status
Write-Host ""
Write-Host "🔧 Celery Worker Status:" -ForegroundColor Yellow
if ($redisPort) {
    try {
        $celeryInspect = celery -A src.core.background_jobs.celery_app inspect ping 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Celery worker: RESPONDING" -ForegroundColor Green
        }
        else {
            Write-Host "   ❌ Celery worker: NOT RESPONDING" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ Celery worker: ERROR - $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "   ❌ Cannot check (Redis not running)" -ForegroundColor Red
}

# Check PowerShell Jobs
Write-Host ""
Write-Host "📋 PowerShell Jobs Status:" -ForegroundColor Magenta
$runningJobs = Get-Job | Where-Object { $_.State -eq "Running" }
if ($runningJobs) {
    Write-Host "   ✅ $($runningJobs.Count) job(s) running:" -ForegroundColor Green
    foreach ($job in $runningJobs) {
        Write-Host "      Job ID: $($job.Id) - $($job.Name) - State: $($job.State)" -ForegroundColor White
        if ($Detailed) {
            $jobOutput = Receive-Job -Id $job.Id -Keep 2>$null
            if ($jobOutput) {
                $recentOutput = $jobOutput | Select-Object -Last 3
                Write-Host "      Recent output: $($recentOutput -join '; ')" -ForegroundColor Gray
            }
        }
    }
}
else {
    Write-Host "   ❌ No running PowerShell jobs found" -ForegroundColor Red
}

# Check job info file
Write-Host ""
Write-Host "💾 Job Information File:" -ForegroundColor Cyan
if (Test-Path ".qak_jobs.json") {
    Write-Host "   ✅ Found .qak_jobs.json" -ForegroundColor Green
    try {
        $jobInfo = Get-Content ".qak_jobs.json" | ConvertFrom-Json
        Write-Host "      Celery Job ID: $($jobInfo.CeleryJobId)" -ForegroundColor White
        Write-Host "      API Job ID: $($jobInfo.ApiJobId)" -ForegroundColor White
        Write-Host "      Redis PID: $($jobInfo.RedisPID)" -ForegroundColor White
        Write-Host "      Start Time: $($jobInfo.StartTime)" -ForegroundColor White
        
        if ($Detailed) {
            Write-Host "      Full content: $($jobInfo | ConvertTo-Json -Compress)" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "   ⚠️ Error reading job info: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}
else {
    Write-Host "   ❌ No job information file found" -ForegroundColor Red
}

# Overall System Status
Write-Host ""
Write-Host "🎯 Overall System Status:" -ForegroundColor Cyan

$redisOk = $redisPort -and $redisProcess
$apiOk = $apiPort -and (Test-HttpEndpoint "http://localhost:8000/health").Success
$bgJobsOk = $apiPort -and (Test-HttpEndpoint "http://localhost:8000/api/v2/background-jobs/health").Success

if ($redisOk -and $apiOk -and $bgJobsOk) {
    Write-Host "   🎉 System is FULLY OPERATIONAL" -ForegroundColor Green
    Write-Host "   ✅ All core services are running properly" -ForegroundColor Green
}
elseif ($redisOk -and $apiOk) {
    Write-Host "   ⚠️ System is PARTIALLY OPERATIONAL" -ForegroundColor Yellow
    Write-Host "   ✅ API is running but background jobs may have issues" -ForegroundColor Yellow
}
elseif ($apiOk) {
    Write-Host "   ⚠️ System is MINIMAL OPERATIONAL" -ForegroundColor Yellow
    Write-Host "   ✅ API is running but Redis/background jobs are not available" -ForegroundColor Yellow
}
else {
    Write-Host "   ❌ System is NOT OPERATIONAL" -ForegroundColor Red
    Write-Host "   ❌ Critical services are not running" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔗 Quick Actions:" -ForegroundColor Cyan
Write-Host "   Start system:  .\start_qak_local.ps1" -ForegroundColor White
Write-Host "   Stop system:   .\stop_qak_local.ps1" -ForegroundColor White
Write-Host "   Detailed info: .\check_qak_status.ps1 -Detailed" -ForegroundColor White
Write-Host "   API docs:      http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
