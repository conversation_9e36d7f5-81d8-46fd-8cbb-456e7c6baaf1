#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Start Celery worker for Windows with optimal configuration

.DESCRIPTION
    This script starts a Celery worker optimized for Windows with the 'solo' pool
    to avoid multiprocessing issues that commonly occur on Windows.

.EXAMPLE
    .\start_celery_windows.ps1
#>

param(
    [string]$LogLevel = "INFO",
    [string]$Queues = "analysis,default"
)

# Set console title
$host.UI.RawUI.WindowTitle = "QAK Celery Worker"

# Change to project root
$projectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $projectRoot

# Ensure virtual environment is activated
if (-not $env:VIRTUAL_ENV) {
    Write-Host "⚠️  Virtual environment not detected. Please activate your virtual environment first." -ForegroundColor Yellow
    Write-Host "Run: .\.venv\Scripts\Activate.ps1" -ForegroundColor Cyan
    exit 1
}

# Check if Redis is running
$redisRunning = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
if (-not $redisRunning) {
    Write-Host "⚠️  Redis server not detected. Please start Redis first." -ForegroundColor Yellow
    Write-Host "Run: redis-server redis-local.conf" -ForegroundColor Cyan
    exit 1
}

Write-Host "🚀 Starting Celery worker for Windows..." -ForegroundColor Green
Write-Host "📝 Log Level: $LogLevel" -ForegroundColor Cyan
Write-Host "📋 Queues: $Queues" -ForegroundColor Cyan
Write-Host "🖥️  Pool: solo (Windows optimized)" -ForegroundColor Cyan

# Start Celery worker with Windows-optimized settings
try {
    python -m celery -A src.core.background_jobs.celery_app worker `
        --loglevel=$LogLevel `
        --queues=$Queues `
        --pool=solo `
        --concurrency=1 `
        --hostname=qak-worker@%env:COMPUTERNAME% `
        --without-gossip `
        --without-mingle `
        --without-heartbeat `
        --logfile=logs/celery_worker.log
} catch {
    Write-Host "❌ Error starting Celery worker: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
