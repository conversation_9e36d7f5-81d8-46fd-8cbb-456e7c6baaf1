# Setup Background Jobs for QAK
# This script installs and configures the background jobs system

# Enable strict error handling
$ErrorActionPreference = "Stop"

Write-Host "Setting up QAK Background Jobs System..." -ForegroundColor Green

# Check if virtual environment exists
if (-not (Test-Path ".venv")) {
    Write-Host "Virtual environment not found. Please run 'python -m venv .venv' first." -ForegroundColor Red
    exit 1
}

Write-Host "Activating virtual environment..." -ForegroundColor Yellow

# Activate virtual environment (Windows)
if (Test-Path ".venv\Scripts\Activate.ps1") {
    & ".venv\Scripts\Activate.ps1"
} elseif (Test-Path ".venv\Scripts\activate.bat") {
    & ".venv\Scripts\activate.bat"
} else {
    Write-Host "Cannot find virtual environment activation script." -ForegroundColor Red
    exit 1
}

Write-Host "Installing background job dependencies..." -ForegroundColor Yellow
try {
    & python -m pip install 'celery[redis]' redis flower kombu
    if ($LASTEXITCODE -ne 0) {
        throw "pip install failed"
    }
} catch {
    Write-Host "Failed to install dependencies: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Testing background jobs system..." -ForegroundColor Yellow

# Create a temporary Python file for testing
$tempTestFile = "temp_test_bg_jobs.py"
$pythonTestCode = @'
from src.core.background_jobs import BACKGROUND_JOBS_AVAILABLE
if BACKGROUND_JOBS_AVAILABLE:
    print("Background jobs system is ready!")
    from src.core.background_jobs.job_manager import get_job_manager
    print("Job manager initialized successfully")
else:
    print("Background jobs system not available")
    exit(1)
'@

try {
    # Write the Python code to a temporary file
    $pythonTestCode | Out-File -FilePath $tempTestFile -Encoding UTF8
    
    # Execute the Python test
    & python $tempTestFile
    if ($LASTEXITCODE -ne 0) {
        throw "Background jobs test failed"
    }
} catch {
    Write-Host "Background jobs system test failed: $_" -ForegroundColor Red
    exit 1
} finally {
    # Clean up temporary file
    if (Test-Path $tempTestFile) {
        Remove-Item $tempTestFile -Force
    }
}

Write-Host ""
Write-Host "Background Jobs Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Start Redis: docker run -d -p 6379:6379 redis:7-alpine" -ForegroundColor White
Write-Host "   OR local Redis: redis-server redis-local.conf" -ForegroundColor White
Write-Host "2. Start Celery worker (Windows): .\scripts\start_celery_windows.ps1" -ForegroundColor White
Write-Host "   OR standard: python celery_worker.py" -ForegroundColor White
Write-Host "3. Start QAK API: python app.py" -ForegroundColor White
Write-Host "4. Check health: curl http://localhost:8000/api/v2/background-jobs/health" -ForegroundColor White
Write-Host ""
Write-Host "Optional: Start Flower monitoring: celery -A src.core.background_jobs.celery_app flower --port=5555" -ForegroundColor Gray

Write-Host ""
Write-Host "Setup complete!" -ForegroundColor Green
