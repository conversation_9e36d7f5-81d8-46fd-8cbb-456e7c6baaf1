# Stop QAK System Locally - Windows PowerShell Script
# This script stops <PERSON><PERSON>, Celery worker, and QAK API services

param(
    [switch]$Force,
    [switch]$Help
)

if ($Help) {
    Write-Host "🛑 QAK Local Stop Script for Windows" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\stop_qak_local.ps1 [-Force]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -Force    Force stop all services without confirmation"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    exit 0
}

Write-Host "🛑 Stopping QAK System..." -ForegroundColor Yellow

# Check if job info file exists
if (Test-Path ".qak_jobs.json") {
    Write-Host "📋 Loading job information..." -ForegroundColor Yellow
    try {
        $jobInfo = Get-Content ".qak_jobs.json" | ConvertFrom-Json
        
        Write-Host "📊 Found running services:" -ForegroundColor Cyan
        Write-Host "   Celery Worker: Job ID $($jobInfo.CeleryJobId)" -ForegroundColor White
        Write-Host "   QAK API:       Job ID $($jobInfo.ApiJobId)" -ForegroundColor White
        Write-Host "   Redis:         PID $($jobInfo.RedisPID)" -ForegroundColor White
        Write-Host "   Started:       $($jobInfo.StartTime)" -ForegroundColor White
        Write-Host ""
        
        if (!$Force) {
            $confirmation = Read-Host "Stop all services? (y/N)"
            if ($confirmation -ne "y" -and $confirmation -ne "Y") {
                Write-Host "❌ Operation cancelled by user" -ForegroundColor Red
                exit 0
            }
        }
        
        # Stop Celery worker
        Write-Host "🔧 Stopping Celery worker..." -ForegroundColor Yellow
        try {
            $celeryJob = Get-Job -Id $jobInfo.CeleryJobId -ErrorAction SilentlyContinue
            if ($celeryJob) {
                Stop-Job -Id $jobInfo.CeleryJobId
                Remove-Job -Id $jobInfo.CeleryJobId
                Write-Host "✅ Celery worker stopped" -ForegroundColor Green
            }
            else {
                Write-Host "⚠️ Celery worker job not found (may have already stopped)" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "⚠️ Error stopping Celery worker: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Stop QAK API
        Write-Host "🌐 Stopping QAK API..." -ForegroundColor Yellow
        try {
            $apiJob = Get-Job -Id $jobInfo.ApiJobId -ErrorAction SilentlyContinue
            if ($apiJob) {
                Stop-Job -Id $jobInfo.ApiJobId
                Remove-Job -Id $jobInfo.ApiJobId
                Write-Host "✅ QAK API stopped" -ForegroundColor Green
            }
            else {
                Write-Host "⚠️ QAK API job not found (may have already stopped)" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "⚠️ Error stopping QAK API: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Stop Redis
        Write-Host "🔴 Stopping Redis..." -ForegroundColor Yellow
        try {
            $redisProcesses = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
            if ($redisProcesses) {
                $redisProcesses | Stop-Process -Force
                Write-Host "✅ Redis stopped" -ForegroundColor Green
            }
            else {
                Write-Host "⚠️ Redis process not found (may have already stopped)" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "⚠️ Error stopping Redis: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Clean up job info file
        Remove-Item ".qak_jobs.json" -ErrorAction SilentlyContinue
        Write-Host "🧹 Cleaned up job information file" -ForegroundColor Green
        
    }
    catch {
        Write-Host "❌ Error reading job information: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Attempting manual cleanup..." -ForegroundColor Yellow
    }
}
else {
    Write-Host "📋 No job information file found, attempting manual cleanup..." -ForegroundColor Yellow
}

# Manual cleanup - stop any remaining processes
Write-Host "🔍 Performing manual cleanup..." -ForegroundColor Yellow

# Stop all PowerShell jobs that might be QAK related
$allJobs = Get-Job | Where-Object { $_.State -eq "Running" }
if ($allJobs) {
    Write-Host "🛑 Found $($allJobs.Count) running PowerShell jobs:" -ForegroundColor Yellow
    foreach ($job in $allJobs) {
        Write-Host "   Job ID: $($job.Id) - $($job.Name)" -ForegroundColor White
    }
    
    if (!$Force) {
        $confirmation = Read-Host "Stop all running PowerShell jobs? (y/N)"
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            $allJobs | Stop-Job
            $allJobs | Remove-Job
            Write-Host "✅ All PowerShell jobs stopped" -ForegroundColor Green
        }
    }
    else {
        $allJobs | Stop-Job
        $allJobs | Remove-Job
        Write-Host "✅ All PowerShell jobs stopped" -ForegroundColor Green
    }
}

# Stop any remaining Redis processes
$redisProcesses = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
if ($redisProcesses) {
    Write-Host "🔴 Found $($redisProcesses.Count) Redis processes running" -ForegroundColor Yellow
    if (!$Force) {
        $confirmation = Read-Host "Stop all Redis processes? (y/N)"
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            $redisProcesses | Stop-Process -Force
            Write-Host "✅ All Redis processes stopped" -ForegroundColor Green
        }
    }
    else {
        $redisProcesses | Stop-Process -Force
        Write-Host "✅ All Redis processes stopped" -ForegroundColor Green
    }
}

# Check for Python processes that might be QAK related
$pythonProcesses = Get-Process -Name "python*" -ErrorAction SilentlyContinue | Where-Object {
    $_.CommandLine -like "*app.py*" -or 
    $_.CommandLine -like "*celery_worker.py*" -or 
    $_.CommandLine -like "*celery*"
}

if ($pythonProcesses) {
    Write-Host "🐍 Found $($pythonProcesses.Count) Python processes that might be QAK related" -ForegroundColor Yellow
    foreach ($proc in $pythonProcesses) {
        Write-Host "   PID: $($proc.Id) - $($proc.ProcessName)" -ForegroundColor White
    }
    
    if (!$Force) {
        $confirmation = Read-Host "Stop these Python processes? (y/N)"
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            $pythonProcesses | Stop-Process -Force
            Write-Host "✅ Python processes stopped" -ForegroundColor Green
        }
    }
    else {
        $pythonProcesses | Stop-Process -Force
        Write-Host "✅ Python processes stopped" -ForegroundColor Green
    }
}

# Final status check
Write-Host ""
Write-Host "🔍 Final status check..." -ForegroundColor Yellow

# Check if ports are free
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

$port8000 = Test-Port 8000
$port6379 = Test-Port 6379

Write-Host "📊 Port Status:" -ForegroundColor Cyan
Write-Host "   Port 8000 (API): $(if ($port8000) { '🔴 Still in use' } else { '✅ Free' })" -ForegroundColor $(if ($port8000) { 'Red' } else { 'Green' })
Write-Host "   Port 6379 (Redis): $(if ($port6379) { '🔴 Still in use' } else { '✅ Free' })" -ForegroundColor $(if ($port6379) { 'Red' } else { 'Green' })

$runningJobs = Get-Job | Where-Object { $_.State -eq "Running" }
Write-Host "   PowerShell Jobs: $(if ($runningJobs) { "🔴 $($runningJobs.Count) still running" } else { '✅ All stopped' })" -ForegroundColor $(if ($runningJobs) { 'Red' } else { 'Green' })

Write-Host ""
if (!$port8000 -and !$port6379 -and !$runningJobs) {
    Write-Host "🎉 QAK System completely stopped!" -ForegroundColor Green
}
else {
    Write-Host "⚠️ Some services may still be running. Check the status above." -ForegroundColor Yellow
    Write-Host "💡 Use the -Force parameter to stop all services without confirmation." -ForegroundColor Cyan
}
Write-Host ""
