# Frontend Migration Guide: Analytics Endpoint Update

## Overview

This document outlines the migration from the legacy `/api/analytics` endpoint to the new `/api/analytics-v2` endpoint in the frontend.

## Changes Made

### 1. Updated API Functions (`web/src/lib/api.ts`)

#### ✅ Updated `getAnalytics()` function
- **Before**: Used `/api/analytics` endpoint
- **After**: Uses `/api/analytics-v2` endpoint
- **Benefits**: 
  - Single source of truth (ExecutionRepository)
  - Real-time data consistency
  - Better performance
  - Removed `includeHistory` parameter (no longer needed)

#### ✅ Added `getAnalyticsLegacy()` function
- Marked as `@deprecated`
- Maintains backward compatibility temporarily
- Uses original `/api/analytics` endpoint
- **Will be removed in future versions**

#### ✅ Added `compareAnalytics()` function
- New utility for comparing legacy vs new data
- Useful for debugging and validation
- Uses `/api/analytics-comparison` endpoint

### 2. Updated TypeScript Types

#### Enhanced `AnalyticsResponse` type:
```typescript
export type AnalyticsResponse = {
  // ... existing fields ...
  testDetails?: Array<{
    // ... existing fields ...
    duration?: number; // NEW: seconds - from v2
  }>;
  // NEW: Fields from analytics-v2 endpoint
  dataSource?: string; // Indicates data source ("ExecutionRepository")
  dateRange?: {
    start: string;
    end: string;
  };
};
```

## Migration Status

### ✅ Completed
- [x] Updated `getAnalytics()` to use `/api/analytics-v2`
- [x] Added legacy compatibility function
- [x] Updated TypeScript types
- [x] Added comparison utility function
- [x] Verified no direct endpoint references in frontend code

### 🔄 Next Steps (Recommended)

1. **Test the Updated Frontend**
   ```bash
   cd web
   npm run dev
   ```
   - Navigate to analytics page
   - Verify data loads correctly
   - Check for any console errors

2. **Optional: Add Migration Logging**
   ```typescript
   // In analytics page component
   useEffect(() => {
     console.log('Using new analytics endpoint v2');
   }, []);
   ```

3. **Monitor for Issues**
   - Watch for any data discrepancies
   - Use `compareAnalytics()` function if needed for debugging

4. **Future Cleanup (After Validation)**
   - Remove `getAnalyticsLegacy()` function
   - Remove `compareAnalytics()` function (if not needed)
   - Remove legacy endpoint from backend

## API Comparison

### Legacy Endpoint (`/api/analytics`)
```typescript
// Uses hybrid data sources (JSON + MongoDB)
// May have data inconsistencies
// Supports includeHistory parameter
getAnalyticsLegacy({
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  includeHistory: true // Only in legacy
})
```

### New Endpoint (`/api/analytics-v2`)
```typescript
// Uses single source of truth (ExecutionRepository)
// Guaranteed data consistency
// Real-time data
getAnalytics({
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  detailed: true
})
```

## Troubleshooting

### If Data Looks Different
1. Use the comparison endpoint:
   ```typescript
   const comparison = await compareAnalytics({
     startDate: '2024-01-01',
     endDate: '2024-01-31'
   });
   console.log('Data comparison:', comparison);
   ```

2. Check the `dataSource` field in response:
   ```typescript
   const analytics = await getAnalytics();
   console.log('Data source:', analytics.dataSource); // Should be "ExecutionRepository"
   ```

### If Errors Occur
1. Verify backend is running with new endpoints
2. Check network tab for 404 errors on `/api/analytics-v2`
3. Temporarily switch back to legacy:
   ```typescript
   // Temporary fallback
   const analytics = await getAnalyticsLegacy(params);
   ```

## Benefits of Migration

1. **Data Consistency**: Single source of truth eliminates discrepancies
2. **Real-time Data**: No more stale cached statistics
3. **Better Performance**: Optimized MongoDB queries
4. **Future-proof**: Aligned with new architecture
5. **Debugging**: Clear data source indication

## Timeline

- **Phase 1** ✅: Frontend updated to use new endpoint
- **Phase 2** (1-2 weeks): Monitor and validate
- **Phase 3** (Future): Remove legacy functions and endpoint

---

**Note**: The legacy endpoint `/api/analytics` is still available but deprecated. It will be removed in a future release after validation period.