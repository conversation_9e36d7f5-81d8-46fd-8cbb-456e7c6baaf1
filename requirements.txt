# Web framework and API dependencies
fastapi>=0.109.0
uvicorn
websockets>=13.0.0,<15.1.0
python-multipart
# pydantic is now a dependency of fastapi and beanie, managed automatically
requests>=2.31.0

# Environment and configuration
python-dotenv
logfire[fastapi,httpx]>=0.7.0

# Development and testing tools
rich>=13.0.0


# Dependencias de browser-use local (actualizadas v0.5.4)
aiofiles>=24.1.0
aiohttp>=3.9.0
anyio>=4.9.0
bubus>=1.4.5
httpx>=0.28.1
markdownify==1.1.0
portalocker>=2.7.0,<3.0.0
posthog>=3.7.0
mem0ai>=0.1.106
patchright>=1.52.5
playwright>=1.52.0
psutil>=7.0.0
pydantic>=2.11.5
pyobjc>=11.0; platform_system == 'darwin'
pyperclip>=1.9.0
python-dotenv>=1.0.1
requests>=2.32.3
screeninfo>=0.8.1; platform_system != 'darwin'
typing-extensions>=4.12.2
uuid7>=0.1.0
authlib>=1.6.0
google-genai>=1.21.1
openai>=1.81.0
anthropic>=0.54.0
groq>=0.28.0
ollama>=0.5.1
google-api-python-client>=2.174.0
google-auth>=2.40.3
google-auth-oauthlib>=1.2.2
mcp>=1.10.1
pypdf>=5.7.0
markdown-pdf>=1.7
opencv-python>=*********
Pillow>=10.0.0

# Dependencias memory
chromadb
sentence-transformers>=4.0.2

# Dependencias CLI
click>=8.1.8
textual>=3.2.0

# AI and language model dependencies
langchain
langchain-google-genai
langchain-openai
langchain-anthropic
langchain-groq
langchain-deepseek
agno


# Data processing and utilities
numpy<2.0
pandas
tabulate
protobuf>=4.20.3
google-api-python-client

# Storage backends and background jobs
redis>=5.0.0
celery[redis]>=5.3.4
kombu>=5.3.4

# Optional: Background job monitoring
flower>=2.0.1

# PyTorch - CPU only version for lightweight installation
# Note: Install separately with: pip install torch --index-url https://download.pytorch.org/whl/cpu
torch>=2.3.0

# QAK MongoDB Persistence Dependencies
motor>=3.3.0               # Async MongoDB driver
beanie>=1.24.0             # Async ODM for MongoDB with Pydantic
pydantic-settings>=2.1.0   # Settings management for Pydantic

aioboto3