version: '3.8'

services:
  # Redis - Broker para Celery y almacenamiento de cache
  redis:
    image: redis:7-alpine
    container_name: qak-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis-local.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - qak-network

  # QAK API - Servicio principal FastAPI
  qak-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: qak-api
    ports:
      - "8000:8000"
    depends_on:
      redis:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Override Redis URL para usar el servicio interno
      - REDIS_URL=redis://redis:6379/0
      # API Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      # Storage backend
      - STORAGE_BACKEND=redis
      # Background jobs
      - USE_BACKGROUND_JOBS=true
      # Browser Use - OpenAI GPT-4.1-mini configuration
      - BROWSER_USE_LLM_MODEL=openai/gpt-4.1-mini
      - BROWSER_USE_MODEL_PROVIDER=openrouter
      - BROWSER_USE_MODEL_NAME=openai/gpt-4.1-mini
      - BROWSER_USE_OPENAI_RPM=30
      - BROWSER_USE_OPENAI_TPM=2000000
      # AI Analysis - OpenAI GPT-4.1-mini configuration
      - AI_ANALYSIS_MODEL=openai/gpt-4.1-mini
      - AI_ANALYSIS_PROVIDER=openrouter
      - AI_ANALYSIS_USE_OPENROUTER=true
      - AI_ANALYSIS_RPM=30
      - AI_ANALYSIS_TPM=2000000
      # Browser Use Cloud Sync (apunta a la API interna)
      - BROWSER_USE_CLOUD_SYNC=true
      - BROWSER_USE_CLOUD_API_URL=http://qak-api:8000
      - BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui
      # Browser Use - OpenAI GPT-4.1-mini configuration
      - BROWSER_USE_LLM_MODEL=openai/gpt-4.1-mini
      - BROWSER_USE_MODEL_PROVIDER=openrouter
      - BROWSER_USE_MODEL_NAME=openai/gpt-4.1-mini
      - BROWSER_USE_OPENAI_RPM=30
      - BROWSER_USE_OPENAI_TPM=2000000
      # AI Analysis - OpenAI GPT-4.1-mini configuration
      - AI_ANALYSIS_MODEL=openai/gpt-4.1-mini
      - AI_ANALYSIS_PROVIDER=openrouter
      - AI_ANALYSIS_USE_OPENROUTER=true
      - AI_ANALYSIS_RPM=30
      - AI_ANALYSIS_TPM=2000000
    volumes:
      # Persistir datos importantes
      - ./data/conversations:/app/conversations
      - ./data/codegen_sessions:/app/codegen_sessions
      - ./data/projects:/app/projects
      - ./data/semantic_memories:/app/semantic_memories
      - ./data/monitoring_data:/app/monitoring_data
      - ./data/logs:/app/logs
      - ./data/screenshots:/app/screenshots
      - ./data/exports:/app/exports
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - qak-network

  # Celery Worker - Procesamiento de tareas en background
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: qak-celery-worker
    depends_on:
      redis:
        condition: service_healthy
      qak-api:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Override Redis URL para usar el servicio interno
      - REDIS_URL=redis://redis:6379/0
      # Worker specific settings
      - CELERY_LOG_LEVEL=INFO
      - C_FORCE_ROOT=1
      # Storage backend
      - STORAGE_BACKEND=redis
      # Background jobs
      - USE_BACKGROUND_JOBS=true
    volumes:
      # Compartir volúmenes con la API para acceso a archivos
      - ./data/conversations:/app/conversations
      - ./data/codegen_sessions:/app/codegen_sessions
      - ./data/projects:/app/projects
      - ./data/semantic_memories:/app/semantic_memories
      - ./data/monitoring_data:/app/monitoring_data
      - ./data/logs:/app/logs
      - ./data/screenshots:/app/screenshots
      - ./data/exports:/app/exports
    restart: unless-stopped
    networks:
      - qak-network

  # Flower - Monitor de Celery (opcional, para desarrollo)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: qak-flower
    command: celery -A src.core.background_jobs.celery_app flower --port=5555 --broker=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      redis:
        condition: service_healthy
      celery-worker:
        condition: service_started
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_LOG_LEVEL=INFO
    profiles:
      - monitoring
    restart: unless-stopped
    networks:
      - qak-network

volumes:
  redis_data:
    driver: local

networks:
  qak-network:
    driver: bridge
    name: qak-network