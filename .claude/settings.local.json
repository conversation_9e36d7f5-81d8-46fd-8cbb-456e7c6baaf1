{"permissions": {"allow": ["Bash(rg:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(python3:*)", "Bash(grep:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(python:*)", "Bash(brew services:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(source:*)", "Bash(npm run:*)", "Bash(rm:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(pip install:*)", "Bash(redis-cli:*)", "Bash(./scripts/start_qak_local.sh:*)", "Bash(./scripts/stop_all_qak.sh:*)", "<PERSON><PERSON>(sed:*)", "Bash(git add:*)", "<PERSON><PERSON>(env)", "Bash(npm info:*)", "<PERSON><PERSON>(npx task-master-ai:*)", "Bash(node:*)", "Bash(npm install:*)", "<PERSON><PERSON>(task-master-ai:*)", "<PERSON><PERSON>(task-master:*)", "<PERSON><PERSON>(timeout:*)", "Bash(pgrep:*)", "Bash(kill:*)", "<PERSON><PERSON>(true)"], "deny": []}}