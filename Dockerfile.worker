# Dockerfile optimizado para QAK Celery Worker
FROM python:3.11-slim AS worker

WORKDIR /app

# Install system dependencies for browser automation
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    build-essential \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir --prefer-binary -r requirements.txt

# Install Playwright and browsers (needed for browser automation tasks)
RUN playwright install --with-deps chromium

# Copy backend source code
COPY . ./

# Remove web directory if exists
RUN rm -rf web/

# Create necessary directories
RUN mkdir -p /app/logs /app/monitoring_data /app/conversations /app/codegen_sessions /app/projects /app/semantic_memories

# Set default environment variables optimized for worker
ENV OPENROUTER_API_KEY="" \
    LLM_MODEL="google/gemini-2.0-flash-exp" \
    PROMPT_LANGUAGE="es" \
    QAK_API_CALL_DELAY="10" \
    REDIS_URL="redis://redis:6379/0" \
    STORAGE_BACKEND="redis" \
    BROWSER_USE_LLM_MODEL="openai/gpt-4.1-mini" \
    BROWSER_USE_MODEL_PROVIDER="openrouter" \
    BROWSER_USE_MODEL_NAME="openai/gpt-4.1-mini" \
    BROWSER_USE_RATE_LIMIT_AGGRESSIVE="false" \
    BROWSER_USE_OPENAI_RPM="30" \
    BROWSER_USE_OPENAI_TPM="2000000" \
    AI_ANALYSIS_MODEL="openai/gpt-4.1-mini" \
    AI_ANALYSIS_PROVIDER="openrouter" \
    AI_ANALYSIS_USE_OPENROUTER="true" \
    AI_ANALYSIS_RPM="30" \
    AI_ANALYSIS_TPM="2000000" \
    EMBEDDING_PROVIDER="huggingface" \
    EMBEDDING_MODEL="all-MiniLM-L6-v2" \
    EMBEDDING_DIMS="384" \
    BROWSER_USE_PLANNER_INTERVAL="3" \
    BROWSER_USE_PLANNER_REASONING="true" \
    BROWSER_USE_MAX_CONTEXT_TOKENS="120000" \
    BROWSER_USE_VISION_QUALITY="medium" \
    GEMINI_MAIN_MODEL="gemini-2.5-flash" \
    GEMINI_PLANNER_MODEL="gemini-2.5-flash" \
    GEMINI_EMBEDDING_MODEL="models/text-embedding-004" \
    SAVE_SMOKE_TEST_CONVERSATIONS="true" \
    SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests" \
    BROWSER_USE_SEMANTIC_MEMORY="false" \
    BROWSER_USE_LOGFIRE="true" \
    DISABLE_LLM_VALIDATION="true" \
    MONGODB_ENVIRONMENT="production" \
    QAK_USE_DATABASE="true" \
    QAK_DATABASE_PROJECTS="true" \
    ARTIFACT_STORAGE_BACKEND="r2" \
    ARTIFACT_AUTO_MIGRATE="true" \
    ARTIFACT_MIGRATE_AFTER_MINUTES="30" \
    ARTIFACT_RETENTION_DAYS="90" \
    ARTIFACT_MAX_STORAGE_GB="100" \
    ARTIFACT_AUTO_COMPRESS="true" \
    ARTIFACT_GENERATE_THUMBNAILS="true" \
    ARTIFACT_MAX_CONCURRENT_UPLOADS="5" \
    ARTIFACT_REMOVE_LOCAL_AFTER_MIGRATION="true" \
    ARTIFACT_R2_ONLY_MODE="true" \
    ARTIFACT_SKIP_LOCAL_STORAGE="true" \
    AI_ANALYSIS_COST_OPTIMIZATION="high" \
    USE_BACKGROUND_JOBS="true" \
    CELERY_LOG_LEVEL="INFO" \
    C_FORCE_ROOT="1"

# Health check for worker
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=3 \
    CMD celery -A src.core.background_jobs.celery_app inspect ping || exit 1

# Start Celery worker
CMD ["python", "celery_worker.py"]