import { API_BASE_URL } from '@/lib/config';
import type {
  ApiHealth,
  ApiResponse,
  CodegenExecutionInfo,
  CodegenExecutionListResponse,
  CodegenExecutionRequest,
  CodegenExecutionResponse,
  CodegenHealthResponse,
  CodegenHistoryResponse,
  CodegenHistorySessionDetailResponse,
  CodegenSessionInfo,
  CodegenSessionListResponse,
  CodegenStatsResponse,
  CodegenTestCaseRequest,
  EnhanceUserStoryInput, EnhanceUserStoryOutput,
  Environment, EnvironmentCreateInput, EnvironmentUpdateInput,
  ExecuteSmokeTestOutput,
  ExecutionRequest,
  ExecutionResponse as ExecutionResponseV2,
  GenerateCodeInput, GenerateCodeOutput,
  GenerateGherkinInput, GenerateGherkinOutput,
  GenerateManualTestCasesInput, GenerateManualTestCasesOutput,
  PlaywrightCodegenRequest,
  Project, ProjectCreateInput, ProjectUpdateInput,
  StandardResult,
  SummarizeTestResultsInput, SummarizeTestResultsOutput,
  TestCase, TestCaseCreateInput,
  TestCaseStatusUpdateInput,
  TestCaseUpdateInput,
  TestExecutionHistoryData,
  TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput,
  V2ExecutionResponse,
  VncDependenciesCheck,
  VncSessionInfo
} from '@/lib/types';


async function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true', // 🔧 Agregar header para evitar bloqueo de ngrok
      ...(options?.headers),
    },
  });

  if (!response.ok) {
    let errorData;
    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.details || errorMessage;
      } else {
        const errorText = await response.text();
        // If HTML error page, include a snippet.
        if (errorText.toLowerCase().includes("<!doctype html")) {
          errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;
        } else {
          errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
        }
      }
    } catch (e) {
      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.
      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;
    }
    throw new Error(errorMessage);
  }
  // Handle cases where response is OK but content might be empty for 204 No Content
  if (response.status === 204) {
    return {} as T; // Or handle as appropriate for your application
  }
  return response.json();
}

// Project Endpoints
export const getProjects = (): Promise<ApiResponse<Project>> => fetchApi<ApiResponse<Project>>('/projects/');
export const createProject = (data: ProjectCreateInput): Promise<Project> => fetchApi<Project>('/projects/', { method: 'POST', body: JSON.stringify(data) });
export const getProjectById = (projectId: string): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`);
export const updateProject = (projectId: string, data: ProjectUpdateInput): Promise<Project> => fetchApi<Project>(`/projects/${projectId}`, { method: 'PUT', body: JSON.stringify(data) });
export const deleteProject = (projectId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}`, { method: 'DELETE' });

// Test Suite Endpoints
export const getSuitesByProjectId = (projectId: string): Promise<ApiResponse<TestSuite>> => fetchApi<ApiResponse<TestSuite>>(`/projects/${projectId}/suites`);
export const createSuite = (projectId: string, data: TestSuiteCreateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/`, { method: 'POST', body: JSON.stringify(data) });
export const getSuiteById = (projectId: string, suiteId: string): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`);
export const updateSuite = (projectId: string, suiteId: string, data: TestSuiteUpdateInput): Promise<TestSuite> => fetchApi<TestSuite>(`/projects/${projectId}/suites/${suiteId}`, { method: 'PUT', body: JSON.stringify(data) });
export const deleteSuite = (projectId: string, suiteId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}`, { method: 'DELETE' });

// Test Case Endpoints
export const getTestCasesBySuiteId = (projectId: string, suiteId: string): Promise<ApiResponse<TestCase>> => fetchApi<ApiResponse<TestCase>>(`/projects/${projectId}/suites/${suiteId}/tests`);
export const createTestCase = (projectId: string, suiteId: string, data: TestCaseCreateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/`, { method: 'POST', body: JSON.stringify(data) });
export const getTestCaseById = (projectId: string, suiteId: string, testId: string): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);
export const updateTestCase = (projectId: string, suiteId: string, testId: string, data: TestCaseUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'PUT', body: JSON.stringify(data) });
export const updateTestCaseStatus = (projectId: string, suiteId: string, testId: string, data: TestCaseStatusUpdateInput): Promise<TestCase> => fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/status`, { method: 'PATCH', body: JSON.stringify(data) });
export const deleteTestCase = (projectId: string, suiteId: string, testId: string): Promise<void> => fetchApi<void>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'DELETE' });

// V2 Test Execution Endpoint
export const executeTest = (request: ExecutionRequest): Promise<ExecutionResponseV2> =>
  fetchApi<ExecutionResponseV2>('/v2/tests/execute', {
    method: 'POST',
    body: JSON.stringify(request),
  });

// API Health
export const getApiHealth = (): Promise<ApiHealth> => fetchApi<ApiHealth>('/health');

export const getTestExecutionHistoryDetails = async (historyPath: string): Promise<TestExecutionHistoryData> => {
  // Use the new backend endpoint that processes the history and extracts screenshots
  const response = await fetchApi<TestExecutionHistoryData>(`/history/${encodeURIComponent(historyPath)}`);
  return response;
};


// AI Tool Endpoints

// Uses the API endpoint from OpenAPI spec
export async function callEnhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    user_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/enhance', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
    return {
      enhancedUserStory: result.enhanced_story || result.enhancedUserStory || '',
    };
  } catch (error) {
    throw new Error(`Failed to enhance user story: ${(error as Error).message}`);
  }
}

export async function callGenerateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    enhanced_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/generate-manual-tests', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });    // Convert API response to our expected output format
    let manualTestCases: (string | any)[] = [];

    // Handle different response formats from the API
    if (result && result.manual_tests) {
      if (Array.isArray(result.manual_tests)) {
        // Check if it's an array of objects (new format) or strings (old format)
        if (result.manual_tests.length > 0 && typeof result.manual_tests[0] === 'object') {
          // New format: array of test case objects - keep as objects
          manualTestCases = result.manual_tests;
        } else {
          // Old format: direct array of strings
          manualTestCases = result.manual_tests;
        }
      } else if (typeof result.manual_tests === 'string') {
        // String response - might be JSON or plain text
        try {
          // Try parsing as JSON
          const parsed = JSON.parse(result.manual_tests);
          if (Array.isArray(parsed)) {
            if (parsed.length > 0 && typeof parsed[0] === 'object') {
              // Array of objects - keep as objects
              manualTestCases = parsed;
            } else {
              // Array of strings
              manualTestCases = parsed;
            }
          } else {
            // Create a single item array if it's an object
            manualTestCases = [parsed];
          }
        } catch (e) {
          // If parsing fails, split lines
          manualTestCases = result.manual_tests.split('\n').filter((line: string) => line.trim().length > 0);
        }
      }
    } else if (result && Array.isArray(result.manualTestCases)) {
      // Alternative field name
      manualTestCases = result.manualTestCases;
    } else if (result && typeof result === 'object') {
      // Try to use the result directly
      manualTestCases = [result];
    }

    return { manualTestCases };
  } catch (error) {
    console.error("Manual test generation error:", error);
    throw new Error(`Failed to generate manual test cases: ${(error as Error).message}`);
  }
}

/** @deprecated Use executeTest with Gherkin-based execution instead. */
export async function callGenerateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {
  // For generating Gherkin from manual test cases
  if (input.instructions) {
    try {
      // Using the documented endpoint for generating Gherkin from manual tests
      const apiInput = {
        manual_tests: Array.isArray(input.instructions)
          ? input.instructions.join('\n')
          : input.instructions,
        language: input.language
      };

      const result = await fetchApi<any>('/stories/generate-gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
  // For generating Gherkin directly from instructions and URL
  else {
    try {
      // Using the documented endpoint for generating Gherkin directly
      const apiInput = {
        instructions: input.userStory || '',
        url: input.url || '',
        user_story: input.userStory || '',
        language: input.language
      };

      const result = await fetchApi<any>('/generate/gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
}


// Uses the API endpoint from OpenAPI spec for code generation
export async function callGenerateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {
  try {
    const apiInput = {
      framework: input.framework,
      gherkin_scenario: input.gherkin_scenario,
      test_history: input.test_history || {}
    };

    const result = await fetchApi<any>('/generate/code', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      code: result.code || '',
    };
  } catch (error) {
    let errorMessage = `Failed to generate code: ${(error as Error).message}`;
    throw new Error(errorMessage);
  }
}

// Function to save test history to a project
export async function saveTestHistory(input: {
  projectId: string,
  suiteId: string,
  name: string,
  description: string,
  gherkin: string,
  testHistory: any
}): Promise<any> {
  // Convert our input to match the API schema
  const apiInput = {
    project_id: input.projectId,
    suite_id: input.suiteId,
    name: input.name,
    description: input.description,
    gherkin: input.gherkin,
    test_history: input.testHistory
  };

  try {
    return await fetchApi<any>('/projects/save-history', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
  } catch (error) {
    throw new Error(`Failed to save test history: ${(error as Error).message}`);
  }
}

// Uses the API endpoint for summarizing test results
export async function callSummarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    test_results: input.testResults
  };

  try {
    const result = await fetchApi<any>('/tests/summarize', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      summary: result.summary || '',
    };
  } catch (error) {
    throw new Error(`Failed to summarize test results: ${(error as Error).message}`);
  }
}

// Prompt Management API Functions
export async function fetchPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/');
  } catch (error) {
    throw new Error(`Failed to fetch prompts: ${(error as Error).message}`);
  }
}

export async function fetchPromptDetail(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`);
  } catch (error) {
    throw new Error(`Failed to fetch prompt details: ${(error as Error).message}`);
  }
}

export async function updatePrompt(
  category: string,
  promptId: string,
  data: {
    content: Record<string, string>;
    metadata?: Record<string, any>;
    commit_message?: string;
  }
): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to update prompt: ${(error as Error).message}`);
  }
}

export async function createPrompt(data: {
  category: string;
  prompt_id: string;
  name: string;
  description: string;
  languages?: string[];
  content: Record<string, string>;
  metadata?: Record<string, any>;
}): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to create prompt: ${(error as Error).message}`);
  }
}

export async function deletePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'DELETE',
    });
  } catch (error) {
    throw new Error(`Failed to delete prompt: ${(error as Error).message}`);
  }
}

export async function validatePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}/validate`, {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate prompt: ${(error as Error).message}`);
  }
}

export async function validateAllPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/validate-all', {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate all prompts: ${(error as Error).message}`);
  }
}

// === CONFIGURATION API ENDPOINTS ===

// Get all configurations (predefined + custom)
export const getAllConfigurations = (): Promise<{ predefined: any[], custom: any[] }> => fetchApi<{ predefined: any[], custom: any[] }>('/config/');

// Get all predefined configurations
export const getPredefinedConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/predefined');

// Get specific predefined configuration
export const getPredefinedConfiguration = (configType: string): Promise<any> => fetchApi<any>(`/config/predefined/${configType}`);

// Get all custom configurations
export const getCustomConfigurations = (): Promise<any[]> => fetchApi<any[]>('/config/custom');

// Get specific custom configuration
export const getCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`);

// Create custom configuration
export const createCustomConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/custom', {
  method: 'POST',
  body: JSON.stringify(data)
});

// Update custom configuration
export const updateCustomConfiguration = (configId: string, data: any): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, {
  method: 'PUT',
  body: JSON.stringify(data)
});

// Delete custom configuration
export const deleteCustomConfiguration = (configId: string): Promise<any> => fetchApi<any>(`/config/custom/${configId}`, {
  method: 'DELETE'
});

// Validate configuration
export const validateConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/validate', {
  method: 'POST',
  body: JSON.stringify(data)
});

// Test configuration
export const testConfiguration = (data: any): Promise<any> => fetchApi<any>('/config/test', {
  method: 'POST',
  body: JSON.stringify(data)
});

// Get configurations by execution type
export const getConfigurationsByExecutionType = (executionType: string): Promise<any[]> => fetchApi<any[]>(`/config/mongodb/configurations/execution-type/${executionType}`);

// Update configuration execution types
export const updateConfigurationExecutionTypes = (configId: string, executionTypes: string[]): Promise<any> => fetchApi<any>(`/config/mongodb/configurations/${configId}/execution-types`, {
  method: 'PUT',
  body: JSON.stringify({ execution_types: executionTypes })
});

// Get environment defaults
export const getEnvironmentDefaults = (): Promise<any> => fetchApi<any>('/config/defaults');

// Update predefined configuration
export const updatePredefinedConfiguration = (configType: string, data: any): Promise<any> => fetchApi<any>(`/config/predefined/${configType}`, {
  method: 'PUT',
  body: JSON.stringify(data)
});

// Validate execution types for conflicts
export const validateExecutionTypes = (data: {
  config_id?: string;
  execution_types: string[];
  exclude_config_id?: string;
}): Promise<{
  has_conflicts: boolean;
  conflicts: Array<{
    config_id: string;
    config_name: string;
    config_type: string;
    conflicting_execution_types: string[];
    is_predefined: boolean;
  }>;
  suggested_action: string;
}> => fetchApi('/config/validate-execution-types', {
  method: 'POST',
  body: JSON.stringify(data)
});

// Resolve execution type conflicts
export const resolveExecutionTypeConflict = (data: {
  action: 'remove_from_existing' | 'cancel';
  target_config?: any;
  conflicts: any[];
  execution_types: string[];
}): Promise<{
  success: boolean;
  message: string;
  updated_configs?: Array<{
    config_id: string;
    name: string;
    removed_types: string[];
  }>;
}> => fetchApi('/config/resolve-execution-type-conflict', {
  method: 'POST',
  body: JSON.stringify(data)
});

// ==========================================
// CodeGen API Functions
// ==========================================

// Start a new codegen session
export const startCodegenSession = (data: PlaywrightCodegenRequest): Promise<CodegenSessionInfo> =>
  fetchApi<CodegenSessionInfo>('/codegen/start', {
    method: 'POST',
    body: JSON.stringify(data)
  });

// Get session status
export const getCodegenSession = (sessionId: string): Promise<CodegenSessionInfo> =>
  fetchApi<CodegenSessionInfo>(`/codegen/session/${sessionId}`);

// Stop session
export const stopCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> =>
  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}/stop`, {
    method: 'POST'
  });

// Get generated code
export const getCodegenGeneratedCode = (sessionId: string): Promise<{
  session_id: string;
  status: string;
  generated_code?: string;
  target_language: string;
  created_at: string;
  updated_at: string;
}> => fetchApi(`/codegen/session/${sessionId}/code`);

// Convert to test case
export const convertCodegenToTestcase = (data: CodegenTestCaseRequest): Promise<Record<string, any>> =>
  fetchApi<Record<string, any>>(`/codegen/session/${data.session_id}/convert`, {
    method: 'POST',
    body: JSON.stringify(data)
  });

// Cleanup session
export const cleanupCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> =>
  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}`, {
    method: 'DELETE'
  });

// List active sessions
export const listCodegenSessions = (): Promise<CodegenSessionListResponse> =>
  fetchApi<CodegenSessionListResponse>('/codegen/sessions');

// Get statistics
export const getCodegenStats = (): Promise<CodegenStatsResponse> =>
  fetchApi<CodegenStatsResponse>('/codegen/stats');

// Bulk cleanup
export const bulkCleanupCodegenSessions = (): Promise<{
  message: string;
  sessions_cleaned: number;
  sessions_remaining: number;
}> => fetchApi('/codegen/bulk-cleanup', { method: 'POST' });

// Health check
export const getCodegenHealth = (): Promise<CodegenHealthResponse> =>
  fetchApi<CodegenHealthResponse>('/codegen/health');

// History endpoints
export const getCodegenHistory = (limit?: number): Promise<CodegenHistoryResponse> =>
  fetchApi<CodegenHistoryResponse>(`/codegen/history${limit ? `?limit=${limit}` : ''}`);

export const getCodegenHistorySession = (sessionId: string): Promise<CodegenHistorySessionDetailResponse> =>
  fetchApi<CodegenHistorySessionDetailResponse>(`/codegen/history/${sessionId}`);

// Execution endpoints
export const executeCodegenTest = (data: CodegenExecutionRequest): Promise<CodegenExecutionResponse> =>
  fetchApi<CodegenExecutionResponse>('/codegen/execute', {
    method: 'POST',
    body: JSON.stringify(data)
  });

export const getCodegenExecution = (executionId: string): Promise<CodegenExecutionInfo> =>
  fetchApi<CodegenExecutionInfo>(`/codegen/execution/${executionId}`);

export const listCodegenExecutions = (): Promise<CodegenExecutionListResponse> =>
  fetchApi<CodegenExecutionListResponse>('/codegen/executions');

export const stopCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> =>
  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}/stop`, {
    method: 'POST'
  });

export const cleanupCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> =>
  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}`, {
    method: 'DELETE'
  });

// VNC Support for Remote CodeGen Access
export const getVncDependencies = (): Promise<VncDependenciesCheck> =>
  fetchApi<VncDependenciesCheck>('/codegen/vnc-dependencies');

export const listVncSessions = (): Promise<{
  vnc_enabled: boolean;
  total_sessions: number;
  sessions: VncSessionInfo[];
  message?: string;
}> => fetchApi('/codegen/vnc-sessions');

export const getVncSessionInfo = (sessionId: string): Promise<VncSessionInfo> =>
  fetchApi<VncSessionInfo>(`/codegen/vnc-session/${sessionId}`);

export const stopVncSession = (sessionId: string): Promise<{ message: string; session_id: string }> =>
  fetchApi<{ message: string; session_id: string }>(`/codegen/vnc-session/${sessionId}/stop`, {
    method: 'POST'
  });

// Analytics Endpoint
export type AnalyticsResponse = {
  totalTests: number;
  testsRun: number;
  passRate: number; // 0-1
  avgDuration: number; // seconds
  dailyExecutions: Array<{
    date: string;
    passed: number;
    failed: number;
    total: number;
  }>;
  suiteStats: Array<{
    suiteId: string;
    suiteName: string;
    // Lifetime stats from summary
    lifetimeTests: number;
    lifetimeRuns: number;
    lifetimePassed: number;
    lifetimeFailed: number;
    lifetimeSuccessRate: number; // 0-1
    lastExecutionAt: string | null;
    // Date-range specific stats
    runsInDateRange: number;
    passedInDateRange: number;
  }>;
  testDetails?: Array<{
    testId: string;
    testName: string;
    suiteName: string;
    suiteId: string;
    projectId: string;
    status: string;
    lastExecution: string;
    duration?: number; // seconds - new field from v2
  }>;
  // New fields from analytics-v2 endpoint
  dataSource?: string; // Indicates data source ("ExecutionRepository")
  dateRange?: {
    start: string;
    end: string;
  };
};

/**
 * Get analytics data using the new improved endpoint.
 * Uses ExecutionRepository as single source of truth for consistent data.
 */
export const getAnalytics = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
  detailed?: boolean;
}): Promise<AnalyticsResponse> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
    if (params.detailed) query.append('detailed', 'true');
  }
  const qs = query.toString();
  return fetchApi<AnalyticsResponse>(`/analytics-v2${qs ? `?${qs}` : ''}`);
};

/**
 * @deprecated Use getAnalytics() instead. This function uses the legacy endpoint.
 * Legacy analytics function - will be removed in future versions.
 */
export const getAnalyticsLegacy = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
  detailed?: boolean;
  includeHistory?: boolean;
}): Promise<AnalyticsResponse> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
    if (params.detailed) query.append('detailed', 'true');
    if (params.includeHistory) query.append('include_history', 'true');
  }
  const qs = query.toString();
  return fetchApi<AnalyticsResponse>(`/analytics${qs ? `?${qs}` : ''}`);
};

/**
 * Compare analytics data between legacy and new systems.
 * Useful for debugging and validation during migration.
 */
export const compareAnalytics = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
}): Promise<any> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
  }
  const qs = query.toString();
  return fetchApi<any>(`/analytics-comparison${qs ? `?${qs}` : ''}`);
};

// V2 API Functions - Support for rich data extraction

export interface V2SmokeTestRequest {
  type: "smoke";
  url: string;
  instructions?: string;
  config_profile?: string;
  max_steps?: number;
}

export interface V2FullTestRequest {
  type: "full";
  url: string;
  gherkin_scenarios: string[];
  instructions?: string;
  config_profile?: string;
}

export interface V2TestCaseRequest {
  type: "case";
  test_id: string;
  project_id?: string;
  suite_id?: string;
  execution_times?: number;
  config_profile?: string;
}

export interface V2SuiteRequest {
  type: "suite";
  suite_id: string;
  project_id?: string;
  parallel?: boolean;
  delay_between_tests?: number;
  config_profile?: string;
}

export type V2ExecutionRequest = V2SmokeTestRequest | V2FullTestRequest | V2TestCaseRequest | V2SuiteRequest;

/**
 * Execute test using V2 API endpoint with rich data extraction support
 */
export async function executeTestV2(request: V2ExecutionRequest): Promise<V2ExecutionResponse> {
  console.log("🌐 executeTestV2 called with request:", request);

  try {
    console.log("📡 Making fetch call to /api/v2/tests/execute...");
    const response = await fetchApi<V2ExecutionResponse>('/v2/tests/execute', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log("📨 Raw V2 API response:", response);

    return response;

  } catch (error) {
    console.error("💥 executeTestV2 fetch failed:", error);
    throw new Error(`Failed to execute test via V2 API: ${(error as Error).message}`);
  }
}

/**
 * Execute smoke test using V2 API - returns rich StandardResult format
 */
export async function executeSmokeTestV2(
  url: string,
  instructions?: string,
  configProfile: string = "fast"
): Promise<V2ExecutionResponse> {
  const request: V2SmokeTestRequest = {
    type: "smoke",
    url,
    instructions,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Execute test case using V2 API - returns rich StandardResult format
 */
export async function executeTestCaseV2(
  testId: string,
  projectId?: string,
  suiteId?: string,
  executionTimes: number = 1,
  configProfile: string = "balanced"
): Promise<V2ExecutionResponse> {
  const request: V2TestCaseRequest = {
    type: "case",
    test_id: testId,
    project_id: projectId,
    suite_id: suiteId,
    execution_times: executionTimes,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Execute test suite using V2 API - returns rich StandardResult format
 */
export async function executeTestSuiteV2(
  suiteId: string,
  projectId?: string,
  parallel: boolean = false,
  delayBetweenTests: number = 0.5,
  configProfile: string = "balanced"
): Promise<V2ExecutionResponse> {
  const request: V2SuiteRequest = {
    type: "suite",
    suite_id: suiteId,
    project_id: projectId,
    parallel,
    delay_between_tests: delayBetweenTests,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Unified execution function that uses V2 API only (no legacy fallback)
 * Now properly handles async execution pattern where backend returns immediately with RUNNING status
 */
export async function executeTestUnified(
  request: V2ExecutionRequest
): Promise<ExecuteSmokeTestOutput | StandardResult | V2ExecutionResponse> {
  console.log("🚀 executeTestUnified called with request:", request);

  try {
    // Use V2 API only - no fallback to legacy
    const v2Result = await executeTestV2(request);
    console.log("✅ V2 API response received:", v2Result);

    // Check if this is an async execution (immediate return with RUNNING status)
    if (v2Result.success === true && v2Result.status === "running") {
      console.log("🚀 V2 API started async execution, returning V2ExecutionResponse for polling:", v2Result);
      // Return the full V2ExecutionResponse so frontend can start polling
      return v2Result;
    }

    // Check if the execution completed successfully (legacy synchronous mode)
    if (v2Result.success === true && v2Result.status === "success") {
      console.log("🎉 V2 API succeeded synchronously, returning StandardResult:", v2Result.result);
      // Return the embedded result (StandardResult) with execution_id from the response
      const result = v2Result.result || {};
      // Ensure execution_id and status are always present at the top level
      const standardResult = {
        ...result,
        execution_id: v2Result.execution_id,
        status: v2Result.status,
        success: v2Result.success
      };
      return standardResult as StandardResult;
    } else {
      console.error("❌ V2 API returned unsuccessful result:", v2Result);
      // For unsuccessful results, prefer specific error message over generic ones
      const errorMessage = v2Result.error || v2Result.message || "V2 execution failed";
      // To provide more context, serialize the whole result object in the error.
      const detailedError = `${errorMessage} - Full Response: ${JSON.stringify(v2Result)}`;
      throw new Error(detailedError);
    }
  } catch (v2Error) {
    console.error("❌ V2 API failed with error:", v2Error);
    // No fallback - V2 should be fixed instead of using legacy API
    throw new Error(`Test execution failed: ${(v2Error as Error).message}`);
  }
}

// Controls for V2 Executions
export const pauseExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/pause`, { method: 'POST' });

export const resumeExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/resume`, { method: 'POST' });

export const stopExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/stop`, { method: 'POST' });

export const getExecutionById = (executionId: string): Promise<V2ExecutionResponse> =>
  fetchApi(`/v2/tests/execution/${executionId}`);

// Get executions from MongoDB for a specific test case
export const getTestMongoExecutions = async (
  projectId: string,
  suiteId: string,
  testId: string,
  environmentId?: string,
  applicationVersion?: string,
  limit?: number,
  includeAiAnalysis?: boolean,
  summaryOnly?: boolean
): Promise<V2ExecutionResponse[]> => {
  // Construct query parameters
  const params = new URLSearchParams();
  if (environmentId) {
    params.append('environment_id', environmentId);
  }
  if (applicationVersion) {
    params.append('application_version', applicationVersion);
  }
  if (limit !== undefined) {
    params.append('limit', limit.toString());
  }
  if (includeAiAnalysis !== undefined) {
    params.append('include_ai_analysis', includeAiAnalysis.toString());
  }
  if (summaryOnly !== undefined) {
    params.append('summary_only', summaryOnly.toString());
  }
  const queryString = params.toString();

  // The backend returns an object { executions: [...], total_count, ... }
  const resp = await fetchApi<{
    executions: V2ExecutionResponse[];
    total_count: number;
    total_available: number;
    limit: number;
    has_more: boolean;
    summary_only: boolean;
    include_ai_analysis: boolean;
    message?: string;
  }>(
    `/v2/tests/${encodeURIComponent(projectId)}/${encodeURIComponent(suiteId)}/${encodeURIComponent(testId)}/executions${queryString ? `?${queryString}` : ''}`
  );
  return resp.executions ?? [];
};

// Get execution summaries (optimized endpoint)
export const getTestExecutionSummaries = async (
  projectId: string,
  suiteId: string,
  testId: string,
  limit?: number
): Promise<any[]> => {
  // Construct query parameters
  const params = new URLSearchParams();
  if (limit !== undefined) {
    params.append('limit', limit.toString());
  }
  const queryString = params.toString();

  // The backend returns an object { summaries: [...], total_available, ... }
  const resp = await fetchApi<{
    summaries: any[];
    total_available: number;
    limit: number;
    has_more: boolean;
    message?: string;
  }>(
    `/v2/tests/${encodeURIComponent(projectId)}/${encodeURIComponent(suiteId)}/${encodeURIComponent(testId)}/executions/summary${queryString ? `?${queryString}` : ''}`
  );
  return resp.summaries ?? [];
};

// Delete execution
export const deleteExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/executions/${executionId}`, { method: 'DELETE' });

// Test History
export async function getTestExecutionHistory(
  projectId: string,
  suiteId: string,
  testId: string,
  executionId: string
): Promise<TestExecutionHistoryData> {
  const response = await fetchApi<TestExecutionHistoryData>(`/history/${encodeURIComponent(projectId)}/${encodeURIComponent(suiteId)}/${encodeURIComponent(testId)}/${encodeURIComponent(executionId)}`);
  return response;
}

// Environment Management API

export const getProjectEnvironments = async (projectId: string): Promise<Environment[]> => {
  const response = await fetchApi<ApiResponse<Environment>>(`/projects/${encodeURIComponent(projectId)}/environments`);
  return response.items || [];
};

export const getProjectEnvironment = async (projectId: string, envId: string): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`);
};

export const createProjectEnvironment = async (
  projectId: string,
  environment: EnvironmentCreateInput
): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments`, {
    method: 'POST',
    body: JSON.stringify(environment),
  });
};

export const updateProjectEnvironment = async (
  projectId: string,
  envId: string,
  environment: EnvironmentUpdateInput
): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`, {
    method: 'PUT',
    body: JSON.stringify(environment),
  });
};

export const deleteProjectEnvironment = async (projectId: string, envId: string): Promise<{ message: string }> => {
  return fetchApi<{ message: string }>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`, {
    method: 'DELETE',
  });
};

export const setDefaultEnvironment = async (projectId: string, envId: string): Promise<{ message: string }> => {
  return fetchApi<{ message: string }>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}/set-default`, {
    method: 'POST',
  });
};

export const getDefaultEnvironment = async (projectId: string): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/default`);
};

export const getEnvironmentsByTag = async (projectId: string, tag: string): Promise<Environment[]> => {
  const response = await fetchApi<ApiResponse<Environment>>(`/projects/${encodeURIComponent(projectId)}/environments/by-tag/${encodeURIComponent(tag)}`);
  return response.items || [];
};

export const testEnvironmentUrl = async (
  projectId: string,
  envId: string,
  relativePath: string = '/'
): Promise<{
  environment_id: string;
  environment_name: string;
  full_url: string;
  accessible: boolean;
  status_code?: number;
}> => {
  return fetchApi(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}/test-url?relative_path=${encodeURIComponent(relativePath)}`, {
    method: 'POST',
  });
};
