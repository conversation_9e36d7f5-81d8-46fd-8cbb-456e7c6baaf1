# =============================================================================
# .dockerignore para QAK Production (sin Next.js)
# =============================================================================
# Este archivo evita copiar archivos innecesarios a los contenedores Docker

# =============================================================================
# ARCHIVOS DE DESARROLLO Y CONFIGURACIÓN
# =============================================================================

# Git
.git
.gitignore
.gitattributes
.github/

# Docker files (evitar recursión)
Dockerfile*
docker-compose*.yml
.dockerignore*
docker-start.sh
README.docker.md

# Environment files (se pasan via env_file)
.env*
!.env.example

# =============================================================================
# FRONTEND Y WEB (NO NECESARIOS PARA API)
# =============================================================================

# Next.js y frontend completo
web/
package*.json
node_modules/
.next/
out/
build/
dist/

# Static files
static/
public/

# =============================================================================
# DATOS Y ARCHIVOS TEMPORALES
# =============================================================================

# Data directories (se montan como volúmenes)
data/
conversations/
codegen_sessions/
projects/
semantic_memories/
monitoring_data/
logs/
screenshots/
exports/

# Redis data
redis-data/
dump.rdb

# Temporary files
*.tmp
*.temp
*.log
*.pid
*.swp
*.swo
*~

# =============================================================================
# PYTHON Y DESARROLLO
# =============================================================================

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PYTHON_ARGCOMPLETE_OK

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# =============================================================================
# HERRAMIENTAS DE DESARROLLO
# =============================================================================

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.hypothesis/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# CONFIGURACIONES ESPECÍFICAS DE EDITORES/IDEs
# =============================================================================

# Cursor AI
.cursor/

# Claude AI
.claude/

# Windsurf
.windsurf/
.windsurfrules

# Roo
.roo/
.roomodes

# Trae
.trae/

# Cline
.clinerules/

# Taskmaster
.taskmaster/

# Memory bank
memory-bank/

# =============================================================================
# DOCUMENTACIÓN Y ARCHIVOS DE PROYECTO
# =============================================================================

# Documentation
docs/
*.md
!README.md

# Architecture diagrams
architecture*.md

# PRD files
*_prd.txt

# Examples
examples/

# Prompts
prompts/

# Scripts
scripts/

# PlanneryRAG
plannerRAG/

# =============================================================================
# ARCHIVOS DEL SISTEMA OPERATIVO
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# ARCHIVOS DE CONFIGURACIÓN ESPECÍFICOS
# =============================================================================

# Redis config (se monta como volumen)
redis-local.conf

# Install scripts
install_dependencies.sh

# Test files
test_*.py
check_*.py
configure_*.py

# =============================================================================
# ARCHIVOS GRANDES O BINARIOS
# =============================================================================

# Images
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.ico
*.svg

# Videos
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Audio
*.mp3
*.wav
*.flac
*.aac

# Archives
*.zip
*.tar
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Databases
*.db
*.sqlite
*.sqlite3

# =============================================================================
# CONFIGURACIONES DE SERVICIOS EXTERNOS
# =============================================================================

# Docker VNC
Dockerfile.vnc
docker/start-with-vnc.sh
docker/supervisord.conf
docker/nginx.conf

# Config directories
config/

# =============================================================================
# ARCHIVOS ESPECÍFICOS DEL PROYECTO QUE NO SON NECESARIOS
# =============================================================================

# CLI tools
cli.py

# Specific test files
test_config_mongodb_migration.py
test_execution_types.py

# Agent documentation
AGENTS.md
CLAUDE.md
GEMINI.md

# Pyproject (ya incluido en requirements.txt)
pyproject.toml
libs/pyproject.toml