"""
AI-Powered Test Analysis Service

Provides intelligent analysis of test execution results using LLM-powered evaluation
to determine step success, detect issues, and provide accurate test completion assessment.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import io
import base64
from PIL import Image

from src.services.llm.llm_service_factory import get_llm_factory
from src.services.llm.prompt_llm_service import PromptLLMService
from src.models.standard_result import StandardResult, TestStep

logger = logging.getLogger(__name__)


class TestAnalysisService:
    """
    Service for AI-powered analysis of test execution results.
    
    Provides:
    - Individual step validation and issue detection
    - Complete test success/failure determination
    - Quality assessment and recommendations
    """
    
    def __init__(self):
        """Initialize the test analysis service."""
        self.llm_factory = get_llm_factory()
        self.prompt_service = PromptLLMService()
        
        # 💰 Cost optimization settings
        import os
        self.cost_optimization_level = os.getenv("AI_ANALYSIS_COST_OPTIMIZATION", "high").lower()
        # Options: "none", "low", "medium", "high", "aggressive"
        logger.info(f"💰 AI Analysis cost optimization level: {self.cost_optimization_level}")
        
    async def analyze_step(
        self,
        step: TestStep,
        test_context: Dict[str, Any],
        screenshot_data: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze a single test step for issues and validation.
        
        Args:
            step: TestStep object to analyze
            test_context: Context about the overall test (objective, gherkin, etc.)
            screenshot_data: Base64 screenshot data if available
            
        Returns:
            Dict containing step analysis results
        """
        try:
            # Prepare input data for the prompt
            step_data = {
                "step_number": step.step_number,
                "description": step.description,
                "action_type": step.action_type,
                "success": step.success,
                "duration_ms": getattr(step, 'duration_ms', None),
                "error_message": getattr(step, 'error_message', None),
                "metadata": step.metadata or {},
                "screenshot_available": screenshot_data is not None
            }
            
            # Prepare context data
            context_data = {
                "test_objective": test_context.get("test_description", ""),
                "gherkin_scenario": test_context.get("gherkin_scenario", ""),
                "test_instructions": test_context.get("test_instructions", ""),
                "url": test_context.get("url", ""),
            }
            
            # Filter out technical errors that should be ignored
            filtered_error_message = step.error_message
            if step.error_message:
                # Filter out the specific error that should be ignored
                ignore_patterns = [
                    "Failed to extract step info: cannot access local variable 'model_output'",
                    "Could not parse response",
                    "MALFORMED_FUNCTION_CALL",
                    "Failed to parse model output"
                ]
                
                for pattern in ignore_patterns:
                    if pattern in step.error_message:
                        filtered_error_message = None
                        logger.info(f"💡 Filtering out technical error for step {step.step_number}: {pattern}")
                        break
            
            # Create filtered step data for analysis
            filtered_step_data = {
                "step_number": step.step_number,
                "action_type": step.action_type,
                "description": step.description,
                "success": step.success,
                "error_message": filtered_error_message,  # Use filtered error
                "url": step.url,
                "element_info": step.element_info.__dict__ if step.element_info else None,
                "screenshot_url": step.screenshot_url
            }
            
            # Enhanced analysis with AI reasoning and discrepancy detection
            ai_reasoning = ""
            if step.metadata and "thinking" in step.metadata:
                ai_reasoning = step.metadata["thinking"]
            
            # Detect potential discrepancies
            browser_url = step.metadata.get("state", {}).get("url", "") if step.metadata else ""
            discrepancy_note = ""
            if "dashboard" in ai_reasoning.lower() and "login" in browser_url.lower():
                discrepancy_note = "IMPORTANT: AI reasoning mentions dashboard but browser URL shows login page. Trust visual evidence over URL data."
            
            # Create the analysis prompt
            analysis_input = {
                "step_info": filtered_step_data,
                "test_context": context_data,
                "browser_state": step.metadata.get("state", {}) if step.metadata else {},
                "execution_results": step.metadata.get("results", []) if step.metadata else [],
                "ai_reasoning": ai_reasoning,
                "has_screenshot": screenshot_data is not None,
                "discrepancy_alert": discrepancy_note,
                "note": "Ignore technical/internal errors that don't affect the actual test functionality. Focus on user-facing functionality and goal achievement. If there are discrepancies between browser state and visual evidence, trust the visual evidence."
            }
            
            # 💰 SMART SCREENSHOT ANALYSIS: Only use screenshots when really needed
            should_use_screenshot = self._should_analyze_screenshot(step, screenshot_data)
            
            # FORCE screenshot analysis when discrepancies are detected
            if discrepancy_note and screenshot_data:
                should_use_screenshot = True
                logger.info(f"🔍 FORCED screenshot analysis for step {step.step_number}: Discrepancy detected between browser state and AI reasoning")
            
            images = [screenshot_data] if should_use_screenshot and screenshot_data else None
            
            if images:
                logger.info(f"📸 Step {step.step_number}: Sending screenshot to LLM ({len(screenshot_data)} chars) - Visual analysis needed")
            elif screenshot_data:
                logger.info(f"💰 Step {step.step_number}: Screenshot available but skipping for cost optimization - text analysis sufficient")
            else:
                logger.info(f"📸 Step {step.step_number}: No screenshot data to send to LLM")
            
            result = self.prompt_service.execute_versioned_prompt(
                category="test-analysis",
                prompt_id="step-validation",
                variables={
                    "analysis_input": json.dumps(analysis_input, indent=2, ensure_ascii=False)
                },
                use_case="test_analysis",
                language="es",  # Allow Spanish responses while keeping English prompts
                images=images   # Include screenshot if available
            )
            
            # Parse the JSON response
            try:
                if result.get("success"):
                    content = result["content"].strip()
                    if not content:
                        logger.warning(f"Empty response from LLM for step {step.step_number}")
                        return self._create_fallback_step_analysis(step, "empty_response")
                    
                    # Clean markdown formatting if present
                    content = self._clean_json_content(content)
                    
                    # Log the raw content for debugging
                    logger.debug(f"Raw LLM response for step {step.step_number}: {content[:200]}...")
                    
                    analysis_result = json.loads(content)
                else:
                    raise Exception(f"LLM request failed: {result.get('error', 'Unknown error')}")
                
                # Add timestamp and metadata
                analysis_result.update({
                    "analyzed_at": datetime.utcnow().isoformat(),
                    "analyzer_version": "1.0.0",
                    "step_id": f"{step.step_number}_{step.action_type}"
                })
                
                # Enhanced logging with Spanish support
                assessment = analysis_result.get('overall_assessment', 'unknown')
                logger.info(f"✅ Analyzed step {step.step_number}: {assessment}")
                return analysis_result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse step analysis JSON: {e}")
                logger.error(f"Raw content that failed to parse: '{result.get('content', '')}'")
                return self._create_fallback_step_analysis(step, f"json_parse_error: {str(e)[:100]}")
                
        except Exception as e:
            logger.error(f"Error analyzing step {step.step_number}: {e}", exc_info=True)
            return self._create_fallback_step_analysis(step, f"analysis_error: {str(e)[:100]}")
    
    def _should_analyze_screenshot(self, step: TestStep, screenshot_data: Optional[str]) -> bool:
        """
        💰 COST OPTIMIZATION: Determine if screenshot analysis is necessary for this step.
        
        Args:
            step: TestStep object
            screenshot_data: Base64 screenshot data
            
        Returns:
            bool: True if screenshot analysis is needed, False if text analysis is sufficient
        """
        if not screenshot_data:
            return False
        
        # Check if this is a technical error that should be ignored
        has_technical_error = False
        if step.error_message:
            ignore_patterns = [
                "Failed to extract step info: cannot access local variable 'model_output'",
                "Could not parse response",
                "MALFORMED_FUNCTION_CALL",
                "Failed to parse model output"
            ]
            for pattern in ignore_patterns:
                if pattern in step.error_message:
                    has_technical_error = True
                    logger.info(f"💰 Skipping screenshot for step {step.step_number}: Technical error should be ignored")
                    break
        
        if has_technical_error:
            return False
        
        # Cost optimization level controls screenshot usage
        if self.cost_optimization_level == "aggressive":
            # Only use screenshots for failed steps and final validation (but not technical errors)
            return (not step.success and not has_technical_error) or (step.action_type == "done")
        elif self.cost_optimization_level == "high":
            # Skip screenshots for simple successful navigation
            if step.success and step.action_type in {"go_to_url", "unknown"}:
                return False
        elif self.cost_optimization_level == "none":
            # Always use screenshots when available (except for technical errors)
            return not has_technical_error
        
        # Medium/Low optimization: Always analyze screenshots for these critical scenarios:
        
        # 1. Steps that likely require visual validation
        visual_action_types = {"click", "type", "scroll", "navigate", "done"}
        if step.action_type in visual_action_types:
            
            # 2. Steps that failed - visual analysis helps understand why
            if not step.success:
                logger.info(f"💰 Using screenshot for step {step.step_number}: Failed step needs visual analysis")
                return True
            
            # 3. Final "done" step - always validate visually
            if step.action_type == "done":
                logger.info(f"💰 Using screenshot for step {step.step_number}: Final step validation")
                return True
            
            # 4. Login/authentication steps - critical to verify visually
            if any(keyword in step.description.lower() for keyword in ["login", "sign in", "authenticate", "dashboard"]):
                logger.info(f"💰 Using screenshot for step {step.step_number}: Login/auth step needs visual verification")
                return True
            
            # 4. Steps with errors or warnings in metadata
            if step.metadata:
                if step.error_message or any("error" in str(val).lower() for val in step.metadata.values()):
                    logger.info(f"💰 Using screenshot for step {step.step_number}: Error detected")
                    return True
        
        # 5. Skip screenshots for simple navigation or successful intermediate steps
        if step.success and step.action_type in {"go_to_url", "unknown"} and not step.error_message:
            logger.info(f"💰 Skipping screenshot for step {step.step_number}: Simple {step.action_type} step")
            return False
        
        # 6. Default: use screenshot for other cases (conservative approach)
        logger.info(f"💰 Using screenshot for step {step.step_number}: Default visual analysis")
        return True
    
    async def analyze_test_completion(
        self,
        result: StandardResult,
        step_analyses: List[Dict[str, Any]],
        final_screenshot: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze complete test execution to determine final pass/fail status.
        
        Args:
            result: StandardResult object with complete test data
            step_analyses: List of individual step analysis results
            final_screenshot: Optional base64 screenshot of final state
            
        Returns:
            Dict containing test completion analysis
        """
        try:
            # Prepare test execution data
            test_data = {
                "test_id": result.test_id,
                "execution_id": result.execution_id,
                "test_type": result.test_type.value if hasattr(result.test_type, 'value') else str(result.test_type) if result.test_type else "unknown",
                "status": result.status.value if hasattr(result.status, 'value') else str(result.status) if result.status else "unknown",
                "success": result.success,
                "duration": getattr(result, 'execution_time', None),
                "total_steps": len(result.steps),
                "error_count": len(result.errors),
                "message": result.message
            }
            
            # Prepare context from metadata
            context_data = {
                "test_objective": result.metadata.get("test_description", ""),
                "gherkin_scenario": result.metadata.get("gherkin_scenario", ""),
                "test_instructions": result.metadata.get("test_instructions", ""),
                "url": result.metadata.get("url", ""),
                "final_url": result.metadata.get("visited_urls", [])[-1] if result.metadata.get("visited_urls") else ""
            }
            
            # Summarize step results
            step_summary = {
                "total_steps": len(result.steps),
                "successful_steps": sum(1 for s in result.steps if s.success),
                "failed_steps": sum(1 for s in result.steps if not s.success),
                "step_analyses": step_analyses
            }
            
            # Create the completion analysis input
            completion_input = {
                "test_data": test_data,
                "context_data": context_data,
                "step_summary": step_summary,
                "errors": result.errors,
                "screenshots_available": len(result.artifacts.screenshots) > 0 if result.artifacts else False
            }
            
            # 💰 SMART FINAL ANALYSIS: Always use final screenshot for completion analysis (most important)
            images = [final_screenshot] if final_screenshot else None
            if images:
                logger.info(f"📸 Final analysis: Sending screenshot to LLM ({len(final_screenshot)} chars) - Final state validation")
            else:
                logger.info(f"📸 Final analysis: No screenshot data to send to LLM")
            
            result = self.prompt_service.execute_versioned_prompt(
                category="test-analysis",
                prompt_id="test-completion-analysis", 
                variables={
                    "completion_input": json.dumps(completion_input, indent=2, ensure_ascii=False)
                },
                use_case="test_analysis",
                language="es",  # Allow Spanish responses while keeping English prompts
                images=images   # Include final screenshot if available
            )
            
            # Parse the JSON response
            try:
                if result.get("success"):
                    content = result["content"].strip()
                    if not content:
                        logger.warning(f"Empty response from LLM for completion analysis")
                        return self._create_fallback_completion_analysis(result, "empty_response")
                    
                    # Clean markdown formatting if present
                    content = self._clean_json_content(content)
                    
                    # Log the raw content for debugging
                    logger.debug(f"Raw LLM response for completion analysis: {content[:200]}...")
                    
                    completion_result = json.loads(content)
                else:
                    raise Exception(f"LLM request failed: {result.get('error', 'Unknown error')}")
                
                # Add timestamp and metadata
                # Handle both StandardResult objects and dict results safely
                execution_id = result.execution_id if hasattr(result, 'execution_id') else result.get('execution_id', 'unknown')
                completion_result.update({
                    "analyzed_at": datetime.utcnow().isoformat(),
                    "analyzer_version": "1.0.0",
                    "execution_id": execution_id
                })
                
                logger.info(f"✅ Analyzed test completion: {completion_result.get('final_verdict', 'unknown')}")
                return completion_result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse completion analysis JSON: {e}")
                logger.error(f"Raw content that failed to parse: '{result.get('content', '')}'")
                return self._create_fallback_completion_analysis(result, f"json_parse_error: {str(e)[:100]}")
                
        except Exception as e:
            logger.error(f"Error analyzing test completion: {e}", exc_info=True)
            return self._create_fallback_completion_analysis(result, f"analysis_error: {str(e)[:100]}")
    
    async def analyze_full_test(
        self,
        result: StandardResult,
        screenshot_data: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Perform complete analysis of a test execution including all steps and final determination.
        
        Args:
            result: StandardResult object to analyze
            screenshot_data: List of base64 screenshot data for each step
            
        Returns:
            Dict containing complete test analysis
        """
        try:
            logger.info(f"🔍 Starting full test analysis for execution {result.execution_id}")
            
            # Log screenshot data availability
            if screenshot_data:
                logger.info(f"📸 AI Analysis: Received {len(screenshot_data)} screenshots for analysis")
                for i, screenshot in enumerate(screenshot_data):
                    if screenshot:
                        screenshot_size = len(screenshot) if isinstance(screenshot, str) else 0
                        logger.info(f"📸 Screenshot {i+1}: {screenshot_size} characters of base64 data")
                    else:
                        logger.warning(f"📸 Screenshot {i+1}: No data")
            else:
                logger.warning(f"📸 AI Analysis: No screenshots provided for analysis")
            
            # Extract test context from metadata
            test_context = {
                "test_description": result.metadata.get("test_description", ""),
                "gherkin_scenario": result.metadata.get("gherkin_scenario", ""),
                "test_instructions": result.metadata.get("test_instructions", ""),
                "url": result.metadata.get("url", ""),
            }
            
            # Analyze individual steps
            step_analyses = []
            for i, step in enumerate(result.steps):
                screenshot = screenshot_data[i] if screenshot_data and i < len(screenshot_data) else None
                if screenshot:
                    logger.info(f"📸 Step {i+1}: Analyzing with screenshot ({len(screenshot)} chars)")
                else:
                    logger.info(f"📸 Step {i+1}: Analyzing without screenshot")
                step_analysis = await self.analyze_step(step, test_context, screenshot)
                step_analyses.append(step_analysis)
            
            # Analyze test completion with final screenshot if available
            final_screenshot = screenshot_data[-1] if screenshot_data and len(screenshot_data) > 0 else None
            if final_screenshot:
                logger.info(f"📸 Final analysis: Using screenshot ({len(final_screenshot)} chars)")
            else:
                logger.info(f"📸 Final analysis: No final screenshot available")
            completion_analysis = await self.analyze_test_completion(result, step_analyses, final_screenshot)
            
            # Combine results
            full_analysis = {
                "execution_id": result.execution_id,
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "step_analyses": step_analyses,
                "completion_analysis": completion_analysis,
                "summary": {
                    "total_steps": len(result.steps),
                    "steps_with_issues": sum(1 for sa in step_analyses if sa.get("overall_assessment") != "success"),
                    "final_verdict": completion_analysis.get("final_verdict", "INCONCLUSIVE"),
                    "confidence_level": completion_analysis.get("confidence_level", 0.5),
                    "ai_recommendation": completion_analysis.get("summary", "")
                }
            }
            
            logger.info(f"✅ Completed full test analysis: {full_analysis['summary']['final_verdict']}")
            return full_analysis
            
        except Exception as e:
            logger.error(f"Error in full test analysis: {e}", exc_info=True)
            return {
                "execution_id": result.execution_id,
                "error": str(e),
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "step_analyses": [],
                "completion_analysis": self._create_fallback_completion_analysis(result, "full_analysis_error"),
                "summary": {
                    "total_steps": len(result.steps),
                    "steps_with_issues": 0,
                    "final_verdict": "INCONCLUSIVE",
                    "confidence_level": 0.0,
                    "ai_recommendation": f"Analysis failed: {str(e)}"
                }
            }
    
    def _create_fallback_step_analysis(self, step: TestStep, error_type: str) -> Dict[str, Any]:
        """Create fallback analysis when AI analysis fails."""
        return {
            "step_number": step.step_number,
            "technical_status": "success" if step.success else "failure",
            "functional_status": "success" if step.success else "failure",
            "overall_assessment": "success" if step.success else "failure",
            "confidence_score": 0.5,
            "issues_detected": [
                {
                    "type": "error",
                    "category": "technical",
                    "description": f"AI analysis failed: {error_type}",
                    "severity": "medium",
                    "suggestion": "Review step manually"
                }
            ] if not step.success else [],
            "positive_indicators": ["Step completed"] if step.success else [],
            "summary": f"Fallback analysis - step {'succeeded' if step.success else 'failed'}",
            "fallback_analysis": True,
            "error_type": error_type
        }
    
    def _create_fallback_completion_analysis(self, result: Union[StandardResult, Dict[str, Any]], error_type: str) -> Dict[str, Any]:
        """Create fallback completion analysis when AI analysis fails."""
        # Handle both StandardResult objects and dict fallbacks safely
        if isinstance(result, dict):
            success = result.get("success", False)
            errors = result.get("errors", [])
        else:
            success = getattr(result, 'success', False)
            errors = getattr(result, 'errors', [])
            
        return {
            "final_verdict": "PASS" if success else "FAIL",
            "confidence_level": 0.5,
            "primary_objective_met": success,
            "execution_quality": {
                "stability_score": 0.5,
                "coverage_score": 0.5,
                "reliability_score": 0.5,
                "issues_count": {
                    "critical": len(errors) if errors else 0,
                    "major": 0,
                    "minor": 0
                }
            },
            "summary": f"Fallback analysis - test {'passed' if success else 'failed'} based on basic criteria",
            "detailed_reasoning": f"AI analysis failed ({error_type}), using basic success/failure determination",
            "fallback_analysis": True,
            "error_type": error_type
        }
    
    def _clean_json_content(self, content: str) -> str:
        """Clean markdown formatting from JSON content."""
        # Remove markdown code blocks
        if content.startswith("```json"):
            content = content[7:]  # Remove ```json
        elif content.startswith("```"):
            content = content[3:]   # Remove ```
        
        if content.endswith("```"):
            content = content[:-3]  # Remove closing ```
        
        return content.strip()


# Global instance for easy access
_analysis_service: Optional[TestAnalysisService] = None

def get_test_analysis_service() -> TestAnalysisService:
    """Get or create the global test analysis service instance."""
    global _analysis_service
    if _analysis_service is None:
        _analysis_service = TestAnalysisService()
    return _analysis_service

class BackgroundJobsTestAnalysisService(TestAnalysisService):
    """Test analysis service optimized for background jobs with cost-controlled paid models."""
    
    def __init__(self):
        """Initialize the background jobs test analysis service."""
        # Import here to avoid circular imports
        from src.services.llm.llm_service_factory import get_background_jobs_llm_factory
        
        self.llm_factory = get_background_jobs_llm_factory()
        
        # Create a PromptLLMService that uses our background jobs factory
        self.prompt_service = PromptLLMService()
        # Override the factory in the prompt service to use our background jobs factory
        self.prompt_service.llm_factory = self.llm_factory
        
        # Cost optimization settings for background jobs
        import os
        self.cost_optimization_level = os.getenv("AI_ANALYSIS_COST_OPTIMIZATION", "medium").lower()
        logger.info(f"💰 Background Jobs AI Analysis cost optimization level: {self.cost_optimization_level}")
        logger.info(f"🎯 Background Jobs using paid models with $2 cost limit")
    
    def _compress_screenshot_for_ai_analysis(self, screenshot_b64: str) -> str:
        """Compress screenshot specifically for AI analysis to reduce token usage.
        
        Args:
            screenshot_b64: Base64 encoded screenshot
            
        Returns:
            str: Compressed base64 screenshot optimized for AI analysis
        """
        try:
            import base64
            import io
            from PIL import Image
            
            # Get optimization level from environment
            optimization_level = self.cost_optimization_level
            
            # Configure compression based on optimization level
            if optimization_level == "none":
                quality = 95
                max_width = 1920
            elif optimization_level == "low":
                quality = 85
                max_width = 1600
            elif optimization_level == "medium":
                quality = 70
                max_width = 1400
            elif optimization_level == "high":
                quality = 55
                max_width = 1200
            elif optimization_level == "aggressive":
                quality = 40
                max_width = 1000
            else:
                # Default to medium
                quality = 70
                max_width = 1400
            
            # Decode base64 image
            image_data = base64.b64decode(screenshot_b64)
            original_size = len(image_data)
            
            # Open image with PIL
            image = Image.open(io.BytesIO(image_data))
            original_dimensions = image.size
            
            # Resize if width exceeds maximum
            if image.width > max_width:
                ratio = max_width / image.width
                new_height = int(image.height * ratio)
                image = image.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert RGBA to RGB if necessary
            if image.mode == 'RGBA':
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=image.split()[-1])
                image = rgb_image
            
            # Compress to JPEG
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            compressed_data = output.getvalue()
            
            # Encode back to base64
            compressed_b64 = base64.b64encode(compressed_data).decode('utf-8')
            
            # Log compression stats
            compression_ratio = len(compressed_data) / original_size
            logger.info(f"🗜️ Background Jobs: Screenshot compressed for AI analysis: {original_size:,} → {len(compressed_data):,} bytes ({compression_ratio:.1%}) | {original_dimensions} → {image.size} | Q:{quality}, Max:{max_width}px")
            
            return compressed_b64
            
        except Exception as e:
            logger.warning(f"⚠️ Background Jobs: Screenshot compression failed: {e}. Using original.")
            return screenshot_b64
    
    async def analyze_step(
        self,
        step: TestStep,
        test_context: Dict[str, Any],
        screenshot_data: Optional[str] = None
    ) -> Dict[str, Any]:
        """Override analyze_step to apply screenshot compression for AI analysis."""
        # Compress screenshot if available for AI analysis
        compressed_screenshot = None
        if screenshot_data:
            logger.info(f"📸 Background Jobs: Compressing screenshot for step {step.step_number} AI analysis")
            compressed_screenshot = self._compress_screenshot_for_ai_analysis(screenshot_data)
            logger.info(f"📸 Background Jobs: Original: {len(screenshot_data)} chars → Compressed: {len(compressed_screenshot)} chars")
        
        # Call parent method with compressed screenshot
        return await super().analyze_step(step, test_context, compressed_screenshot)
    
    async def analyze_test_completion(
        self,
        result,
        step_analyses: list,
        final_screenshot: Optional[str] = None
    ) -> Dict[str, Any]:
        """Override analyze_test_completion to apply screenshot compression for AI analysis."""
        # Compress final screenshot if available for AI analysis
        compressed_final_screenshot = None
        if final_screenshot:
            logger.info(f"📸 Background Jobs: Compressing final screenshot for completion analysis")
            compressed_final_screenshot = self._compress_screenshot_for_ai_analysis(final_screenshot)
            logger.info(f"📸 Background Jobs: Final screenshot - Original: {len(final_screenshot)} chars → Compressed: {len(compressed_final_screenshot)} chars")
        
        # Call parent method with compressed screenshot
        return await super().analyze_test_completion(result, step_analyses, compressed_final_screenshot)
    
    async def analyze_tests_batch(self, prepared_tests: list[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Analyze multiple tests in a single batch to optimize costs.
        
        Args:
            prepared_tests: List of prepared test data, each containing:
                - job_id: Individual job identifier
                - result: StandardResult object
                - screenshot_data: Optional screenshot data
                
        Returns:
            Dict mapping job_id to analysis results
        """
        logger.info(f"📦 Starting batch analysis for {len(prepared_tests)} tests")
        
        try:
            # Prepare batch prompt data
            batch_data = []
            job_id_mapping = {}
            
            for i, test_data in enumerate(prepared_tests):
                job_id = test_data["job_id"]
                result = test_data["result"]
                screenshot_data = test_data.get("screenshot_data")
                
                # Compress screenshot if available
                compressed_screenshot = None
                if screenshot_data:
                    compressed_screenshot = self._compress_screenshot_for_ai_analysis(screenshot_data)
                
                # Prepare test context for batch
                test_context = {
                    "test_index": i + 1,
                    "test_id": job_id,
                    "execution_id": getattr(result, 'execution_id', 'unknown'),
                    "url": getattr(result, 'url', 'unknown'),
                    "steps": [{
                        "step_number": step.step_number,
                        "action": step.action,
                        "success": step.success,
                        "error_message": step.error_message,
                        "screenshot_available": compressed_screenshot is not None
                    } for step in result.steps],
                    "overall_success": result.success,
                    "errors": result.errors,
                    "screenshot_data": compressed_screenshot
                }
                
                batch_data.append(test_context)
                job_id_mapping[i] = job_id
            
            # Create batch analysis prompt
            batch_prompt = self._create_batch_analysis_prompt(batch_data)
            
            logger.info(f"🤖 Sending batch of {len(prepared_tests)} tests to LLM for analysis")
            
            # Send to LLM
            llm_response = await self.prompt_service.generate_response(
                prompt=batch_prompt,
                response_format="json",
                max_tokens=8000,  # Increased for batch processing
                temperature=0.1
            )
            
            # Parse batch response
            batch_results = self._parse_batch_analysis_response(llm_response, job_id_mapping)
            
            logger.info(f"✅ Successfully completed batch analysis for {len(batch_results)} tests")
            return batch_results
            
        except Exception as e:
            logger.error(f"❌ Batch analysis failed: {e}", exc_info=True)
            
            # Fallback: create individual fallback analyses
            fallback_results = {}
            for test_data in prepared_tests:
                job_id = test_data["job_id"]
                result = test_data["result"]
                
                fallback_results[job_id] = {
                    "step_analyses": [
                        self._create_fallback_step_analysis(step, "batch_analysis_failed")
                        for step in result.steps
                    ],
                    "completion_analysis": self._create_fallback_completion_analysis(result, "batch_analysis_failed"),
                    "summary": {
                        "total_steps": len(result.steps),
                        "successful_steps": len([s for s in result.steps if s.success]),
                        "failed_steps": len([s for s in result.steps if not s.success]),
                        "overall_success": result.success,
                        "batch_processed": True,
                        "fallback_analysis": True
                    },
                    "analysis_timestamp": datetime.now().isoformat()
                }
            
            return fallback_results
    
    def _create_batch_analysis_prompt(self, batch_data: list[Dict[str, Any]]) -> str:
        """Create a prompt for batch analysis of multiple tests."""
        prompt = f"""You are an expert QA analyst. Analyze the following {len(batch_data)} test executions in batch.

For each test, provide:
1. Step-by-step analysis (technical_status, functional_status, issues_detected)
2. Overall completion analysis (final_verdict, confidence_level, execution_quality)
3. Summary with recommendations

IMPORTANT: Respond with valid JSON only. Use this exact structure:

{{
  "batch_results": {{
    "test_1": {{
      "step_analyses": [...],
      "completion_analysis": {{...}},
      "summary": {{...}}
    }},
    "test_2": {{
      "step_analyses": [...],
      "completion_analysis": {{...}},
      "summary": {{...}}
    }}
    // ... for each test
  }}
}}

TEST EXECUTIONS TO ANALYZE:

"""
        
        for i, test_context in enumerate(batch_data, 1):
            prompt += f"\n=== TEST {i} (ID: {test_context['test_id']}) ===\n"
            prompt += f"URL: {test_context['url']}\n"
            prompt += f"Overall Success: {test_context['overall_success']}\n"
            prompt += f"Total Steps: {len(test_context['steps'])}\n"
            
            if test_context['errors']:
                prompt += f"Errors: {test_context['errors']}\n"
            
            prompt += "\nSteps:\n"
            for step in test_context['steps']:
                status = "✅" if step['success'] else "❌"
                prompt += f"{status} Step {step['step_number']}: {step['action']}"
                if step['error_message']:
                    prompt += f" (Error: {step['error_message']})"
                prompt += "\n"
            
            if test_context.get('screenshot_data'):
                prompt += "📸 Screenshot available for analysis\n"
            
            prompt += "\n"
        
        prompt += "\nAnalyze all tests and provide the JSON response with batch_results containing each test's analysis."
        
        return prompt
    
    def _parse_batch_analysis_response(self, llm_response: str, job_id_mapping: Dict[int, str]) -> Dict[str, Dict[str, Any]]:
        """Parse the batch analysis response from LLM."""
        try:
            # Clean and parse JSON
            cleaned_response = self._clean_json_content(llm_response)
            response_data = json.loads(cleaned_response)
            
            batch_results = response_data.get("batch_results", {})
            parsed_results = {}
            
            # Map test results back to job IDs
            for test_key, analysis in batch_results.items():
                # Extract test index from key (e.g., "test_1" -> 0)
                try:
                    test_index = int(test_key.split("_")[1]) - 1
                    job_id = job_id_mapping.get(test_index)
                    
                    if job_id:
                        # Add metadata
                        analysis["analysis_timestamp"] = datetime.now().isoformat()
                        analysis["batch_processed"] = True
                        
                        parsed_results[job_id] = analysis
                    else:
                        logger.warning(f"⚠️ Could not map test_key {test_key} to job_id")
                        
                except (ValueError, IndexError) as e:
                    logger.warning(f"⚠️ Could not parse test_key {test_key}: {e}")
            
            logger.info(f"📊 Parsed {len(parsed_results)} test results from batch analysis")
            return parsed_results
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse batch analysis JSON: {e}")
            logger.error(f"Raw response: {llm_response[:500]}...")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to parse batch analysis response: {e}")
            raise


# Background jobs service instance
_background_jobs_analysis_service = None

def get_background_jobs_test_analysis_service() -> BackgroundJobsTestAnalysisService:
    """Get or create the background jobs test analysis service instance."""
    global _background_jobs_analysis_service
    if _background_jobs_analysis_service is None:
        _background_jobs_analysis_service = BackgroundJobsTestAnalysisService()
    return _background_jobs_analysis_service