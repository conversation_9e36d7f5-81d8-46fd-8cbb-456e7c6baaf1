"""Browser Use Logging Service for QAK

Servicio de logging estructurado en formato JSON para eventos de browser-use
con correlación de IDs y metadatos enriquecidos.
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from dataclasses import dataclass, asdict
from enum import Enum


class LogLevel(Enum):
    """Niveles de logging."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class EventType(Enum):
    """Tipos de eventos de browser-use."""
    SESSION = "session"
    TASK = "task"
    STEP = "step"
    OUTPUT_FILE = "output_file"
    UPDATE_TASK = "update_task"
    ERROR = "error"
    PERFORMANCE = "performance"
    SECURITY = "security"


@dataclass
class StructuredLogEntry:
    """Entrada de log estructurada."""
    timestamp: str
    level: str
    message: str
    event_type: str
    correlation_id: str
    session_correlation_id: Optional[str] = None
    task_correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    device_id: Optional[str] = None
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None
    duration_ms: Optional[int] = None
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    
    def to_json(self) -> str:
        """Convertir a JSON string."""
        data = asdict(self)
        # Filtrar valores None
        data = {k: v for k, v in data.items() if v is not None}
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))


class CorrelationContext:
    """Contexto de correlación para rastrear eventos relacionados."""
    
    def __init__(self):
        self.correlation_id: Optional[str] = None
        self.session_correlation_id: Optional[str] = None
        self.task_correlation_id: Optional[str] = None
        self.user_id: Optional[str] = None
        self.device_id: Optional[str] = None
        self.source_ip: Optional[str] = None
        self.user_agent: Optional[str] = None
        self.metadata: Dict[str, Any] = {}
    
    def set_session_context(
        self,
        session_id: str,
        user_id: Optional[str] = None,
        device_id: Optional[str] = None
    ):
        """Establecer contexto de sesión."""
        self.session_correlation_id = session_id
        if user_id:
            self.user_id = user_id
        if device_id:
            self.device_id = device_id
    
    def set_task_context(self, task_id: str):
        """Establecer contexto de tarea."""
        self.task_correlation_id = task_id
    
    def set_request_context(
        self,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Establecer contexto de request HTTP."""
        if source_ip:
            self.source_ip = source_ip
        if user_agent:
            self.user_agent = user_agent
    
    def add_metadata(self, key: str, value: Any):
        """Agregar metadatos al contexto."""
        self.metadata[key] = value
    
    def clear(self):
        """Limpiar el contexto."""
        self.__init__()


class BrowserUseLogger:
    """Logger estructurado para eventos de browser-use."""
    
    def __init__(self, name: str = "browser_use"):
        self.logger = logging.getLogger(name)
        self.context = CorrelationContext()
        self._setup_logger()
    
    def _setup_logger(self):
        """Configurar el logger con formato JSON."""
        # Evitar duplicar handlers
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(message)s'))
            
            # Add binary content filter to prevent base64 screenshots from cluttering logs
            from app import BinaryContentFilter
            handler.addFilter(BinaryContentFilter())
            
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _create_log_entry(
        self,
        level: LogLevel,
        message: str,
        event_type: EventType,
        correlation_id: Optional[str] = None,
        duration_ms: Optional[int] = None,
        error_code: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> StructuredLogEntry:
        """Crear entrada de log estructurada."""
        
        # Usar correlation_id del contexto si no se proporciona
        if not correlation_id:
            correlation_id = self.context.correlation_id or str(uuid.uuid4())
        
        # Combinar metadatos del contexto con los proporcionados
        combined_metadata = self.context.metadata.copy()
        if metadata:
            combined_metadata.update(metadata)
        
        return StructuredLogEntry(
            timestamp=datetime.utcnow().isoformat() + "Z",
            level=level.value if hasattr(level, 'value') else str(level),
            message=message,
            event_type=event_type.value if hasattr(event_type, 'value') else str(event_type),
            correlation_id=correlation_id,
            session_correlation_id=self.context.session_correlation_id,
            task_correlation_id=self.context.task_correlation_id,
            user_id=self.context.user_id,
            device_id=self.context.device_id,
            source_ip=self.context.source_ip,
            user_agent=self.context.user_agent,
            duration_ms=duration_ms,
            error_code=error_code,
            error_details=error_details,
            metadata=combined_metadata if combined_metadata else None,
            tags=tags
        )
    
    def _log(self, entry: StructuredLogEntry):
        """Escribir entrada de log."""
        json_log = entry.to_json()
        
        # Mapear nivel de log
        level_map = {
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFO: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL
        }
        
        log_level = level_map.get(LogLevel(entry.level), logging.INFO)
        self.logger.log(log_level, json_log)
    
    def info(
        self,
        message: str,
        event_type: EventType = EventType.SESSION,
        **kwargs
    ):
        """Log de información."""
        entry = self._create_log_entry(LogLevel.INFO, message, event_type, **kwargs)
        self._log(entry)
    
    def error(
        self,
        message: str,
        event_type: EventType = EventType.ERROR,
        error_code: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log de error."""
        entry = self._create_log_entry(
            LogLevel.ERROR,
            message,
            event_type,
            error_code=error_code,
            error_details=error_details,
            **kwargs
        )
        self._log(entry)
    
    def warning(
        self,
        message: str,
        event_type: EventType = EventType.SESSION,
        **kwargs
    ):
        """Log de advertencia."""
        entry = self._create_log_entry(LogLevel.WARNING, message, event_type, **kwargs)
        self._log(entry)
    
    def debug(
        self,
        message: str,
        event_type: EventType = EventType.SESSION,
        **kwargs
    ):
        """Log de debug."""
        entry = self._create_log_entry(LogLevel.DEBUG, message, event_type, **kwargs)
        self._log(entry)
    
    def performance(
        self,
        message: str,
        duration_ms: int,
        **kwargs
    ):
        """Log de performance."""
        entry = self._create_log_entry(
            LogLevel.INFO,
            message,
            EventType.PERFORMANCE,
            duration_ms=duration_ms,
            **kwargs
        )
        self._log(entry)
    
    def security(
        self,
        message: str,
        event_details: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log de seguridad."""
        entry = self._create_log_entry(
            LogLevel.WARNING,
            message,
            EventType.SECURITY,
            metadata=event_details,
            **kwargs
        )
        self._log(entry)
    
    @contextmanager
    def session_context(
        self,
        session_id: str,
        user_id: Optional[str] = None,
        device_id: Optional[str] = None
    ):
        """Context manager para sesión."""
        old_session = self.context.session_correlation_id
        old_user = self.context.user_id
        old_device = self.context.device_id
        
        try:
            self.context.set_session_context(session_id, user_id, device_id)
            yield
        finally:
            self.context.session_correlation_id = old_session
            self.context.user_id = old_user
            self.context.device_id = old_device
    
    @contextmanager
    def task_context(self, task_id: str):
        """Context manager para tarea."""
        old_task = self.context.task_correlation_id
        
        try:
            self.context.set_task_context(task_id)
            yield
        finally:
            self.context.task_correlation_id = old_task
    
    @contextmanager
    def correlation_context(self, correlation_id: str):
        """Context manager para correlación."""
        old_correlation = self.context.correlation_id
        
        try:
            self.context.correlation_id = correlation_id
            yield
        finally:
            self.context.correlation_id = old_correlation
    
    @contextmanager
    def timed_operation(self, operation_name: str, event_type: EventType = EventType.PERFORMANCE):
        """Context manager para medir tiempo de operación."""
        start_time = datetime.utcnow()
        
        try:
            yield
        finally:
            end_time = datetime.utcnow()
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            self.performance(
                f"Operation '{operation_name}' completed",
                duration_ms=duration_ms,
                metadata={"operation": operation_name}
            )


# Instancia global del logger
browser_use_logger = BrowserUseLogger("browser_use")


# Funciones de conveniencia
def log_session_event(
    message: str,
    session_id: str,
    user_id: Optional[str] = None,
    device_id: Optional[str] = None,
    **kwargs
):
    """Log de evento de sesión."""
    with browser_use_logger.session_context(session_id, user_id, device_id):
        browser_use_logger.info(message, EventType.SESSION, **kwargs)


def log_task_event(
    message: str,
    task_id: str,
    session_id: Optional[str] = None,
    **kwargs
):
    """Log de evento de tarea."""
    with browser_use_logger.task_context(task_id):
        if session_id:
            browser_use_logger.context.session_correlation_id = session_id
        browser_use_logger.info(message, EventType.TASK, **kwargs)


def log_step_event(
    message: str,
    step_number: int,
    task_id: Optional[str] = None,
    duration_ms: Optional[int] = None,
    **kwargs
):
    """Log de evento de paso."""
    metadata = {"step_number": step_number}
    if kwargs.get("metadata"):
        metadata.update(kwargs["metadata"])
    kwargs["metadata"] = metadata
    
    if task_id:
        browser_use_logger.context.task_correlation_id = task_id
    
    browser_use_logger.info(
        message,
        EventType.STEP,
        duration_ms=duration_ms,
        **kwargs
    )


def log_error_event(
    message: str,
    error_code: Optional[str] = None,
    error_details: Optional[Dict[str, Any]] = None,
    **kwargs
):
    """Log de evento de error."""
    browser_use_logger.error(
        message,
        EventType.ERROR,
        error_code=error_code,
        error_details=error_details,
        **kwargs
    )


def log_file_event(
    message: str,
    file_name: str,
    file_size: Optional[int] = None,
    **kwargs
):
    """Log de evento de archivo."""
    metadata = {"file_name": file_name}
    if file_size:
        metadata["file_size"] = file_size
    if kwargs.get("metadata"):
        metadata.update(kwargs["metadata"])
    kwargs["metadata"] = metadata
    
    browser_use_logger.info(message, EventType.OUTPUT_FILE, **kwargs)