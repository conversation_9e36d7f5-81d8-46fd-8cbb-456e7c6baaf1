INFO:     127.0.0.1:52597 - "GET /api/analytics-v2?start_date=2024-01-01&end_date=2024-12-31&detailed=false HTTP/1.1" 404 Not Found
18:14:11.077 GET /api/analytics-comparison ? detailed='false' & end_date='2024-12-31' & start_date='2024-01-01'
INFO:     127.0.0.1:52597 - "GET /api/analytics-comparison?start_date=2024-01-01&end_date=2024-12-31&detailed=false HTTP/1.1" 404 Not Found
18:14:11.081 GET /api/analytics ? detailed='true' & end_date='2024-12-31' & start_date='2024-01-01'
18:14:11.081   FastAPI arguments
18:14:11.082     ✅ ProjectManagerService: MongoDB mode enabled
18:14:11.082     Service operation: list_projects
INFO:     127.0.0.1:52601 - "GET /api/analytics?start_date=2024-01-01&end_date=2024-12-31&detailed=true HTTP/1.1" 200 OK
18:14:11.435 GET /api/analytics-v2 ? detailed='true' & end_date='2024-12-31' & start_date='2024-01-01'
INFO:     127.0.0.1:52601 - "GET /api/analytics-v2?start_date=2024-01-01&end_date=2024-12-31&detailed=true HTTP/1.1" 404 Not Found
18:22:36.706 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
18:22:36.711 BrowserPool initialized (min: 2, max: 10)
18:22:36.714 PerformanceMonitor initialized (interval: 30s)
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
✅ Browser-use logging configured from /Users/<USER>/Proyectos/qak/libs
INFO     [browser_use.telemetry.service] Anonymized telemetry enabled. See https://docs.browser-use.com/development/telemetry for more information.
18:22:37.556 ✅ Using explicit REDIS_URL: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
18:22:37.632 Iniciando API web de QA Agent en http://0.0.0.0:8000...
18:22:37.633 Documentacion API disponible en:
18:22:37.633   - Swagger UI: http://localhost:8000/docs
18:22:37.633   - ReDoc: http://localhost:8000/redoc
18:22:37.633 
Presiona Ctrl+C para detener el servidor.
INFO:     Started server process [52685]
INFO:     Waiting for application startup.
18:22:38.010 🔧 Database configuration initialized for development environment
18:22:38.011 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
18:22:38.011 🔧 Database manager initialized
18:22:38.011 🔌 Connecting to MongoDB
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
18:22:39.943 ✅ Connected to MongoDB database: qak_dev
18:22:39.944 📊 MongoDB Server Version: 8.0.11
18:22:39.945 🚀 Database initialization successful
18:22:39.946 📊 Database indexes will be managed by Beanie ODM models
18:22:39.947 🔧 ODM manager initialized
18:22:39.947 📝 Registered ODM model: Project
18:22:39.948 📝 Registered ODM model: Execution
18:22:39.948 📝 Registered ODM model: CodegenSession
18:22:39.948 📝 Registered ODM model: Artifact
18:22:39.948 📝 Registered ODM model: BrowserConfiguration
18:22:39.948 📝 Registered ODM model: BrowserSessionPool
18:22:39.949 📝 Registered ODM model: BrowserUseEventDocument
18:22:39.949 📝 Registered ODM model: BrowserUseSessionDocument
18:22:39.949 📝 Registered ODM model: BrowserUseTaskDocument
18:22:39.949 📝 Registered ODM model: BrowserUseStepDocument
18:22:39.949 📝 Registered ODM model: BrowserUseFileDocument
18:22:40.112 🚀 Initializing Beanie ODM with 11 models...
18:22:40.114 🧹 Cleaning up potentially conflicting indexes...
18:22:40.455 🗑️ Dropped conflicting index: projects.project_id_1
18:22:40.626 🗑️ Dropped conflicting index: projects.created_at_-1
18:22:40.964 🗑️ Dropped conflicting index: executions.execution_id_1
18:22:41.312 🗑️ Dropped conflicting index: codegen_sessions.session_id_1
18:22:41.655 🗑️ Dropped conflicting index: artifacts.artifact_id_1
18:22:41.657 ✅ Index cleanup completed
18:22:47.809 ✅ Beanie ODM initialization successful
18:22:47.809   📝 Project -> projects
18:22:47.810   📝 Execution -> executions
18:22:47.810   📝 CodegenSession -> codegen_sessions
18:22:47.810   📝 Artifact -> artifacts
18:22:47.810   📝 BrowserConfiguration -> browser_configurations
18:22:47.810   📝 BrowserSessionPool -> browser_session_pools
18:22:47.810   📝 BrowserUseEventDocument -> browser_use_events
18:22:47.810   📝 BrowserUseSessionDocument -> browser_use_sessions
18:22:47.810   📝 BrowserUseTaskDocument -> browser_use_tasks
18:22:47.810   📝 BrowserUseStepDocument -> browser_use_steps
18:22:47.811   📝 BrowserUseFileDocument -> browser_use_files
18:22:48.148 Predefined configuration already exists: test_case
18:22:48.309 Predefined configuration already exists: smoke
18:22:48.473 Predefined configuration already exists: exploration
18:22:48.643 Predefined configuration already exists: exploration_deep
18:22:48.810 Predefined configuration already exists: test_suite
18:22:49.315 ✅ ProjectManagerService: MongoDB mode enabled
18:22:49.316 Service operation: list_projects
18:22:49.318 BrowserPool initialized (min: 2, max: 10)
18:22:49.319 PerformanceMonitor initialized (interval: 30s)
18:22:49.319 ExecutionOrchestrator initialized
18:22:49.319 🔄 Creating shared global EnhancedArtifactCollector instance
18:22:49.320 ✅ Initialized r2 storage backend for artifacts
18:22:49.320 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
18:22:49.320 EnhancedArtifactCollector initialized with CloudflareR2StorageBackend cloud storage backend (R2-ONLY MODE)
18:22:49.320 Loaded existing artifacts from storage
18:22:49.320 ArtifactCollector initialized successfully
18:22:49.321 🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud
18:22:49.321 ✅ Shared global EnhancedArtifactCollector instance initialized
18:22:49.321 Execution orchestrator initialized with ExecutionRepository.
INFO:     Application startup complete.
18:22:49.323 Loading active/stale sessions from database...
18:22:49.324 Cleaning up old, unfinished sessions from database...
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
18:22:50.375 Processed 0 stale sessions from database.
18:22:50.574 No old, unfinished sessions to clean up.
18:23:08.336 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:08.341 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:08.342 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
INFO:     127.0.0.1:55425 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:23:08.343 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:55427 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:23:08.343 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
INFO:     127.0.0.1:55428 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
INFO:     127.0.0.1:55430 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55431 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:23:08.346 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:08.351   FastAPI arguments
18:23:08.351 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:23:08.352   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
18:23:08.352     ✅ ProjectManagerService: MongoDB mode enabled
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
               FastAPI arguments
18:23:08.353     ✅ ProjectManagerService: MongoDB mode enabled
18:23:08.653     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:08.653     Service operation: list_projects
18:23:08.654     Service operation: list_projects
18:23:09.332     ✅ Project found: Web Agent Page
18:23:09.332     ✅ Test suite found: Funcionalidad core
18:23:09.332     ✅ Test case found: Login
18:23:09.332     ✅ Test case found: Login
INFO:     127.0.0.1:55427 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:23:09.335 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:09.335   FastAPI arguments
18:23:09.335     ✅ ProjectManagerService: MongoDB mode enabled
18:23:09.336     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:09.336     Service operation: list_projects
18:23:09.502     ✅ Project found: Web Agent Page
18:23:09.502     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:23:09.502     📝 Has environments attr: True
18:23:09.503     📝 Environments count: 2
INFO:     127.0.0.1:55425 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:23:09.504 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:23:09.505   FastAPI arguments
18:23:09.506     ✅ ProjectManagerService: MongoDB mode enabled
18:23:09.506     Service operation: list_projects
18:23:09.684     ✅ Project found: Web Agent Page
18:23:09.684     ✅ Test suite found: Funcionalidad core
18:23:09.684     ✅ Test case found: Login
18:23:09.684     ✅ Test case found: Login
INFO:     127.0.0.1:55437 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:23:09.686 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:09.687   FastAPI arguments
18:23:09.687 OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
18:23:09.688     ✅ ProjectManagerService: MongoDB mode enabled
INFO:     127.0.0.1:55437 - "OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:23:09.689     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:09.689     Service operation: list_projects
18:23:09.690 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:23:09.690   FastAPI arguments
18:23:09.691     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:09.691     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:23:09.691     ✅ Orchestrator and execution repository initialized
18:23:09.858     ✅ Project found: Web Agent Page
18:23:09.859     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:23:09.859     📝 Has environments attr: True
18:23:09.859     📝 Environments count: 2
INFO:     127.0.0.1:55427 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:23:09.860 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:23:09.861   FastAPI arguments
18:23:09.861     ✅ ProjectManagerService: MongoDB mode enabled
18:23:09.861     Service operation: list_projects
18:23:10.037     ✅ Project found: Web Agent Page
18:23:10.037     ✅ Test suite found: Funcionalidad core
18:23:10.037     ✅ Test case found: Login
18:23:10.037     ✅ Test case found: Login
INFO:     127.0.0.1:55425 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:23:10.205     ✅ Project found: Web Agent Page
18:23:10.205     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:23:10.205     📝 Has environments attr: True
18:23:10.205     📝 Environments count: 2
INFO:     127.0.0.1:55427 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:23:17.569     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:17.569     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:55452 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:23:17.572 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:23:17.572   FastAPI arguments
18:23:17.573     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:17.573     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:23:17.573     ✅ Orchestrator and execution repository initialized
18:23:17.983 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:23:17.984   FastAPI arguments
18:23:17.984     ✅ ProjectManagerService: MongoDB mode enabled
18:23:17.985     Service operation: list_projects
18:23:18.654     ✅ Project found: Web Agent Page
18:23:18.654     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:23:18.654     📝 Has environments attr: True
18:23:18.654     📝 Environments count: 2
INFO:     127.0.0.1:55452 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:23:21.224     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:23:21.224     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:55454 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:24:09.523 OPTIONS /api/projects/
INFO:     127.0.0.1:55793 - "OPTIONS /api/projects/ HTTP/1.1" 200 OK
18:24:09.556 GET /api/projects/
18:24:09.562   FastAPI arguments
18:24:09.572     ✅ ProjectManagerService: MongoDB mode enabled
18:24:09.588     Service operation: list_projects
INFO:     127.0.0.1:55799 - "GET /api/projects/ HTTP/1.1" 200 OK
18:24:10.287 OPTIONS /api/analytics ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-13'
INFO:     127.0.0.1:55799 - "OPTIONS /api/analytics?start_date=2025-06-13&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:24:10.290 GET /api/analytics ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-13'
18:24:10.292   FastAPI arguments
18:24:10.294     ✅ ProjectManagerService: MongoDB mode enabled
18:24:10.296     Service operation: list_projects
INFO:     127.0.0.1:55828 - "GET /api/analytics?start_date=2025-06-13&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:24:11.138 OPTIONS /api/v2/tests/execute
INFO:     127.0.0.1:55828 - "OPTIONS /api/v2/tests/execute HTTP/1.1" 200 OK
18:24:11.139 POST /api/v2/tests/execute
18:24:11.140   FastAPI arguments
18:24:11.141     🔧 BROWSER CONFIG: Resolving configuration for execution type case
18:24:11.141     🔧 BROWSER CONFIG: MongoDB query filters: {'execution_types': {'$in': ['case']}, 'is_active': True}
18:24:11.310     Found 1 configurations for execution type: case
18:24:11.310       Config 1: Test Case (Default) - execution_types: ['case', 'full'] - model_name: None
18:24:11.310     Ordered configurations by priority - OpenRouter configs first
18:24:11.310       1. Test Case (Default) (provider: openrouter, usage: 0)
18:24:11.311     🔧 BROWSER CONFIG: Found DB config 'Test Case (Default)' for type case
18:24:11.311     🔧 BROWSER CONFIG: MongoDB settings: model_provider=openrouter, model_name=None, headless=True
18:24:11.311     🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: openai/gpt-4.1-mini
18:24:11.311     🔧 BROWSER CONFIG: DB config failed (BrowserConfig.__init__() got an unexpected keyword argument 'capture_beyond_viewport'), using hardcoded fallback
18:24:11.311     🔧 BROWSER CONFIG: Applied hardcoded fallback for type case with headless=False
18:24:11.311     ✅ ProjectManagerService: MongoDB mode enabled
18:24:11.311     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:24:11.311     Service operation: list_projects
18:24:11.652     ✅ Project found: Web Agent Page
18:24:11.652     ✅ Test suite found: Funcionalidad core
18:24:11.652     ✅ Test case found: Login
18:24:11.989     🔍 EXECUTION DEBUG: Environment name resolved: QA
18:24:11.990     Created execution context 40ec54d8-1695-4148-8910-949d65b85586 for TestType.CASE
18:24:11.990     🔍 CONTEXT DEBUG: No application_version provided or empty
18:24:11.990     Registering execution 40ec54d8-1695-4148-8910-949d65b85586 in orchestrator.active_executions
18:24:11.990     Orchestrator instance ID: 5122926032
18:24:11.990     Active executions before: []
18:24:11.990     Execution 40ec54d8-1695-4148-8910-949d65b85586 status: ExecutionStatus.RUNNING
18:24:11.990     Active executions after: ['40ec54d8-1695-4148-8910-949d65b85586']
18:24:11.990     Successfully registered 40ec54d8-1695-4148-8910-949d65b85586
18:24:11.990     Simple tracking has: ['40ec54d8-1695-4148-8910-949d65b85586']
18:24:11.990     Execution 40ec54d8-1695-4148-8910-949d65b85586 started and returned immediately
INFO:     127.0.0.1:55828 - "POST /api/v2/tests/execute HTTP/1.1" 200 OK
18:24:11.991 Background execution started for 40ec54d8-1695-4148-8910-949d65b85586
18:24:11.991 Execution 40ec54d8-1695-4148-8910-949d65b85586 status: ExecutionStatus.RUNNING
18:24:12.322 Using specified environment: QA
18:24:12.323 🔍 ORCHESTRATOR DEBUG: Resolved environment: Environment(name='QA', base_url='https://web-agent-playground.lovable.app', is_default=True)
18:24:12.323 🔍 ORCHESTRATOR DEBUG: Environment details - ID: 079f669c-ea2c-43f4-bf76-0a98897b1353, Name: QA
18:24:12.323 Using absolute URL: https://web-agent-playground.lovable.app
18:24:12.324 🔍 ORCHESTRATOR DEBUG: Constructed URL: https://web-agent-playground.lovable.app
18:24:12.324 Environment info set for execution 40ec54d8-1695-4148-8910-949d65b85586: QA -> https://web-agent-playground.lovable.app
18:24:12.324 🔍 ORCHESTRATOR DEBUG: Environment info set in context
18:24:12.324 BrowserPool initialized successfully
18:24:12.324 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:24:12.324   - headless: False
18:24:12.324   - model_provider: openrouter
18:24:12.324   - model_name: openai/gpt-4.1-mini
18:24:12.324   - highlight_elements: False
18:24:12.324   - temperature: 0.1
18:24:12.325 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added disable_security=False from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added enable_memory=False from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added generate_gif=True from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added headless=False from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:24:12.325 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added max_failures=3 from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added model_name=openai/gpt-4.1-mini from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added model_provider=openrouter from MongoDB config
18:24:12.326 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added retry_delay=5 from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:24:12.327 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:24:12.328 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:24:12.328 🔍 BROWSER POOL: Ensured critical field headless=False
18:24:12.328 🔍 BROWSER POOL: Ensured critical field disable_security=False
18:24:12.328 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:24:12.328 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'model_name', 'model_provider', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:24:12.328 🔍 BROWSER POOL: headless value being passed: False
18:24:12.328 🔍 BROWSER POOL: model_provider=openrouter, model_name=openai/gpt-4.1-mini
18:24:12.329 🔍 BROWSER POOL: BrowserProfile created - headless=False
18:24:12.329 ✅ BROWSER POOL: Created browser with MongoDB config - headless=False, model_provider=openrouter
18:24:12.380 Created browser 4ccd57ba-f980-4624-a4f4-73eeb783dec3 with config hash ddfce38a
18:24:12.380 Created new browser 4ccd57ba-f980-4624-a4f4-73eeb783dec3 for 40ec54d8-1695-4148-8910-949d65b85586
18:24:12.380 Executing with strategy: TestCaseStrategy
18:24:12.380 Executing test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 using TestCaseStrategy.
18:24:12.380 Created initial actions to navigate to: https://web-agent-playground.lovable.app
18:24:12.380 🔧 STRATEGY CONFIG: Using resolved config from context: headless=False, max_steps=50
18:24:12.380 🔧 STRATEGY CONFIG: Created agent config with max_steps=50 from DB config
18:24:12.381 Creating OpenRouter LLM with model: openai/gpt-4.1-mini
18:24:12.381 Using provided Gherkin scenario for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO     [browser_use.agent.service] 💾 File system path: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browser_use_agent_2da1da94
INFO     [browser_use.Agent🅰 da94 on 🆂 da94 🅟 72] 🧠 Starting a browser-use agent 0.5.4 with base_model=openai/gpt-4.1-mini +vision extraction_model=openai/gpt-4.1-mini +file_system
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 72] 🚀 Starting task: Caracterstica: Autenticacin de Usuario

Escenario: Inicio de sesin exitoso con credenciales vlidas
  Dado que el usuario est en la pgina de inicio de sesin
  Cuando el usuario ingresa "<EMAIL>" como nombre de usuario y "admin123" como contrasea
  Y el usuario intenta iniciar sesin
  Entonces el usuario debera iniciar sesin exitosamente
18:24:12.741 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:55853 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:24:12.749 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:55855 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:None #96] 🎭 Launching new local browser playwright:chromium keep_alive=False user_data_dir= ~/.config/browseruse/profiles/default
WARNING  [browser_use.BrowserSession🆂 e5ff:None #96] ⚠️ SingletonLock conflict detected. Profile at ~/.config/browseruse/profiles/default is locked. Using temporary profile instead: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-g3p1moe6
INFO     [browser_use.BrowserSession🆂 e5ff:None #96]  ↳ Spawning Chrome subprocess listening on CDP http://127.0.0.1:55861/ with user_data_dir= /private/var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-g3p1moe6
18:24:15.051 OPTIONS /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
INFO:     127.0.0.1:55828 - "OPTIONS /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:15.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:15.055   FastAPI arguments
18:24:15.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🌎 Connecting to newly spawned browser via CDP http://127.0.0.1:55861/ -> browser_pid=53399 (local)
18:24:18.050 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:18.051   FastAPI arguments
18:24:18.051     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.controller.service] 🔗  Opened new tab #1 with url https://web-agent-playground.lovable.app
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: go_to_url()
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 386,109 bytes (514,812 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 2: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:24:21.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:21.052   FastAPI arguments
18:24:21.053     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:24.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:24.052   FastAPI arguments
18:24:24.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user request is to test the user authentication feature with a successful login scenario using valid credentials. The login page is loaded with input fields for email and password, and a submit button labeled 'Iniciar Sesión'. The placeholders in the input fields match the test credentials provided in the scenario. The next step is to input the email '<EMAIL>' into the email field and the password 'admin123' into the password field, then click the 'Iniciar Sesión' button to attempt login. After that, I will need to verify if the login was successful by checking for any confirmation or redirection indicating a successful login.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: The login page with input fields and submit button is correctly loaded and ready for interaction. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The login page is displayed with email and password fields and a submit button. The placeholders match the test credentials '<EMAIL>' and 'admin123'.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Input the email '<EMAIL>' and password 'admin123' into the respective fields and click the 'Iniciar Sesión' button to attempt login.

18:24:26.784 🧠 openai/gpt-4.1-mini | 🆕 2.7k + 💾 4.6k | 📤 272
18:24:27.050 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:27.051   FastAPI arguments
18:24:27.051     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 2: Ran 1 actions in 8.71s: ✅ 1
18:24:27.127 POST /api/browser-use/v1/api/v1/oauth/device/authorize
18:24:27.128 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:55968 - "POST /api/browser-use/v1/api/v1/oauth/device/authorize HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55969 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
WARNING  [browser_use.sync.auth] Cloud sync authentication endpoint not found (404). Check your BROWSER_USE_CLOUD_API_URL setting.
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 390,081 bytes (520,108 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 3: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:24:30.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:30.051   FastAPI arguments
18:24:30.051     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:33.050 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:33.051   FastAPI arguments
18:24:33.051     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:36.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:36.052   FastAPI arguments
18:24:36.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The email field has been successfully filled with '<EMAIL>'. The next step is to input the password 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt login. This will complete the login attempt as per the user request scenario for successful login with valid credentials.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: Successfully input the email '<EMAIL>' into the email field. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: Email field filled with '<EMAIL>'. Password field is visible and ready for input. The 'Iniciar Sesión' button is available for clicking to submit the login form.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Input the password 'admin123' into the password field and click the 'Iniciar Sesión' button to attempt login.

18:24:36.335 🧠 openai/gpt-4.1-mini | 🆕 2.8k + 💾 4.6k | 📤 189
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 3: Ran 1 actions in 9.51s: ✅ 1
18:24:36.638 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56034 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 385,428 bytes (513,904 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 4: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:24:39.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:39.052   FastAPI arguments
18:24:39.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:42.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:42.051   FastAPI arguments
18:24:42.051     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:42.331 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:24:42.331   - headless: True
18:24:42.331   - model_provider: None
18:24:42.331   - model_name: None
18:24:42.331   - highlight_elements: False
18:24:42.331   - temperature: 0.1
18:24:42.332 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:24:42.332 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:24:42.333 🔍 BROWSER POOL: Ensured critical field headless=True
18:24:42.333 🔍 BROWSER POOL: Ensured critical field disable_security=True
18:24:42.333 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:24:42.333 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:24:42.333 🔍 BROWSER POOL: headless value being passed: True
18:24:42.333 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
18:24:42.334 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:24:42.334 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
18:24:42.385 Created browser 3f701e08-411c-4a23-b95d-1d965dd6af46 with config hash 20b966c0
18:24:42.385 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:24:42.385   - headless: True
18:24:42.385   - model_provider: None
18:24:42.385   - model_name: None
18:24:42.385   - highlight_elements: False
18:24:42.386   - temperature: 0.1
18:24:42.386 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:24:42.386 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:24:42.387 🔍 BROWSER POOL: Ensured critical field headless=True
18:24:42.387 🔍 BROWSER POOL: Ensured critical field disable_security=True
18:24:42.387 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:24:42.387 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:24:42.387 🔍 BROWSER POOL: headless value being passed: True
18:24:42.387 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
18:24:42.388 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:24:42.388 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
18:24:42.438 Created browser 822420ae-0945-4cc3-94ed-2eec6ee424d0 with config hash 20b966c0
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The email and password fields have been correctly filled with the valid credentials '<EMAIL>' and 'admin123'. The next step is to click the 'Iniciar Sesión' button to attempt login and verify if the user can successfully log in. After clicking, I will observe the page for any indication of successful login or error messages.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: Successfully input the password 'admin123' into the password field. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The login form is fully filled with valid credentials: email '<EMAIL>' and password 'admin123'. The 'Iniciar Sesión' button is ready to be clicked to submit the login form.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login and verify successful authentication.

18:24:43.765 🧠 openai/gpt-4.1-mini | 🆕 2.8k + 💾 4.7k | 📤 185
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 4: Ran 1 actions in 7.29s: ✅ 1
18:24:43.927 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56072 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
18:24:45.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:45.051   FastAPI arguments
18:24:45.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 280,173 bytes (373,564 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 5: Evaluating page with 20 interactive elements on: https://web-agent-playground.lovable.app/
18:24:48.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:48.051   FastAPI arguments
18:24:48.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user requested to verify the successful login scenario with valid credentials. The login was performed with '<EMAIL>' and 'admin123', and the current page shows a dashboard with a notification 'Login exitoso' and 'Bienvenido al sistema de testing', indicating successful authentication. This matches the expected outcome of the scenario. The next step is to confirm this success by checking the presence of the success message and then finalize the task.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: The login was successful, and the dashboard page with a success message is displayed. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The user has successfully logged in with valid credentials. The dashboard page is visible with a 'Login exitoso' message confirming successful authentication.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Confirm the presence of the success message 'Login exitoso' on the page to validate the successful login scenario.

18:24:50.495 🧠 openai/gpt-4.1-mini | 🆕 3.0k + 💾 4.9k | 📤 210
18:24:51.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:51.052   FastAPI arguments
18:24:51.053     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:24:52.963 🧠 openai/gpt-4.1-mini | 📥 324 | 📤 89
INFO     [browser_use.controller.service] 📄 Page Link: https://web-agent-playground.lovable.app/
Query: Login exitoso message on dashboard page
Extracted Content:
```json
{
  "explanation": "The webpage is a dashboard for a system called 'Sistema Testing' showing general system summary, user statistics, sales data, recent activities, and navigation options. However, there is no information or message related to a 'Login exitoso' (successful login) message on the dashboard page.",
  "requested_information": "Login exitoso message on dashboard page is not available on this webpage."
}
```
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: extract_structured_data()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 5: Ran 1 actions in 9.04s: ✅ 1
18:24:52.973 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56127 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
18:24:54.051 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:54.052   FastAPI arguments
18:24:54.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 248,291 bytes (331,056 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 6: Evaluating page with 19 interactive elements on: https://web-agent-playground.lovable.app/
18:24:57.053 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:24:57.053   FastAPI arguments
18:24:57.054     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user request is to validate the feature "Autenticación de Usuario" with the scenario "Inicio de sesión exitoso con credenciales válidas." The login was performed successfully in previous steps, and the dashboard page is now visible. However, the extracted data shows there is no explicit "Login exitoso" message on the dashboard page. To confirm the successful login, I should verify the presence of other indicators on the dashboard page that confirm the user is logged in, such as the presence of the "Cerrar Sesión" button or the dashboard content itself. This will validate the successful login scenario as per the user request.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: Extracted data confirmed that there is no explicit 'Login exitoso' message on the dashboard page. Verdict: Success in extraction but failure in finding the message.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The user has successfully logged in with valid credentials and reached the dashboard page. There is no explicit 'Login exitoso' message, but the dashboard content and 'Cerrar Sesión' button are visible, indicating a successful login state.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Check for the presence of the 'Cerrar Sesión' button or other dashboard elements that confirm the user is logged in successfully.

18:24:59.885 🧠 openai/gpt-4.1-mini | 🆕 3.1k + 💾 5.0k | 📤 253
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 5: Cerrar Sesión
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 6: Ran 1 actions in 7.07s: ✅ 1
18:25:00.049 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56194 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:25:00.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:00.109   FastAPI arguments
18:25:00.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 386,109 bytes (514,812 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 7: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:25:03.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:03.109   FastAPI arguments
18:25:03.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:06.107 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:06.107   FastAPI arguments
18:25:06.107     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The current page shows the login form again with the email and password fields pre-filled with '<EMAIL>' and 'admin123', and the 'Iniciar Sesión' button is available. This indicates that the user has been logged out or the session reset after clicking 'Cerrar Sesión' previously. To verify the login functionality as per the user request, I will input the credentials again and attempt to log in to confirm the successful login scenario.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: Clicked the 'Cerrar Sesión' button to log out, which redirected back to the login page. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The user was logged in successfully before, then clicked 'Cerrar Sesión' to log out, which brought back the login page with credentials fields visible again. Ready to test login again with valid credentials.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Input the email '<EMAIL>' and password 'admin123' again and click 'Iniciar Sesión' to verify successful login.

18:25:08.666 🧠 openai/gpt-4.1-mini | 📥 8.0k | 📤 242
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 7: Ran 1 actions in 8.94s: ✅ 1
18:25:08.993 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56242 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:25:09.107 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:09.108   FastAPI arguments
18:25:09.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 390,396 bytes (520,528 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 8: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:25:12.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:12.109   FastAPI arguments
18:25:12.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:15.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:15.110   FastAPI arguments
18:25:15.111     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:18.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:18.109   FastAPI arguments
18:25:18.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user request is to test the successful login scenario with valid credentials on the login page. The current page is the login page with the email and password fields visible. The email field currently contains a typo ('<EMAIL>' is typed as '<EMAIL>'). To proceed correctly, I need to fix the email input to the correct '<EMAIL>', input the password 'admin123', and then click the 'Iniciar Sesión' button to attempt login. This will verify the successful login flow as requested.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: The email field was filled but with a typo in the email address. Verdict: Partial success but needs correction.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: Currently on the login page with email field containing '<EMAIL>' (typo). Password field is empty. The 'Iniciar Sesión' button is visible and ready for interaction.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Correct the email field to '<EMAIL>', input the password 'admin123', and click the 'Iniciar Sesión' button to test successful login.

18:25:20.293 🧠 openai/gpt-4.1-mini | 🆕 2.9k + 💾 5.2k | 📤 258
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 8: Ran 1 actions in 11.61s: ✅ 1
18:25:20.609 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56337 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:25:21.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:21.108   FastAPI arguments
18:25:21.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 390,081 bytes (520,108 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 9: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:25:24.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:24.110   FastAPI arguments
18:25:24.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:27.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:27.109   FastAPI arguments
18:25:27.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The current email input field contains '<EMAIL>' which matches the valid credential required by the user request. The password field is visible but empty. The next step is to input the password 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt a successful login as per the scenario described by the user.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: Successfully corrected the email field to '<EMAIL>'. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The login page is loaded with the email field correctly set to '<EMAIL>'. The password field is empty and ready for input. The 'Iniciar Sesión' button is visible and ready for interaction.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Input the password 'admin123' into the password field and click the 'Iniciar Sesión' button to attempt login.

18:25:27.430 🧠 openai/gpt-4.1-mini | 🆕 2.9k + 💾 5.4k | 📤 181
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 9: Ran 1 actions in 7.12s: ✅ 1
18:25:27.729 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56382 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 385,428 bytes (513,904 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 10: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:25:30.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:30.109   FastAPI arguments
18:25:30.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:33.052 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:33.052   FastAPI arguments
18:25:33.053     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user request is to test the successful login scenario with valid credentials on the login page. The current page shows the login form with the email field pre-filled with '<EMAIL>' and the password field pre-filled with 'admin123'. The 'Iniciar Sesión' button is available to submit the form. The next logical step is to click the 'Iniciar Sesión' button to attempt login and verify if the login is successful.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: The email and password fields are correctly filled with valid credentials, ready to submit the login form. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: Currently on the login page with valid credentials '<EMAIL>' and 'admin123' filled in the respective fields. Ready to submit the login form by clicking 'Iniciar Sesión'.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login and verify successful authentication.

18:25:35.936 🧠 openai/gpt-4.1-mini | 🆕 2.9k + 💾 5.5k | 📤 210
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 10: Ran 1 actions in 8.37s: ✅ 1
18:25:36.098 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56433 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:25:36.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:36.109   FastAPI arguments
18:25:36.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Full page screenshot using captureBeyondViewport without clip restrictions
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🔥 SCREENSHOT DEBUG: Captured screenshot size: 282,257 bytes (376,344 base64 chars)
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 11: Evaluating page with 20 interactive elements on: https://web-agent-playground.lovable.app/
18:25:39.110 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:39.110   FastAPI arguments
18:25:39.111     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:42.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:42.109   FastAPI arguments
18:25:42.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:45.110 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:45.111   FastAPI arguments
18:25:45.111     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 💡 Thinking:
The user request is to verify the successful login scenario with valid credentials. From the previous steps, the login was successful, and the dashboard page is displayed. Although there is no explicit 'Login exitoso' message visible in the main dashboard content, the presence of the 'Cerrar Sesión' button and the dashboard elements strongly indicate a successful login. Additionally, the page contains a visible notification or message box at the bottom right corner with the text 'Login exitoso Bienvenido al sistema de testing', which confirms the successful login. This matches the expected outcome of the scenario. Therefore, the login feature works as intended with valid credentials.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 👍 Eval: The dashboard page is correctly displayed after login, and the 'Login exitoso' notification is visible, confirming successful login. Verdict: Success
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🧠 Memory: The user has successfully logged in with valid credentials '<EMAIL>' and 'admin123'. The dashboard page is visible with key system metrics and a 'Cerrar Sesión' button. A notification message 'Login exitoso Bienvenido al sistema de testing' is displayed, confirming successful login.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 🎯 Next goal: No further actions needed as the login success scenario is verified. Prepare to complete the task.

18:25:45.919 🧠 openai/gpt-4.1-mini | 🆕 3.1k + 💾 5.6k | 📤 376
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ☑️ Executed action 1/1: done()
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📄 Result: The user authentication feature for the scenario 'Inicio de sesión exitoso con credenciales válidas' has been successfully verified. The user was able to log in with the email '<EMAIL>' and password 'admin123', and upon login, the dashboard page was displayed with relevant system information and a visible 'Login exitoso' notification message. This confirms that the user can log in successfully with valid credentials as expected.
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] 📍 Step 11: Ran 1 actions in 9.93s: ✅ 1
INFO     [browser_use.Agent🅰 da94 on 🆂 e5ff 🅟 36] ✅ Task completed successfully
18:25:46.024 📊 Per-Model Usage Breakdown:
18:25:46.024   🤖 openai/gpt-4.1-mini: 82.5k tokens | ⬅️ 80.1k | ➡️ 2.5k | 📞 11 calls | 📈 7.5k/call
18:25:46.033 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56489 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:25:46.055 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:56491 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96] 🛑 Closing cdp_url=http://127.0.0.1:55861/ browser context  <Browser type=<BrowserType name=chromium executable_path=/Users/<USER>/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium> version=138.0.7204.23>
INFO     [browser_use.BrowserSession🆂 e5ff:55861 #96]  ↳ Killing browser_pid=53399 ~/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium (terminate() called)
18:25:46.469 Processed 10 screenshots from history (artifacts already created by collector)
18:25:46.469 🗂️  Retrieved 0 artifacts for execution 40ec54d8-1695-4148-8910-949d65b85586
18:25:46.470 Processing 10 history items from history.history
18:25:46.470 [STEP EXTRACT] Processing step 1: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.470 [STEP EXTRACT] Successfully processed step 1: action_type=unknown, success=True
18:25:46.470 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.471 [STEP EXTRACT] Processing step 2: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.471 [STEP EXTRACT] Successfully processed step 2: action_type=unknown, success=True
18:25:46.471 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.471 [STEP EXTRACT] Processing step 3: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.471 [STEP EXTRACT] Successfully processed step 3: action_type=unknown, success=True
18:25:46.471 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.471 [STEP EXTRACT] Processing step 4: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.471 [STEP EXTRACT] Successfully processed step 4: action_type=unknown, success=True
18:25:46.471 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.471 [STEP EXTRACT] Processing step 5: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.472 [STEP EXTRACT] Successfully processed step 5: action_type=unknown, success=True
18:25:46.472 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.472 [STEP EXTRACT] Processing step 6: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.472 [STEP EXTRACT] Successfully processed step 6: action_type=unknown, success=True
18:25:46.472 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.472 [STEP EXTRACT] Processing step 7: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.472 [STEP EXTRACT] Successfully processed step 7: action_type=unknown, success=True
18:25:46.472 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.472 [STEP EXTRACT] Processing step 8: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.472 [STEP EXTRACT] Successfully processed step 8: action_type=unknown, success=True
18:25:46.473 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.473 [STEP EXTRACT] Processing step 9: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.473 [STEP EXTRACT] Successfully processed step 9: action_type=unknown, success=True
18:25:46.473 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.473 [STEP EXTRACT] Processing step 10: type=<class 'browser_use.agent.views.AgentHistory'>
18:25:46.473 [STEP EXTRACT] Successfully processed step 10: action_type=unknown, success=True
18:25:46.473 [STEP EXTRACT] Traceback: NoneType: None

18:25:46.473 Checking 10 model actions for done action
18:25:46.473 Final step count: 10 steps processed
18:25:46.474 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:46.652 🔍 Found 0 total artifacts for execution
18:25:46.652 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:47.000 🔍 Found 0 screenshot artifacts
18:25:47.000 ❌ No artifact found for step 1 with step_name: step_1
18:25:47.000 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:47.178 🔍 Found 0 total artifacts for execution
18:25:47.179 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:47.526 🔍 Found 0 screenshot artifacts
18:25:47.526 ❌ No artifact found for step 2 with step_name: step_2
18:25:47.527 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:47.701 🔍 Found 0 total artifacts for execution
18:25:47.701 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:48.046 🔍 Found 0 screenshot artifacts
18:25:48.046 ❌ No artifact found for step 3 with step_name: step_3
18:25:48.047 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:48.110 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:48.110   FastAPI arguments
18:25:48.111     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:48.218 🔍 Found 0 total artifacts for execution
18:25:48.218 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:48.567 🔍 Found 0 screenshot artifacts
18:25:48.567 ❌ No artifact found for step 4 with step_name: step_4
18:25:48.568 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:48.735 🔍 Found 0 total artifacts for execution
18:25:48.735 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:49.077 🔍 Found 0 screenshot artifacts
18:25:49.077 ❌ No artifact found for step 5 with step_name: step_5
18:25:49.077 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:49.244 🔍 Found 0 total artifacts for execution
18:25:49.244 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:49.579 🔍 Found 0 screenshot artifacts
18:25:49.580 ❌ No artifact found for step 6 with step_name: step_6
18:25:49.580 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:49.750 🔍 Found 0 total artifacts for execution
18:25:49.750 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:50.091 🔍 Found 0 screenshot artifacts
18:25:50.091 ❌ No artifact found for step 7 with step_name: step_7
18:25:50.092 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:50.262 🔍 Found 0 total artifacts for execution
18:25:50.262 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:50.604 🔍 Found 0 screenshot artifacts
18:25:50.604 ❌ No artifact found for step 8 with step_name: step_8
18:25:50.605 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:50.774 🔍 Found 0 total artifacts for execution
18:25:50.774 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:51.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:51.109   FastAPI arguments
18:25:51.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:51.112 🔍 Found 0 screenshot artifacts
18:25:51.112 ❌ No artifact found for step 9 with step_name: step_9
18:25:51.113 🔍 Searching for artifacts with execution_id: 40ec54d8-1695-4148-8910-949d65b85586
18:25:51.284 🔍 Found 0 total artifacts for execution
18:25:51.284 🔍 Searching with query: {'execution_id': '40ec54d8-1695-4148-8910-949d65b85586', 'type': 'screenshot'}
18:25:51.631 🔍 Found 0 screenshot artifacts
18:25:51.631 ❌ No artifact found for step 10 with step_name: step_10
18:25:51.631 Agent completed successfully based on final_result: The user authentication feature for the scenario 'Inicio de sesión exitoso con credenciales válidas' has been successfully verified. The user was able to log in with the email '<EMAIL>' and password 'admin123', and upon login, the dashboard page was displayed with relevant system information and a visible 'Login exitoso' notification message. This confirms that the user can log in successfully with valid credentials as expected.
18:25:51.631 Captured 10 raw result items for frontend processing
18:25:51.631 Processing 10 steps to find done action
18:25:51.632 Last step (#10) success: True, action_type: unknown
18:25:51.632 Found success indicator in formatted raw result: Status: ✅ Success
Completion: ✅ Complete
Memory: Task completed: True - The user authentication feat...
18:25:51.632 SUCCESS INDICATOR: Last step successful with completion message - marking as successful
18:25:51.632 SUCCESS DETERMINATION: Using done action success: True
18:25:51.632 SUCCESS DETERMINATION: Done action successful - overriding any intermediate step failures
18:25:51.632 FINAL SUCCESS DETERMINATION: success=True, done_action_success=True, last_step_success=True, agent_completed_successfully=True
18:25:51.632 🤖 Starting AI-powered test analysis...
18:25:51.632 🔍 PRIORITY EXTRACTION: Attempting to extract screenshots directly from history
18:25:51.632 🔍 HISTORY: Attempting to extract screenshots from history.history
18:25:51.633 🔥 SCREENSHOT DEBUG: Keeping original screenshot 1 - user display (compression disabled) (386,109 bytes)
18:25:51.633 ✅ Keeping original screenshot 1 quality - user display (compression disabled) (386,109 bytes)
18:25:51.633 ✅ HISTORY: Extracted screenshot 1 from state (514812 chars)
18:25:51.634 🔥 SCREENSHOT DEBUG: Keeping original screenshot 2 - user display (compression disabled) (390,081 bytes)
18:25:51.634 ✅ Keeping original screenshot 2 quality - user display (compression disabled) (390,081 bytes)
18:25:51.634 ✅ HISTORY: Extracted screenshot 2 from state (520108 chars)
18:25:51.635 🔥 SCREENSHOT DEBUG: Keeping original screenshot 3 - user display (compression disabled) (385,428 bytes)
18:25:51.635 ✅ Keeping original screenshot 3 quality - user display (compression disabled) (385,428 bytes)
18:25:51.635 ✅ HISTORY: Extracted screenshot 3 from state (513904 chars)
18:25:51.636 🔥 SCREENSHOT DEBUG: Keeping original screenshot 4 - user display (compression disabled) (280,173 bytes)
18:25:51.636 ✅ Keeping original screenshot 4 quality - user display (compression disabled) (280,173 bytes)
18:25:51.636 ✅ HISTORY: Extracted screenshot 4 from state (373564 chars)
18:25:51.636 🔥 SCREENSHOT DEBUG: Keeping original screenshot 5 - user display (compression disabled) (248,291 bytes)
18:25:51.636 ✅ Keeping original screenshot 5 quality - user display (compression disabled) (248,291 bytes)
18:25:51.637 ✅ HISTORY: Extracted screenshot 5 from state (331056 chars)
18:25:51.637 🔥 SCREENSHOT DEBUG: Keeping original screenshot 6 - user display (compression disabled) (386,109 bytes)
18:25:51.637 ✅ Keeping original screenshot 6 quality - user display (compression disabled) (386,109 bytes)
18:25:51.637 ✅ HISTORY: Extracted screenshot 6 from state (514812 chars)
18:25:51.638 🔥 SCREENSHOT DEBUG: Keeping original screenshot 7 - user display (compression disabled) (390,396 bytes)
18:25:51.638 ✅ Keeping original screenshot 7 quality - user display (compression disabled) (390,396 bytes)
18:25:51.638 ✅ HISTORY: Extracted screenshot 7 from state (520528 chars)
18:25:51.639 🔥 SCREENSHOT DEBUG: Keeping original screenshot 8 - user display (compression disabled) (390,081 bytes)
18:25:51.639 ✅ Keeping original screenshot 8 quality - user display (compression disabled) (390,081 bytes)
18:25:51.639 ✅ HISTORY: Extracted screenshot 8 from state (520108 chars)
18:25:51.640 🔥 SCREENSHOT DEBUG: Keeping original screenshot 9 - user display (compression disabled) (385,428 bytes)
18:25:51.640 ✅ Keeping original screenshot 9 quality - user display (compression disabled) (385,428 bytes)
18:25:51.640 ✅ HISTORY: Extracted screenshot 9 from state (513904 chars)
18:25:51.641 🔥 SCREENSHOT DEBUG: Keeping original screenshot 10 - user display (compression disabled) (282,257 bytes)
18:25:51.641 ✅ Keeping original screenshot 10 quality - user display (compression disabled) (282,257 bytes)
18:25:51.641 ✅ HISTORY: Extracted screenshot 10 from state (376344 chars)
18:25:51.641 🖼️ HISTORY: Successfully extracted 10 screenshots for AI analysis
18:25:51.641 🔍 ARTIFACTS DEBUG: result.artifacts = Artifacts(screenshots=[], videos=[], logs=[], generated_code=None, history_file=None, gherkin_scenarios=[])
18:25:51.641 🔍 ARTIFACTS DEBUG: artifacts.screenshots = []
18:25:51.641 🔍 ARTIFACTS DEBUG: artifacts.__dict__ = {'screenshots': [], 'videos': [], 'logs': [], 'generated_code': None, 'history_file': None, 'gherkin_scenarios': []}
18:25:51.641 📸 AI Analysis: Prepared 10 screenshots for analysis
18:25:51.641 🔍 Background jobs check: BACKGROUND_JOBS_AVAILABLE=True, USE_BACKGROUND_JOBS=true, use_background_jobs=True
18:25:51.642 🔄 Background jobs available - will process AI analysis asynchronously
18:25:51.642 ✅ JobManager using local Redis: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
18:25:51.643 Created job ai_analysis_005003bf for execution 40ec54d8-1695-4148-8910-949d65b85586
18:25:51.769 🚀 AI analysis job created: ai_analysis_005003bf
18:25:51.769 Processed browser_history result: 40ec54d8-1695-4148-8910-949d65b85586
18:25:51.769 🔍 ORCHESTRATOR DEBUG: Final status: ExecutionStatus.SUCCESS (type: <enum 'ExecutionStatus'>)
18:25:54.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:54.109   FastAPI arguments
18:25:54.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:25:57.108 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:25:57.108   FastAPI arguments
18:25:57.109     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:00.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:00.110   FastAPI arguments
18:26:00.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:03.109 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:03.110   FastAPI arguments
18:26:03.110     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:06.052 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:06.053   FastAPI arguments
18:26:06.053     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:07.073 GET /api/analytics-v2 ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:07.074   FastAPI arguments
18:26:09.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:09.055   FastAPI arguments
18:26:09.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:12.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:12.055   FastAPI arguments
18:26:12.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:15.053 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:15.054   FastAPI arguments
18:26:15.054     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:18.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:18.056   FastAPI arguments
18:26:18.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:20.641 GET /api/analytics-v2 ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:20.642   FastAPI arguments
18:26:21.052 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:21.053   FastAPI arguments
18:26:21.053     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:24.052 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:24.052   FastAPI arguments
18:26:24.052     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:27.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:27.055   FastAPI arguments
18:26:27.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:30.054 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:30.055   FastAPI arguments
18:26:30.055     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56661 - "GET /api/analytics-v2?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:26:30.220 GET /api/analytics ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:30.220   FastAPI arguments
18:26:30.220     ✅ ProjectManagerService: MongoDB mode enabled
18:26:30.221     Service operation: list_projects
INFO:     127.0.0.1:56661 - "GET /api/analytics?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:26:30.555 GET /api/analytics-comparison ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:30.555   FastAPI arguments
18:26:30.555     ✅ ProjectManagerService: MongoDB mode enabled
18:26:30.555     Service operation: list_projects
18:26:33.055 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:33.055   FastAPI arguments
18:26:33.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:36.053 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:36.054   FastAPI arguments
18:26:36.054     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:36.109 ✅ Created document in executions: 6872a8afa9e223c41beb493f
18:26:36.109 💾 Saved execution 40ec54d8-1695-4148-8910-949d65b85586 to the database.
18:26:36.971 ✅ Updated document in projects: 6861fe7bb4804a69aa767eca
18:26:36.971 Added execution 40ec54d8-1695-4148-8910-949d65b85586 to test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 history
18:26:36.972 📊 Updated history for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 with execution 40ec54d8-1695-4148-8910-949d65b85586.
18:26:36.972 Execution 40ec54d8-1695-4148-8910-949d65b85586 status: ExecutionStatus.SUCCESS
18:26:36.972 Execution 40ec54d8-1695-4148-8910-949d65b85586 completed successfully
18:26:36.972 Browser 4ccd57ba-f980-4624-a4f4-73eeb783dec3 contaminated: 
18:26:36.972 Disposed browser 4ccd57ba-f980-4624-a4f4-73eeb783dec3
18:26:36.972 Background execution completed for 40ec54d8-1695-4148-8910-949d65b85586 with status ExecutionStatus.SUCCESS
INFO:     127.0.0.1:56750 - "GET /api/analytics-v2?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:26:39.015 GET /api/analytics ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:39.016   FastAPI arguments
18:26:39.016     ✅ ProjectManagerService: MongoDB mode enabled
18:26:39.017     Service operation: list_projects
18:26:39.053 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:39.054   FastAPI arguments
18:26:39.054     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:39.054     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
INFO:     127.0.0.1:56750 - "GET /api/analytics?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:26:39.354 GET /api/analytics-comparison ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-12'
18:26:39.354   FastAPI arguments
18:26:39.354     ✅ ProjectManagerService: MongoDB mode enabled
18:26:39.354     Service operation: list_projects
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:26:40.585     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:40.585     📋 MongoDB metadata present: True
18:26:40.585     🧠 AI Analysis in MongoDB metadata: False
18:26:40.585     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:41.605 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:41.606   FastAPI arguments
18:26:41.607     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:41.607     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:42.455     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:42.455     📋 MongoDB metadata present: True
18:26:42.455     🧠 AI Analysis in MongoDB metadata: False
18:26:42.455     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:42.603 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:42.604   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:26:42.606 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:42.606   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:26:44.609 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:44.610   FastAPI arguments
18:26:44.610     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:44.610     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:44.611 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:44.611   FastAPI arguments
INFO:     127.0.0.1:56894 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:26:46.016     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:46.016     📋 MongoDB metadata present: True
18:26:46.016     🧠 AI Analysis in MongoDB metadata: False
18:26:46.016     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:46.610 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:46.610   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:26:48.054 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:48.055   FastAPI arguments
18:26:48.056     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:48.056     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:48.610 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:48.611   FastAPI arguments
INFO:     127.0.0.1:56894 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:26:49.468     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:49.469     📋 MongoDB metadata present: True
18:26:49.469     🧠 AI Analysis in MongoDB metadata: False
18:26:49.469     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:50.607 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:50.608   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:26:51.507 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:51.508   FastAPI arguments
18:26:51.508     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:51.508     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:52.610 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:52.610   FastAPI arguments
INFO:     127.0.0.1:56894 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:26:52.889     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:52.890     📋 MongoDB metadata present: True
18:26:52.890     🧠 AI Analysis in MongoDB metadata: False
18:26:52.890     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:54.609 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:54.609   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:26:54.928 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:54.928   FastAPI arguments
18:26:54.929     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:54.929     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:56.294     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:56.294     📋 MongoDB metadata present: True
18:26:56.294     🧠 AI Analysis in MongoDB metadata: False
18:26:56.294     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:26:56.607 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:56.608   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
INFO:     127.0.0.1:56750 - "GET /api/analytics-comparison?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:26:58.335 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:26:58.335   FastAPI arguments
18:26:58.336     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:26:58.336     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:26:58.609 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:26:58.610   FastAPI arguments
INFO:     127.0.0.1:56976 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:26:59.215     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:26:59.215     📋 MongoDB metadata present: True
18:26:59.215     🧠 AI Analysis in MongoDB metadata: False
18:26:59.215     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56661 - "GET /api/analytics-comparison?start_date=2025-06-12&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:27:00.612 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:00.612   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:01.255 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:01.255   FastAPI arguments
18:27:01.256     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:01.256     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:02.413     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:02.413     📋 MongoDB metadata present: True
18:27:02.413     🧠 AI Analysis in MongoDB metadata: False
18:27:02.413     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:02.612 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:02.613   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:04.451 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:04.451   FastAPI arguments
18:27:04.452     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:04.452     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:04.612 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:04.613   FastAPI arguments
INFO:     127.0.0.1:57018 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:27:05.982     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:05.982     📋 MongoDB metadata present: True
18:27:05.982     🧠 AI Analysis in MongoDB metadata: False
18:27:05.982     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:06.614 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:06.615   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:08.024 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:08.025   FastAPI arguments
18:27:08.025     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:08.025     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:08.615 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:08.615   FastAPI arguments
INFO:     127.0.0.1:57018 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:27:09.727     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:09.727     📋 MongoDB metadata present: True
18:27:09.727     🧠 AI Analysis in MongoDB metadata: False
18:27:09.728     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:10.615 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:10.616   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:11.767 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:11.768   FastAPI arguments
18:27:11.768     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:11.768     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:12.617 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:12.617   FastAPI arguments
INFO:     127.0.0.1:57018 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:27:13.475     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:13.475     📋 MongoDB metadata present: True
18:27:13.475     🧠 AI Analysis in MongoDB metadata: False
18:27:13.475     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:14.615 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:14.616   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:15.518 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:15.518   FastAPI arguments
18:27:15.519     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:15.519     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:16.617 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:16.618   FastAPI arguments
INFO:     127.0.0.1:57018 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:27:17.028     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:17.029     📋 MongoDB metadata present: True
18:27:17.029     🧠 AI Analysis in MongoDB metadata: False
18:27:17.029     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:18.616 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:18.616   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:19.063 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:19.063   FastAPI arguments
18:27:19.064     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:19.064     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:20.569     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:20.569     📋 MongoDB metadata present: True
18:27:20.569     🧠 AI Analysis in MongoDB metadata: False
18:27:20.569     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:20.616 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:20.617   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
18:27:22.608 GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
18:27:22.608   FastAPI arguments
18:27:22.609     Found execution 40ec54d8-1695-4148-8910-949d65b85586 in simple tracking with status ExecutionStatus.SUCCESS
18:27:22.609     🔍 Execution 40ec54d8-1695-4148-8910-949d65b85586 is complete, checking MongoDB for latest data including AI analysis
18:27:22.618 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:22.618   FastAPI arguments
INFO:     127.0.0.1:57124 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:27:24.295     ✅ Found updated execution data in MongoDB for 40ec54d8-1695-4148-8910-949d65b85586
18:27:24.295     📋 MongoDB metadata present: True
18:27:24.295     🧠 AI Analysis in MongoDB metadata: False
18:27:24.295     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:55828 - "GET /api/v2/tests/execution/40ec54d8-1695-4148-8910-949d65b85586 HTTP/1.1" 200 OK
18:27:24.619 GET /api/v2/background-jobs/ai_analysis_005003bf/status
18:27:24.619   FastAPI arguments
INFO:     127.0.0.1:55828 - "GET /api/v2/background-jobs/ai_analysis_005003bf/status HTTP/1.1" 200 OK
