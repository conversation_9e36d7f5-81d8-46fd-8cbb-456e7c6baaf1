[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Screenshot 1: 75124 characters of base64 data
[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Screenshot 2: 65236 characters of base64 data
[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Screenshot 3: 63412 characters of base64 data
[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Screenshot 4: 165960 characters of base64 data
[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Step 1: Analyzing with screenshot (75124 chars)
[2025-07-12 14:52:20,800: INFO/MainProcess] 📸 Background Jobs: Compressing screenshot for step 1 AI analysis
[2025-07-12 14:52:20,832: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 56,343 → 17,776 bytes (31.5%) | (1512, 982) → (1200, 779) | Q:55, Max:1200px
[2025-07-12 14:52:20,832: INFO/MainProcess] 📸 Background Jobs: Original: 75124 chars → Compressed: 23704 chars
[2025-07-12 14:52:20,832: INFO/MainProcess] 💰 Step 1: Screenshot available but skipping for cost optimization - text analysis sufficient
[2025-07-12 14:52:20,833: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:20,833: INFO/MainProcess] Trying 💰 PAID PAID model openai/gpt-4o-mini for test_analysis (1/3) [8s timeout]
[2025-07-12 14:52:20,833: INFO/MainProcess] Making request to 💰 PAID PAID model: openai/gpt-4o-mini [8s timeout]
[2025-07-12 14:52:26,524: INFO/MainProcess] ✅ openai/gpt-4o-mini - Tokens: 52252 (PAID)
[2025-07-12 14:52:26,524: INFO/MainProcess] ✅ SUCCESS with 💰 PAID PAID model openai/gpt-4o-mini
[2025-07-12 14:52:26,524: INFO/MainProcess] Success with provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:26,524: INFO/MainProcess] ✅ Analyzed step 1: success
[2025-07-12 14:52:26,524: INFO/MainProcess] 📸 Step 2: Analyzing with screenshot (65236 chars)
[2025-07-12 14:52:26,524: INFO/MainProcess] 📸 Background Jobs: Compressing screenshot for step 2 AI analysis
[2025-07-12 14:52:26,529: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 48,926 → 20,019 bytes (40.9%) | (756, 562) → (756, 562) | Q:55, Max:1200px
[2025-07-12 14:52:26,529: INFO/MainProcess] 📸 Background Jobs: Original: 65236 chars → Compressed: 26692 chars
[2025-07-12 14:52:26,529: INFO/MainProcess] 💰 Step 2: Screenshot available but skipping for cost optimization - text analysis sufficient
[2025-07-12 14:52:26,529: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:26,529: INFO/MainProcess] Trying 💰 PAID PAID model openai/gpt-4o-mini for test_analysis (1/3) [8s timeout]
[2025-07-12 14:52:26,529: INFO/MainProcess] Making request to 💰 PAID PAID model: openai/gpt-4o-mini [8s timeout]
[2025-07-12 14:52:34,107: INFO/MainProcess] ✅ openai/gpt-4o-mini - Tokens: 46333 (PAID)
[2025-07-12 14:52:34,107: INFO/MainProcess] ✅ SUCCESS with 💰 PAID PAID model openai/gpt-4o-mini
[2025-07-12 14:52:34,107: INFO/MainProcess] Success with provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:34,107: INFO/MainProcess] ✅ Analyzed step 2: success
[2025-07-12 14:52:34,107: INFO/MainProcess] 📸 Step 3: Analyzing with screenshot (63412 chars)
[2025-07-12 14:52:34,107: INFO/MainProcess] 📸 Background Jobs: Compressing screenshot for step 3 AI analysis
[2025-07-12 14:52:34,111: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 47,558 → 19,734 bytes (41.5%) | (756, 562) → (756, 562) | Q:55, Max:1200px
[2025-07-12 14:52:34,111: INFO/MainProcess] 📸 Background Jobs: Original: 63412 chars → Compressed: 26312 chars
[2025-07-12 14:52:34,111: INFO/MainProcess] 💰 Step 3: Screenshot available but skipping for cost optimization - text analysis sufficient
[2025-07-12 14:52:34,111: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:34,111: INFO/MainProcess] Trying 💰 PAID PAID model openai/gpt-4o-mini for test_analysis (1/3) [8s timeout]
[2025-07-12 14:52:34,111: INFO/MainProcess] Making request to 💰 PAID PAID model: openai/gpt-4o-mini [8s timeout]
[2025-07-12 14:52:38,837: INFO/MainProcess] ✅ openai/gpt-4o-mini - Tokens: 45155 (PAID)
[2025-07-12 14:52:38,837: INFO/MainProcess] ✅ SUCCESS with 💰 PAID PAID model openai/gpt-4o-mini
[2025-07-12 14:52:38,837: INFO/MainProcess] Success with provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:38,837: INFO/MainProcess] ✅ Analyzed step 3: success
[2025-07-12 14:52:38,837: INFO/MainProcess] 📸 Step 4: Analyzing with screenshot (165960 chars)
[2025-07-12 14:52:38,837: INFO/MainProcess] 📸 Background Jobs: Compressing screenshot for step 4 AI analysis
[2025-07-12 14:52:38,845: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 124,470 → 59,158 bytes (47.5%) | (756, 1653) → (756, 1653) | Q:55, Max:1200px
[2025-07-12 14:52:38,845: INFO/MainProcess] 📸 Background Jobs: Original: 165960 chars → Compressed: 78880 chars
[2025-07-12 14:52:38,845: INFO/MainProcess] 💰 Step 4: Screenshot available but skipping for cost optimization - text analysis sufficient
[2025-07-12 14:52:38,846: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:38,846: INFO/MainProcess] Trying 💰 PAID PAID model openai/gpt-4o-mini for test_analysis (1/3) [8s timeout]
[2025-07-12 14:52:38,846: INFO/MainProcess] Making request to 💰 PAID PAID model: openai/gpt-4o-mini [8s timeout]
[2025-07-12 14:52:47,078: WARNING/MainProcess] ❌ Error with model openai/gpt-4o-mini: Request timeout (8s) - Model overloaded, trying next
[2025-07-12 14:52:47,078: INFO/MainProcess] ⏭️ Model openai/gpt-4o-mini overloaded/timeout, trying next model...
[2025-07-12 14:52:47,078: INFO/MainProcess] Trying 💰 PAID PAID model meta-llama/llama-4-maverick for test_analysis (2/3) [8s timeout]
[2025-07-12 14:52:47,078: INFO/MainProcess] Making request to 💰 PAID PAID model: meta-llama/llama-4-maverick [8s timeout]
[2025-07-12 14:52:47,181: WARNING/MainProcess] ❌ Error with model meta-llama/llama-4-maverick: [Errno 1] [SSL: SSLV3_ALERT_BAD_RECORD_MAC] ssl/tls alert bad record mac (_ssl.c:2590)
[2025-07-12 14:52:47,181: INFO/MainProcess] Trying 💰 PAID PAID model anthropic/claude-3-haiku for test_analysis (3/3) [8s timeout]
[2025-07-12 14:52:47,181: INFO/MainProcess] Making request to 💰 PAID PAID model: anthropic/claude-3-haiku [8s timeout]
[2025-07-12 14:52:47,269: WARNING/MainProcess] ❌ Error with model anthropic/claude-3-haiku: [Errno 1] [SSL: SSLV3_ALERT_BAD_RECORD_MAC] ssl/tls alert bad record mac (_ssl.c:2590)
[2025-07-12 14:52:47,269: ERROR/MainProcess] Failed after trying 3 models for test_analysis (limited to 3 to prevent API blocking). Last error: [Errno 1] [SSL: SSLV3_ALERT_BAD_RECORD_MAC] ssl/tls alert bad record mac (_ssl.c:2590)
[2025-07-12 14:52:47,270: WARNING/MainProcess] Provider 'openrouter' returned unsuccessful response: Failed after trying 3 models for test_analysis (limited to 3 to prevent API blocking). Last error: [Errno 1] [SSL: SSLV3_ALERT_BAD_RECORD_MAC] ssl/tls alert bad record mac (_ssl.c:2590)
[2025-07-12 14:52:47,270: INFO/MainProcess] Trying provider 'gemini' for use case 'test_analysis'
[2025-07-12 14:52:47,275: WARNING/MainProcess] /Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py:483: UserWarning: Convert_system_message_to_human will be deprecated!
  warnings.warn("Convert_system_message_to_human will be deprecated!")

[2025-07-12 14:52:47,275: ERROR/MainProcess] Gemini request failed: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 14:52:47,276: WARNING/MainProcess] Provider 'gemini' returned unsuccessful response: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 14:52:47,276: ERROR/MainProcess] All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 14:52:47,276: ERROR/MainProcess] LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 14:52:47,276: ERROR/MainProcess] Error analyzing step 4: LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
Traceback (most recent call last):
  File "/Users/<USER>/Proyectos/qak/src/services/test_analysis_service.py", line 178, in analyze_step
    raise Exception(f"LLM request failed: {result.get('error', 'Unknown error')}")
Exception: LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 14:52:47,277: INFO/MainProcess] 📸 Final analysis: Using screenshot (165960 chars)
[2025-07-12 14:52:47,277: INFO/MainProcess] 📸 Background Jobs: Compressing final screenshot for completion analysis
[2025-07-12 14:52:47,285: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 124,470 → 59,158 bytes (47.5%) | (756, 1653) → (756, 1653) | Q:55, Max:1200px
[2025-07-12 14:52:47,285: INFO/MainProcess] 📸 Background Jobs: Final screenshot - Original: 165960 chars → Compressed: 78880 chars
[2025-07-12 14:52:47,285: INFO/MainProcess] 📸 Final analysis: Sending screenshot to LLM (78880 chars) - Final state validation
[2025-07-12 14:52:47,286: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:47,286: INFO/MainProcess] Trying 💰 PAID PAID model openai/gpt-4o-mini for test_analysis (1/2) [8s timeout]
[2025-07-12 14:52:47,286: INFO/MainProcess] Making request to 💰 PAID PAID model: openai/gpt-4o-mini [8s timeout]
[2025-07-12 14:52:52,641: INFO/MainProcess] ✅ openai/gpt-4o-mini - Tokens: 50517 (PAID)
[2025-07-12 14:52:52,642: INFO/MainProcess] ✅ SUCCESS with 💰 PAID PAID model openai/gpt-4o-mini
[2025-07-12 14:52:52,642: INFO/MainProcess] Success with provider 'openrouter' for use case 'test_analysis'
[2025-07-12 14:52:52,642: INFO/MainProcess] ✅ Analyzed test completion: PASS
[2025-07-12 14:52:52,642: INFO/MainProcess] ✅ Completed full test analysis: PASS
[2025-07-12 14:52:52,644: INFO/MainProcess] Stored result for job ai_analysis_cf13ee31
[2025-07-12 14:52:52,644: INFO/MainProcess] 🔍 Analysis result structure: ['execution_id', 'analysis_timestamp', 'step_analyses', 'completion_analysis', 'summary']
[2025-07-12 14:52:52,644: INFO/MainProcess] 🔍 Has completion_analysis: True
[2025-07-12 14:52:52,644: INFO/MainProcess] 🔍 Has step_analyses: True
[2025-07-12 14:52:52,644: INFO/MainProcess] 🔍 Has summary: True
[2025-07-12 14:52:52,644: INFO/MainProcess] 🔍 Updating execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 with data keys: ['step_analyses', 'completion_analysis', 'summary', 'analysis_timestamp']
[2025-07-12 14:52:52,645: INFO/MainProcess] 🔧 Database configuration initialized for development environment
[2025-07-12 14:52:52,645: INFO/MainProcess] 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
[2025-07-12 14:52:52,645: INFO/MainProcess] 🔧 Database manager initialized
[2025-07-12 14:52:52,645: INFO/MainProcess] 🔌 Connecting to MongoDB
[2025-07-12 14:52:54,693: INFO/MainProcess] ✅ Connected to MongoDB database: qak_dev
[2025-07-12 14:52:54,693: INFO/MainProcess] 📊 MongoDB Server Version: 8.0.11
[2025-07-12 14:52:55,731: INFO/MainProcess] ✅ Updated execution by execution_id: c4810e32-1864-4515-9d40-e0e9f8ada6f0
[2025-07-12 14:52:55,731: INFO/MainProcess] ✅ Updated execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 with analysis results in MongoDB
[2025-07-12 14:52:55,732: INFO/MainProcess] Successfully completed analysis for job ai_analysis_cf13ee31
[2025-07-12 14:52:55,732: INFO/MainProcess] Task analyze_test_background[329198b3-e27d-4920-be95-e29b21ddf090] succeeded in 34.96654295800181s: {'execution_id': 'c4810e32-1864-4515-9d40-e0e9f8ada6f0', 'analysis_timestamp': '2025-07-12T17:52:52.642330', 'step_analyses': [{'step_number': 1, 'technical_status': 'success', 'functional_status': 'success', 'overall_assessment': 'success', 'confidence_score': 0.95, 'validation_analysis': {...}, 'issues_detected': [...], 'positive_indicators': [...], 'summary': 'Navegué a la página de inicio de sesión y ingresé mi dirección de correo electrónico \'<EMAIL>\' y la contraseña \'admin123\'. Hice clic en el botón de inicio de sesión y observé que la página se redirigió correctamente al panel de control, confirmando que el inicio de sesión fue exitoso.', 'analyzed_at': '2025-07-12T17:52:26.524933', 'analyzer_version': '1.0.0', 'step_id': '1_unknown'}, {'step_number': 2, 'technical_status': 'success', 'functional_status': 'success', 'overall_assessment': 'success', 'confidence_score': 0.95, 'validation_analysis': {...}, 'issues_detected': [...], 'positive_indicators': [...], 'summary': 'Logré llenar el campo...', ...}]}