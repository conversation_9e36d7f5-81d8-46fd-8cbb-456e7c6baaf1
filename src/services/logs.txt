18:11:31.137   - ReDoc: http://localhost:8000/redoc
18:11:31.138 
Presiona Ctrl+C para detener el servidor.
INFO:     Started server process [48417]
INFO:     Waiting for application startup.
18:11:31.503 🔧 Database configuration initialized for development environment
18:11:31.503 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
18:11:31.503 🔧 Database manager initialized
18:11:31.503 🔌 Connecting to MongoDB
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
18:11:33.434 ✅ Connected to MongoDB database: qak_dev
18:11:33.435 📊 MongoDB Server Version: 8.0.11
18:11:33.435 🚀 Database initialization successful
18:11:33.435 📊 Database indexes will be managed by Beanie ODM models
18:11:33.435 🔧 ODM manager initialized
18:11:33.436 📝 Registered ODM model: Project
18:11:33.436 📝 Registered ODM model: Execution
18:11:33.436 📝 Registered ODM model: CodegenSession
18:11:33.436 📝 Registered ODM model: Artifact
18:11:33.436 📝 Registered ODM model: BrowserConfiguration
18:11:33.436 📝 Registered ODM model: BrowserSessionPool
18:11:33.436 📝 Registered ODM model: BrowserUseEventDocument
18:11:33.437 📝 Registered ODM model: BrowserUseSessionDocument
18:11:33.437 📝 Registered ODM model: BrowserUseTaskDocument
18:11:33.437 📝 Registered ODM model: BrowserUseStepDocument
18:11:33.437 📝 Registered ODM model: BrowserUseFileDocument
18:11:33.610 🚀 Initializing Beanie ODM with 11 models...
18:11:33.610 🧹 Cleaning up potentially conflicting indexes...
18:11:33.954 🗑️ Dropped conflicting index: projects.project_id_1
18:11:34.130 🗑️ Dropped conflicting index: projects.created_at_-1
18:11:34.484 🗑️ Dropped conflicting index: executions.execution_id_1
18:11:34.828 🗑️ Dropped conflicting index: codegen_sessions.session_id_1
18:11:35.178 🗑️ Dropped conflicting index: artifacts.artifact_id_1
18:11:35.179 ✅ Index cleanup completed
18:11:41.423 ✅ Beanie ODM initialization successful
18:11:41.423   📝 Project -> projects
18:11:41.423   📝 Execution -> executions
18:11:41.424   📝 CodegenSession -> codegen_sessions
18:11:41.424   📝 Artifact -> artifacts
18:11:41.424   📝 BrowserConfiguration -> browser_configurations
18:11:41.424   📝 BrowserSessionPool -> browser_session_pools
18:11:41.424   📝 BrowserUseEventDocument -> browser_use_events
18:11:41.424   📝 BrowserUseSessionDocument -> browser_use_sessions
18:11:41.425   📝 BrowserUseTaskDocument -> browser_use_tasks
18:11:41.425   📝 BrowserUseStepDocument -> browser_use_steps
18:11:41.425   📝 BrowserUseFileDocument -> browser_use_files
18:11:41.768 Predefined configuration already exists: test_case
18:11:41.944 Predefined configuration already exists: smoke
18:11:42.119 Predefined configuration already exists: exploration
18:11:42.295 Predefined configuration already exists: exploration_deep
18:11:42.468 Predefined configuration already exists: test_suite
18:11:42.974 ✅ ProjectManagerService: MongoDB mode enabled
18:11:42.975 Service operation: list_projects
18:11:42.977 BrowserPool initialized (min: 2, max: 10)
18:11:42.977 PerformanceMonitor initialized (interval: 30s)
18:11:42.977 ExecutionOrchestrator initialized
18:11:42.978 🔄 Creating shared global EnhancedArtifactCollector instance
18:11:42.978 ✅ Initialized r2 storage backend for artifacts
18:11:42.978 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
18:11:42.978 EnhancedArtifactCollector initialized with CloudflareR2StorageBackend cloud storage backend (R2-ONLY MODE)
18:11:42.979 Loaded existing artifacts from storage
18:11:42.979 ArtifactCollector initialized successfully
18:11:42.979 🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud
18:11:42.979 ✅ Shared global EnhancedArtifactCollector instance initialized
18:11:42.979 Execution orchestrator initialized with ExecutionRepository.
INFO:     Application startup complete.
18:11:42.980 Loading active/stale sessions from database...
18:11:42.981 Cleaning up old, unfinished sessions from database...
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
18:11:44.071 Processed 0 stale sessions from database.
18:11:44.292 No old, unfinished sessions to clean up.
18:11:45.041 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:45.055   FastAPI arguments
18:11:45.055 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:11:45.056   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
18:11:45.057     ✅ ProjectManagerService: MongoDB mode enabled
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
               FastAPI arguments
18:11:45.057     ✅ ProjectManagerService: MongoDB mode enabled
18:11:45.385     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:45.385     Service operation: list_projects
18:11:45.386     Service operation: list_projects
18:11:46.064     ✅ Project found: Web Agent Page
18:11:46.064     ✅ Test suite found: Funcionalidad core
18:11:46.064     ✅ Test case found: Login
18:11:46.064     ✅ Test case found: Login
INFO:     127.0.0.1:51623 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:11:46.067 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:46.067   FastAPI arguments
18:11:46.067     ✅ ProjectManagerService: MongoDB mode enabled
18:11:46.068     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:46.068     Service operation: list_projects
18:11:46.215     ✅ Project found: Web Agent Page
18:11:46.215     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:11:46.215     📝 Has environments attr: True
18:11:46.215     📝 Environments count: 2
INFO:     127.0.0.1:51624 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:11:46.216 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:11:46.217   FastAPI arguments
18:11:46.217     ✅ ProjectManagerService: MongoDB mode enabled
18:11:46.218     Service operation: list_projects
18:11:46.414     ✅ Project found: Web Agent Page
18:11:46.415     ✅ Test suite found: Funcionalidad core
18:11:46.415     ✅ Test case found: Login
18:11:46.415     ✅ Test case found: Login
INFO:     127.0.0.1:51623 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:11:46.416 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:46.416   FastAPI arguments
18:11:46.417     ✅ ProjectManagerService: MongoDB mode enabled
18:11:46.417     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:46.417     Service operation: list_projects
18:11:46.420 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:11:46.420   FastAPI arguments
18:11:46.421     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:46.421     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:11:46.421     ✅ Orchestrator and execution repository initialized
18:11:46.577     ✅ Project found: Web Agent Page
18:11:46.577     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:11:46.578     📝 Has environments attr: True
18:11:46.578     📝 Environments count: 2
INFO:     127.0.0.1:51630 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:11:46.580 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:11:46.581   FastAPI arguments
18:11:46.581     ✅ ProjectManagerService: MongoDB mode enabled
18:11:46.582     Service operation: list_projects
18:11:46.762     ✅ Project found: Web Agent Page
18:11:46.762     ✅ Test suite found: Funcionalidad core
18:11:46.763     ✅ Test case found: Login
18:11:46.763     ✅ Test case found: Login
INFO:     127.0.0.1:51624 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:11:46.923     ✅ Project found: Web Agent Page
18:11:46.923     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:11:46.923     📝 Has environments attr: True
18:11:46.923     📝 Environments count: 2
INFO:     127.0.0.1:51630 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:11:50.506     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:50.506     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:51623 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:11:50.518 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:11:50.520   FastAPI arguments
18:11:50.521     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:50.521     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:11:50.521     ✅ Orchestrator and execution repository initialized
18:11:50.577 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:11:50.578   FastAPI arguments
18:11:50.578     ✅ ProjectManagerService: MongoDB mode enabled
18:11:50.579     Service operation: list_projects
18:11:51.245     ✅ Project found: Web Agent Page
18:11:51.245     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:11:51.245     📝 Has environments attr: True
18:11:51.245     📝 Environments count: 2
INFO:     127.0.0.1:51623 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:11:53.561     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:53.562     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:11:54.324 POST /api/v2/tests/execute
18:11:54.325   FastAPI arguments
18:11:54.326     🔧 BROWSER CONFIG: Resolving configuration for execution type case
18:11:54.326     🔧 BROWSER CONFIG: MongoDB query filters: {'execution_types': {'$in': ['case']}, 'is_active': True}
18:11:54.492     Found 1 configurations for execution type: case
18:11:54.492       Config 1: Test Case (Default) - execution_types: ['case', 'full'] - model_name: None
18:11:54.493     Ordered configurations by priority - OpenRouter configs first
18:11:54.493       1. Test Case (Default) (provider: openrouter, usage: 0)
18:11:54.493     🔧 BROWSER CONFIG: Found DB config 'Test Case (Default)' for type case
18:11:54.493     🔧 BROWSER CONFIG: MongoDB settings: model_provider=openrouter, model_name=None, headless=True
18:11:54.493     🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: openai/gpt-4.1-mini
18:11:54.493     🔧 BROWSER CONFIG: DB config failed (BrowserConfig.__init__() got an unexpected keyword argument 'capture_beyond_viewport'), using hardcoded fallback
18:11:54.493     🔧 BROWSER CONFIG: Applied hardcoded fallback for type case with headless=True
18:11:54.493     ✅ ProjectManagerService: MongoDB mode enabled
18:11:54.494     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:11:54.494     Service operation: list_projects
18:11:54.835     ✅ Project found: Web Agent Page
18:11:54.835     ✅ Test suite found: Funcionalidad core
18:11:54.835     ✅ Test case found: Login
18:11:55.182     🔍 EXECUTION DEBUG: Environment name resolved: QA
18:11:55.182     Created execution context b9b7165d-57df-4ec2-ab62-bf8aeff6c01d for TestType.CASE
18:11:55.183     🔍 CONTEXT DEBUG: No application_version provided or empty
18:11:55.183     Registering execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in orchestrator.active_executions
18:11:55.183     Orchestrator instance ID: 5114837328
18:11:55.183     Active executions before: []
18:11:55.183     Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d status: ExecutionStatus.RUNNING
18:11:55.183     Active executions after: ['b9b7165d-57df-4ec2-ab62-bf8aeff6c01d']
18:11:55.183     Successfully registered b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:11:55.183     Simple tracking has: ['b9b7165d-57df-4ec2-ab62-bf8aeff6c01d']
18:11:55.184     Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d started and returned immediately
INFO:     127.0.0.1:51630 - "POST /api/v2/tests/execute HTTP/1.1" 200 OK
18:11:55.184 Background execution started for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:11:55.184 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d status: ExecutionStatus.RUNNING
18:11:55.553 Using specified environment: QA
18:11:55.553 🔍 ORCHESTRATOR DEBUG: Resolved environment: Environment(name='QA', base_url='https://web-agent-playground.lovable.app', is_default=True)
18:11:55.553 🔍 ORCHESTRATOR DEBUG: Environment details - ID: 079f669c-ea2c-43f4-bf76-0a98897b1353, Name: QA
18:11:55.553 Using absolute URL: https://web-agent-playground.lovable.app
18:11:55.553 🔍 ORCHESTRATOR DEBUG: Constructed URL: https://web-agent-playground.lovable.app
18:11:55.553 Environment info set for execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d: QA -> https://web-agent-playground.lovable.app
18:11:55.553 🔍 ORCHESTRATOR DEBUG: Environment info set in context
18:11:55.553 BrowserPool initialized successfully
18:11:55.554 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:11:55.554   - headless: True
18:11:55.554   - model_provider: openrouter
18:11:55.554   - model_name: openai/gpt-4.1-mini
18:11:55.554   - highlight_elements: False
18:11:55.554   - temperature: 0.1
18:11:55.554 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:11:55.554 🔍 BROWSER POOL: Added disable_security=False from MongoDB config
18:11:55.554 🔍 BROWSER POOL: Added enable_memory=False from MongoDB config
18:11:55.554 🔍 BROWSER POOL: Added generate_gif=True from MongoDB config
18:11:55.555 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:11:55.555 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:11:55.555 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:11:55.555 🔍 BROWSER POOL: Added max_failures=3 from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added model_name=openai/gpt-4.1-mini from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added model_provider=openrouter from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added retry_delay=5 from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:11:55.556 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:11:55.557 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:11:55.557 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:11:55.557 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:11:55.557 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:11:55.557 🔍 BROWSER POOL: Ensured critical field headless=True
18:11:55.557 🔍 BROWSER POOL: Ensured critical field disable_security=False
18:11:55.557 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:11:55.557 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'model_name', 'model_provider', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:11:55.557 🔍 BROWSER POOL: headless value being passed: True
18:11:55.557 🔍 BROWSER POOL: model_provider=openrouter, model_name=openai/gpt-4.1-mini
18:11:55.558 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:11:55.558 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=openrouter
18:11:55.608 Created browser eee833db-f745-4eef-b6dd-9bb785b0a82c with config hash 20b966c0
18:11:55.609 Created new browser eee833db-f745-4eef-b6dd-9bb785b0a82c for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:11:55.609 Executing with strategy: TestCaseStrategy
18:11:55.609 Executing test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 using TestCaseStrategy.
18:11:55.609 Created initial actions to navigate to: https://web-agent-playground.lovable.app
18:11:55.609 🔧 STRATEGY CONFIG: Using resolved config from context: headless=True, max_steps=50
18:11:55.609 🔧 STRATEGY CONFIG: Created agent config with max_steps=50 from DB config
18:11:55.609 Creating OpenRouter LLM with model: openai/gpt-4.1-mini
18:11:55.609 Using provided Gherkin scenario for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO     [browser_use.agent.service] 💾 File system path: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browser_use_agent_a28326ad
INFO     [browser_use.Agent🅰 26ad on 🆂 26ad 🅟 84] 🧠 Starting a browser-use agent 0.5.4 with base_model=openai/gpt-4.1-mini +vision extraction_model=openai/gpt-4.1-mini +file_system
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 84] 🚀 Starting task: Caracterstica: Autenticacin de Usuario

Escenario: Inicio de sesin exitoso con credenciales vlidas
  Dado que el usuario est en la pgina de inicio de sesin
  Cuando el usuario ingresa "<EMAIL>" como nombre de usuario y "admin123" como contrasea
  Y el usuario intenta iniciar sesin
  Entonces el usuario debera iniciar sesin exitosamente
18:11:55.940 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51686 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:11:55.948 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51688 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:None #08] 🎭 Launching new local browser playwright:chromium keep_alive=False user_data_dir= ~/.config/browseruse/profiles/default
WARNING  [browser_use.BrowserSession🆂 cdcb:None #08] ⚠️ SingletonLock conflict detected. Profile at ~/.config/browseruse/profiles/default is locked. Using temporary profile instead: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-e3zicfha
INFO     [browser_use.BrowserSession🆂 cdcb:None #08]  ↳ Spawning Chrome subprocess listening on CDP http://127.0.0.1:51689/ with user_data_dir= /private/var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-e3zicfha
18:11:58.243 OPTIONS /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
INFO:     127.0.0.1:51630 - "OPTIONS /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:11:58.246 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:11:58.246   FastAPI arguments
18:11:58.247     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] 🌎 Connecting to newly spawned browser via CDP http://127.0.0.1:51689/ -> browser_pid=48509 (local)
INFO     [browser_use.controller.service] 🔗  Opened new tab #1 with url https://web-agent-playground.lovable.app
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: go_to_url()
18:12:01.244 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:01.245   FastAPI arguments
18:12:01.245     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 2: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:12:04.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:04.244   FastAPI arguments
18:12:04.245     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:07.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:07.246   FastAPI arguments
18:12:07.246     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The user request describes a test scenario for user authentication with a successful login using valid credentials. The current page is a login form with fields for email and password, already pre-filled with the valid credentials "<EMAIL>" and "admin123" as placeholders. The next step is to simulate the user action of clicking the "Iniciar Sesión" (Login) button to attempt login. After clicking, I will need to verify if the login was successful by checking the page response or any confirmation message.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 👍 Eval: The login page with input fields for email and password and the login button is fully loaded and visible. Verdict: Success
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The login page is loaded with the correct placeholders for email and password matching the test credentials. The login button is visible and ready to be clicked.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login with the provided valid credentials.

18:12:08.297 🧠 openai/gpt-4.1-mini | 🆕 2.5k + 💾 4.7k | 📤 197
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 2: Ran 1 actions in 7.29s: ✅ 1
18:12:08.541 POST /api/browser-use/v1/api/v1/oauth/device/authorize
18:12:08.542 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51800 - "POST /api/browser-use/v1/api/v1/oauth/device/authorize HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51801 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
WARNING  [browser_use.sync.auth] Cloud sync authentication endpoint not found (404). Check your BROWSER_USE_CLOUD_API_URL setting.
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 3: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:12:10.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:10.244   FastAPI arguments
18:12:10.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:13.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:13.246   FastAPI arguments
18:12:13.247     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The previous step clicked the 'Iniciar Sesión' button without filling in the email input field, which caused a validation error indicating the email field is required. Now, I need to input the email '<EMAIL>' into the email field and the password 'admin123' into the password field before clicking the login button again to simulate a successful login attempt as per the user request.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ⚠️ Eval: Clicked the login button without filling the email field, resulting in a validation error. Verdict: Failure to login due to missing email input.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The login page is loaded with visible email and password input fields and the login button. The password field is filled with 'admin123' but the email field is empty, causing a validation error when trying to login. Need to input the email and then attempt login again.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Input '<EMAIL>' into the email field and ensure 'admin123' is in the password field, then click the 'Iniciar Sesión' button to attempt a successful login.

18:12:14.260 🧠 openai/gpt-4.1-mini | 🆕 1.1k + 💾 4.6k | 📤 226
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 3: Ran 1 actions in 6.09s: ✅ 1
18:12:14.635 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51832 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 4: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:12:16.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:16.244   FastAPI arguments
18:12:16.245     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:19.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:19.246   FastAPI arguments
18:12:19.246     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The email field is already filled with '<EMAIL>' and the password field shows 'admin123' as a placeholder, which likely means it is filled or ready to be filled. The next step is to click the 'Iniciar Sesión' button to attempt a successful login as per the user request scenario. This will test the login functionality with valid credentials.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 👍 Eval: Successfully input the email '<EMAIL>' into the email field. Verdict: Success
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The login page is loaded with the email field filled with '<EMAIL>' and the password field showing placeholder 'admin123'. Ready to attempt login by clicking the 'Iniciar Sesión' button.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login with the provided valid credentials.

18:12:20.619 🧠 openai/gpt-4.1-mini | 🆕 1.2k + 💾 4.6k | 📤 192
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 4: Ran 1 actions in 6.16s: ✅ 1
18:12:20.795 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51862 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 5: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:12:22.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:22.246   FastAPI arguments
18:12:22.246     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:25.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:25.243   FastAPI arguments
18:12:25.245     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:25.560 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:12:25.560   - headless: True
18:12:25.560   - model_provider: None
18:12:25.560   - model_name: None
18:12:25.560   - highlight_elements: False
18:12:25.561   - temperature: 0.1
18:12:25.561 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:12:25.561 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
18:12:25.561 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
18:12:25.561 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
18:12:25.561 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:12:25.561 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:12:25.562 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:12:25.563 🔍 BROWSER POOL: Ensured critical field headless=True
18:12:25.563 🔍 BROWSER POOL: Ensured critical field disable_security=True
18:12:25.564 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:12:25.564 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:12:25.564 🔍 BROWSER POOL: headless value being passed: True
18:12:25.564 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
18:12:25.565 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:12:25.565 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
18:12:25.615 Created browser c1cb3d0e-928b-4dc0-b9e2-9eba4947e719 with config hash 20b966c0
18:12:25.615 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:12:25.615   - headless: True
18:12:25.615   - model_provider: None
18:12:25.615   - model_name: None
18:12:25.616   - highlight_elements: False
18:12:25.616   - temperature: 0.1
18:12:25.616 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:12:25.616 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
18:12:25.616 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
18:12:25.616 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
18:12:25.616 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:12:25.616 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:12:25.617 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:12:25.618 🔍 BROWSER POOL: Ensured critical field headless=True
18:12:25.619 🔍 BROWSER POOL: Ensured critical field disable_security=True
18:12:25.619 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:12:25.619 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:12:25.619 🔍 BROWSER POOL: headless value being passed: True
18:12:25.619 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
18:12:25.619 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:12:25.620 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
18:12:25.671 Created browser 893c5e5b-8006-4cce-b8d5-d88930b92239 with config hash 20b966c0
18:12:28.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:28.246   FastAPI arguments
18:12:28.247     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:31.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:31.243   FastAPI arguments
18:12:31.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The current page shows the login form with the email field already filled with '<EMAIL>' and the password field showing the placeholder 'admin123'. However, the password field is not filled yet, which is likely why the previous login attempt failed. To complete the login scenario as per the user request, I need to input the password 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt a successful login. This matches the scenario steps: user inputs valid credentials and attempts login, expecting success.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ⚠️ Eval: The login button was clicked with the email field filled but the password field was empty, causing a validation error and failed login attempt. Verdict: Failure due to missing password input.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The email field is filled with '<EMAIL>'. The password field is currently empty but shows placeholder 'admin123'. The login button is visible and ready to be clicked after filling the password.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Input 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt a successful login.

18:12:31.430 🧠 openai/gpt-4.1-mini | 🆕 1.1k + 💾 4.9k | 📤 237
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 5: Ran 1 actions in 11.09s: ✅ 1
18:12:31.887 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51914 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 6: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:12:34.242 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:34.243   FastAPI arguments
18:12:34.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:37.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:37.244   FastAPI arguments
18:12:37.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The email and password fields are correctly filled with '<EMAIL>' and 'admin123' respectively, and the 'Iniciar Sesión' button is visible and ready to be clicked. The next logical step is to click the 'Iniciar Sesión' button to attempt a successful login, as per the user request scenario for successful authentication with valid credentials.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 👍 Eval: The password was successfully input into the password field, preparing the form for submission. Verdict: Success
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The login form is fully filled with valid credentials: email '<EMAIL>' and password 'admin123'. The login button is ready to be clicked to attempt login.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login with the provided valid credentials and verify successful login.

18:12:37.835 🧠 openai/gpt-4.1-mini | 🆕 1.1k + 💾 5.0k | 📤 173
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 6: Ran 1 actions in 6.12s: ✅ 1
18:12:38.011 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:51978 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 7: Evaluating page with 20 interactive elements on: https://web-agent-playground.lovable.app/
18:12:40.245 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:40.246   FastAPI arguments
18:12:40.246     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:43.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:43.244   FastAPI arguments
18:12:43.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The login was successful as evidenced by the dashboard page being displayed and the presence of a notification message 'Login exitoso - Bienvenido al sistema de testing'. This matches the expected outcome in the user request scenario for a successful login with valid credentials. The user is now on the dashboard page, confirming the authentication flow works correctly.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 👍 Eval: Successfully logged in with valid credentials and reached the dashboard page with a welcome message. Verdict: Success
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The user has successfully logged in with email '<EMAIL>' and password 'admin123'. The dashboard page is visible with a welcome notification confirming login success.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Confirm the presence of the welcome message and dashboard elements to fully validate the successful login scenario.

18:12:45.762 🧠 openai/gpt-4.1-mini | 🆕 2.5k + 💾 5.1k | 📤 207
18:12:46.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:46.244   FastAPI arguments
18:12:46.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:49.045 🧠 openai/gpt-4.1-mini | 📥 314 | 📤 125
INFO     [browser_use.controller.service] 📄 Page Link: https://web-agent-playground.lovable.app/
Query: Extract the login success message and dashboard summary information to confirm successful login
Extracted Content:
```json
{
  "login_success_message": "success",
  "dashboard_summary": {
    "Usuarios Totales": "2,543",
    "Crecimiento Usuarios": "+12% desde el mes pasado",
    "Ventas del Mes": "$45,231",
    "Crecimiento Ventas": "+23% desde el mes pasado",
    "Pedidos": "1,234",
    "Cambio Pedidos": "-3% desde el mes pasado",
    "Conversión": "12.5%",
    "Crecimiento Conversión": "+5% desde el mes pasado"
  }
}
```
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: extract_structured_data()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 7: Ran 1 actions in 11.05s: ✅ 1
18:12:49.056 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:52057 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:12:49.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:49.244   FastAPI arguments
18:12:49.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 8: Evaluating page with 18 interactive elements on: https://web-agent-playground.lovable.app/
18:12:52.247 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:52.247   FastAPI arguments
18:12:52.248     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:55.246 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:55.247   FastAPI arguments
18:12:55.248     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 💡 Thinking:
The user request describes a feature and scenario for user authentication with a successful login using valid credentials. The previous steps show that the login was performed successfully with the provided credentials ('<EMAIL>' and 'admin123'), and the dashboard page loaded with a welcome message and system summary. This confirms the scenario 'Inicio de sesión exitoso con credenciales válidas' is met. The dashboard elements and welcome message are visible, confirming successful login. The next step is to finalize the task by confirming the scenario is validated and reporting the success.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 👍 Eval: Successfully extracted and confirmed the login success message and dashboard summary, validating the successful login scenario.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🧠 Memory: The user has successfully logged in with the specified credentials and reached the dashboard page showing key metrics and recent activity, confirming the login feature works as expected.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 🎯 Next goal: Complete the task by reporting the successful login scenario validation to the user.

18:12:57.613 🧠 openai/gpt-4.1-mini | 🆕 2.6k + 💾 5.2k | 📤 302
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ☑️ Executed action 1/1: done()
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📄 Result: The user authentication feature with the scenario 'Inicio de sesión exitoso con credenciales válidas' has been successfully validated. The user was able to log in with the email '<EMAIL>' and password 'admin123', and the dashboard page loaded with the expected welcome message and system summary metrics. This confirms the login functionality works correctly with valid credentials.
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] 📍 Step 8: Ran 1 actions in 8.67s: ✅ 1
INFO     [browser_use.Agent🅰 26ad on 🆂 cdcb 🅟 40] ✅ Task completed successfully
18:12:57.720 📊 Per-Model Usage Breakdown:
18:12:57.720   🤖 openai/gpt-4.1-mini: 48.3k tokens | ⬅️ 46.6k | ➡️ 1.7k | 📞 8 calls | 📈 6.0k/call
18:12:57.730 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:52102 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:12:57.743 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:52104 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08] 🛑 Closing cdp_url=http://127.0.0.1:51689/ browser context  <Browser type=<BrowserType name=chromium executable_path=/Users/<USER>/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium> version=138.0.7204.23>
INFO     [browser_use.BrowserSession🆂 cdcb:51689 #08]  ↳ Killing browser_pid=48509 ~/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium (terminate() called)
18:12:58.124 Processed 7 screenshots from history (artifacts already created by collector)
18:12:58.125 🗂️  Retrieved 0 artifacts for execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:58.125 Processing 7 history items from history.history
18:12:58.126 [STEP EXTRACT] Processing step 1: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.127 [STEP EXTRACT] Successfully processed step 1: action_type=unknown, success=True
18:12:58.127 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.128 [STEP EXTRACT] Processing step 2: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.128 [STEP EXTRACT] Successfully processed step 2: action_type=unknown, success=True
18:12:58.128 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.128 [STEP EXTRACT] Processing step 3: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.128 [STEP EXTRACT] Successfully processed step 3: action_type=unknown, success=True
18:12:58.129 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.129 [STEP EXTRACT] Processing step 4: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.129 [STEP EXTRACT] Successfully processed step 4: action_type=unknown, success=True
18:12:58.129 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.129 [STEP EXTRACT] Processing step 5: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.129 [STEP EXTRACT] Successfully processed step 5: action_type=unknown, success=True
18:12:58.130 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.130 [STEP EXTRACT] Processing step 6: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.130 [STEP EXTRACT] Successfully processed step 6: action_type=unknown, success=True
18:12:58.130 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.130 [STEP EXTRACT] Processing step 7: type=<class 'browser_use.agent.views.AgentHistory'>
18:12:58.130 [STEP EXTRACT] Successfully processed step 7: action_type=unknown, success=True
18:12:58.130 [STEP EXTRACT] Traceback: NoneType: None

18:12:58.131 Checking 7 model actions for done action
18:12:58.131 Final step count: 7 steps processed
18:12:58.131 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:58.243 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:58.244   FastAPI arguments
18:12:58.244     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:12:59.213 🔍 Found 0 total artifacts for execution
18:12:59.213 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:12:59.555 🔍 Found 0 screenshot artifacts
18:12:59.555 ❌ No artifact found for step 1 with step_name: step_1
18:12:59.555 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:12:59.728 🔍 Found 0 total artifacts for execution
18:12:59.728 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:00.067 🔍 Found 0 screenshot artifacts
18:13:00.067 ❌ No artifact found for step 2 with step_name: step_2
18:13:00.067 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:00.241 🔍 Found 0 total artifacts for execution
18:13:00.241 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:00.582 🔍 Found 0 screenshot artifacts
18:13:00.583 ❌ No artifact found for step 3 with step_name: step_3
18:13:00.583 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:00.757 🔍 Found 0 total artifacts for execution
18:13:00.757 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:01.105 🔍 Found 0 screenshot artifacts
18:13:01.105 ❌ No artifact found for step 4 with step_name: step_4
18:13:01.105 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:01.244 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:01.244   FastAPI arguments
18:13:01.245     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:01.276 🔍 Found 0 total artifacts for execution
18:13:01.277 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:01.618 🔍 Found 0 screenshot artifacts
18:13:01.619 ❌ No artifact found for step 5 with step_name: step_5
18:13:01.619 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:01.786 🔍 Found 0 total artifacts for execution
18:13:01.786 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:02.126 🔍 Found 0 screenshot artifacts
18:13:02.126 ❌ No artifact found for step 6 with step_name: step_6
18:13:02.127 🔍 Searching for artifacts with execution_id: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:02.300 🔍 Found 0 total artifacts for execution
18:13:02.300 🔍 Searching with query: {'execution_id': 'b9b7165d-57df-4ec2-ab62-bf8aeff6c01d', 'type': 'screenshot'}
18:13:02.642 🔍 Found 0 screenshot artifacts
18:13:02.643 ❌ No artifact found for step 7 with step_name: step_7
18:13:02.643 Agent completed successfully based on final_result: The user authentication feature with the scenario 'Inicio de sesión exitoso con credenciales válidas' has been successfully validated. The user was able to log in with the email '<EMAIL>' and password 'admin123', and the dashboard page loaded with the expected welcome message and system summary metrics. This confirms the login functionality works correctly with valid credentials.
18:13:02.643 Captured 7 raw result items for frontend processing
18:13:02.643 Processing 7 steps to find done action
18:13:02.643 Last step (#7) success: True, action_type: unknown
18:13:02.644 Found success indicator in formatted raw result: Status: ✅ Success
Completion: ✅ Complete
Memory: Task completed: True - The user authentication feat...
18:13:02.644 SUCCESS INDICATOR: Last step successful with completion message - marking as successful
18:13:02.644 SUCCESS DETERMINATION: Using done action success: True
18:13:02.644 SUCCESS DETERMINATION: Done action successful - overriding any intermediate step failures
18:13:02.644 FINAL SUCCESS DETERMINATION: success=True, done_action_success=True, last_step_success=True, agent_completed_successfully=True
18:13:02.644 🤖 Starting AI-powered test analysis...
18:13:02.644 🔍 PRIORITY EXTRACTION: Attempting to extract screenshots directly from history
18:13:02.645 🔍 HISTORY: Attempting to extract screenshots from history.history
18:13:02.645 ✅ Keeping original screenshot 1 quality - small file (56,343 bytes)
18:13:02.645 ✅ HISTORY: Extracted screenshot 1 from state (75124 chars)
18:13:02.645 ✅ Keeping original screenshot 2 quality - small file (52,653 bytes)
18:13:02.645 ✅ HISTORY: Extracted screenshot 2 from state (70204 chars)
18:13:02.645 ✅ Keeping original screenshot 3 quality - small file (48,926 bytes)
18:13:02.646 ✅ HISTORY: Extracted screenshot 3 from state (65236 chars)
18:13:02.646 ✅ Keeping original screenshot 4 quality - small file (50,941 bytes)
18:13:02.646 ✅ HISTORY: Extracted screenshot 4 from state (67924 chars)
18:13:02.646 ✅ Keeping original screenshot 5 quality - small file (47,558 bytes)
18:13:02.646 ✅ HISTORY: Extracted screenshot 5 from state (63412 chars)
18:13:02.647 ✅ Keeping original screenshot 6 quality - small file (122,697 bytes)
18:13:02.647 ✅ HISTORY: Extracted screenshot 6 from state (163596 chars)
18:13:02.647 ✅ Keeping original screenshot 7 quality - small file (121,829 bytes)
18:13:02.647 ✅ HISTORY: Extracted screenshot 7 from state (162440 chars)
18:13:02.647 🖼️ HISTORY: Successfully extracted 7 screenshots for AI analysis
18:13:02.648 🔍 ARTIFACTS DEBUG: result.artifacts = Artifacts(screenshots=[], videos=[], logs=[], generated_code=None, history_file=None, gherkin_scenarios=[])
18:13:02.648 🔍 ARTIFACTS DEBUG: artifacts.screenshots = []
18:13:02.648 🔍 ARTIFACTS DEBUG: artifacts.__dict__ = {'screenshots': [], 'videos': [], 'logs': [], 'generated_code': None, 'history_file': None, 'gherkin_scenarios': []}
18:13:02.648 📸 AI Analysis: Prepared 7 screenshots for analysis
18:13:02.648 🔍 Background jobs check: BACKGROUND_JOBS_AVAILABLE=True, USE_BACKGROUND_JOBS=true, use_background_jobs=True
18:13:02.648 🔄 Background jobs available - will process AI analysis asynchronously
18:13:02.649 ✅ JobManager using local Redis: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
18:13:02.650 Created job ai_analysis_df62dda2 for execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:02.721 🚀 AI analysis job created: ai_analysis_df62dda2
18:13:02.721 Processed browser_history result: b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:02.721 🔍 ORCHESTRATOR DEBUG: Final status: ExecutionStatus.SUCCESS (type: <enum 'ExecutionStatus'>)
18:13:04.247 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:04.248   FastAPI arguments
18:13:04.249     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:04.790 ✅ Created document in executions: 6872a5ae97b36c98284546e4
18:13:04.791 💾 Saved execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d to the database.
18:13:05.643 ✅ Updated document in projects: 6861fe7bb4804a69aa767eca
18:13:05.643 Added execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d to test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 history
18:13:05.643 📊 Updated history for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 with execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d.
18:13:05.644 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d status: ExecutionStatus.SUCCESS
18:13:05.644 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d completed successfully
18:13:05.644 Browser eee833db-f745-4eef-b6dd-9bb785b0a82c contaminated: 
18:13:05.644 Disposed browser eee833db-f745-4eef-b6dd-9bb785b0a82c
18:13:05.644 Background execution completed for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d with status ExecutionStatus.SUCCESS
18:13:07.244 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:07.245   FastAPI arguments
18:13:07.246     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:07.246     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:08.086     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:08.086     📋 MongoDB metadata present: True
18:13:08.086     🧠 AI Analysis in MongoDB metadata: False
18:13:08.086     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:09.207 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:09.208   FastAPI arguments
18:13:09.209     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:09.209     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:09.875     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:09.875     📋 MongoDB metadata present: True
18:13:09.876     🧠 AI Analysis in MongoDB metadata: False
18:13:09.876     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:51630 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:09.972 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:09.974   FastAPI arguments
INFO:     127.0.0.1:51630 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:09.989 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:09.990   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:11.977 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:11.978   FastAPI arguments
18:13:11.978     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:11.978     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:11.979 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:11.980   FastAPI arguments
INFO:     127.0.0.1:51630 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:13.004     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:13.005     📋 MongoDB metadata present: True
18:13:13.005     🧠 AI Analysis in MongoDB metadata: False
18:13:13.005     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:13.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:13.981   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:15.022 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:15.023   FastAPI arguments
18:13:15.023     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:15.023     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:15.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:15.981   FastAPI arguments
INFO:     127.0.0.1:51630 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:16.030     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:16.030     📋 MongoDB metadata present: True
18:13:16.031     🧠 AI Analysis in MongoDB metadata: False
18:13:16.031     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:17.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:17.980   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:18.043 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:18.043   FastAPI arguments
18:13:18.044     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:18.044     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:19.049     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:19.049     📋 MongoDB metadata present: True
18:13:19.049     🧠 AI Analysis in MongoDB metadata: False
18:13:19.049     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:19.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:19.980   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:21.064 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:21.064   FastAPI arguments
18:13:21.065     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:21.065     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:21.981 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:21.982   FastAPI arguments
INFO:     127.0.0.1:52265 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:22.071     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:22.071     📋 MongoDB metadata present: True
18:13:22.072     🧠 AI Analysis in MongoDB metadata: False
18:13:22.072     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:23.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:23.981   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:24.088 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:24.089   FastAPI arguments
18:13:24.089     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:24.089     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:25.112     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:25.112     📋 MongoDB metadata present: True
18:13:25.112     🧠 AI Analysis in MongoDB metadata: False
18:13:25.112     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:25.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:25.981   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:27.126 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:27.127   FastAPI arguments
18:13:27.128     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:27.128     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:27.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:27.980   FastAPI arguments
INFO:     127.0.0.1:52298 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:28.123     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:28.123     📋 MongoDB metadata present: True
18:13:28.123     🧠 AI Analysis in MongoDB metadata: False
18:13:28.123     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:29.981 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:29.982   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:30.140 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:30.141   FastAPI arguments
18:13:30.144     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:30.145     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:31.175     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:31.176     📋 MongoDB metadata present: True
18:13:31.176     🧠 AI Analysis in MongoDB metadata: False
18:13:31.176     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:31.978 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:31.979   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:33.197 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:33.197   FastAPI arguments
18:13:33.198     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:33.198     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:33.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:33.981   FastAPI arguments
INFO:     127.0.0.1:52329 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:34.207     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:34.208     📋 MongoDB metadata present: True
18:13:34.208     🧠 AI Analysis in MongoDB metadata: False
18:13:34.208     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:35.978 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:35.979   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:36.230 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:36.231   FastAPI arguments
18:13:36.232     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:36.232     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:37.265     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:37.265     📋 MongoDB metadata present: True
18:13:37.265     🧠 AI Analysis in MongoDB metadata: False
18:13:37.265     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
18:13:37.980 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:37.982   FastAPI arguments
INFO:     127.0.0.1:52174 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
18:13:39.279 GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:39.280   FastAPI arguments
18:13:39.282     Found execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d in simple tracking with status ExecutionStatus.SUCCESS
18:13:39.282     🔍 Execution b9b7165d-57df-4ec2-ab62-bf8aeff6c01d is complete, checking MongoDB for latest data including AI analysis
18:13:39.978 GET /api/v2/background-jobs/ai_analysis_df62dda2/status
18:13:39.979   FastAPI arguments
INFO:     127.0.0.1:52358 - "GET /api/v2/background-jobs/ai_analysis_df62dda2/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
18:13:40.290     ✅ Found updated execution data in MongoDB for b9b7165d-57df-4ec2-ab62-bf8aeff6c01d
18:13:40.290     📋 MongoDB metadata present: True
18:13:40.290     🧠 AI Analysis in MongoDB metadata: False
18:13:40.291     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:52174 - "GET /api/v2/tests/execution/b9b7165d-57df-4ec2-ab62-bf8aeff6c01d HTTP/1.1" 200 OK
