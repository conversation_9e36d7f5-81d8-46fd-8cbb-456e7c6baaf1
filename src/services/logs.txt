INFO:     127.0.0.1:63791 - "GET /api/v2/background-jobs/ai_analysis_833cb5a5/result HTTP/1.1" 200 OK
17:39:33.135 GET /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640
17:39:33.136   FastAPI arguments
17:39:33.136     Found execution c0edb58b-30fb-49e8-b288-baa5dc07f640 in simple tracking with status ExecutionStatus.SUCCESS
17:39:33.136     🔍 Execution c0edb58b-30fb-49e8-b288-baa5dc07f640 is complete, checking MongoDB for latest data including AI analysis
17:39:33.804     ✅ Found updated execution data in MongoDB for c0edb58b-30fb-49e8-b288-baa5dc07f640
17:39:33.804     📋 MongoDB metadata present: True
17:39:33.804     🧠 AI Analysis in MongoDB metadata: True
17:39:33.804     📊 AI Analysis Status in MongoDB metadata: completed
INFO:     127.0.0.1:63791 - "GET /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640 HTTP/1.1" 200 OK
17:51:09.934 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
17:51:09.939 BrowserPool initialized (min: 2, max: 10)
17:51:09.942 PerformanceMonitor initialized (interval: 30s)
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
✅ Browser-use logging configured from /Users/<USER>/Proyectos/qak/libs
INFO     [browser_use.telemetry.service] Anonymized telemetry enabled. See https://docs.browser-use.com/development/telemetry for more information.
17:51:10.729 ✅ Using explicit REDIS_URL: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
17:51:10.866 Iniciando API web de QA Agent en http://0.0.0.0:8000...
17:51:10.867 Documentacion API disponible en:
17:51:10.867   - Swagger UI: http://localhost:8000/docs
17:51:10.867   - ReDoc: http://localhost:8000/redoc
17:51:10.867 
Presiona Ctrl+C para detener el servidor.
INFO:     Started server process [44861]
INFO:     Waiting for application startup.
17:51:11.255 🔧 Database configuration initialized for development environment
17:51:11.255 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
17:51:11.255 🔧 Database manager initialized
17:51:11.256 🔌 Connecting to MongoDB
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
17:51:13.152 ✅ Connected to MongoDB database: qak_dev
17:51:13.153 📊 MongoDB Server Version: 8.0.11
17:51:13.154 🚀 Database initialization successful
17:51:13.154 📊 Database indexes will be managed by Beanie ODM models
17:51:13.155 🔧 ODM manager initialized
17:51:13.155 📝 Registered ODM model: Project
17:51:13.155 📝 Registered ODM model: Execution
17:51:13.156 📝 Registered ODM model: CodegenSession
17:51:13.156 📝 Registered ODM model: Artifact
17:51:13.156 📝 Registered ODM model: BrowserConfiguration
17:51:13.156 📝 Registered ODM model: BrowserSessionPool
17:51:13.156 📝 Registered ODM model: BrowserUseEventDocument
17:51:13.156 📝 Registered ODM model: BrowserUseSessionDocument
17:51:13.157 📝 Registered ODM model: BrowserUseTaskDocument
17:51:13.157 📝 Registered ODM model: BrowserUseStepDocument
17:51:13.157 📝 Registered ODM model: BrowserUseFileDocument
17:51:13.325 🚀 Initializing Beanie ODM with 11 models...
17:51:13.326 🧹 Cleaning up potentially conflicting indexes...
17:51:13.666 🗑️ Dropped conflicting index: projects.project_id_1
17:51:13.840 🗑️ Dropped conflicting index: projects.created_at_-1
17:51:14.180 🗑️ Dropped conflicting index: executions.execution_id_1
17:51:14.533 🗑️ Dropped conflicting index: codegen_sessions.session_id_1
17:51:14.870 🗑️ Dropped conflicting index: artifacts.artifact_id_1
17:51:14.871 ✅ Index cleanup completed
17:51:21.162 ✅ Beanie ODM initialization successful
17:51:21.163   📝 Project -> projects
17:51:21.163   📝 Execution -> executions
17:51:21.163   📝 CodegenSession -> codegen_sessions
17:51:21.163   📝 Artifact -> artifacts
17:51:21.163   📝 BrowserConfiguration -> browser_configurations
17:51:21.163   📝 BrowserSessionPool -> browser_session_pools
17:51:21.163   📝 BrowserUseEventDocument -> browser_use_events
17:51:21.163   📝 BrowserUseSessionDocument -> browser_use_sessions
17:51:21.163   📝 BrowserUseTaskDocument -> browser_use_tasks
17:51:21.164   📝 BrowserUseStepDocument -> browser_use_steps
17:51:21.164   📝 BrowserUseFileDocument -> browser_use_files
17:51:21.503 Predefined configuration already exists: test_case
17:51:21.669 Predefined configuration already exists: smoke
17:51:21.845 Predefined configuration already exists: exploration
17:51:22.018 Predefined configuration already exists: exploration_deep
17:51:22.188 Predefined configuration already exists: test_suite
17:51:22.702 ✅ ProjectManagerService: MongoDB mode enabled
17:51:22.703 Service operation: list_projects
17:51:22.703 BrowserPool initialized (min: 2, max: 10)
17:51:22.703 PerformanceMonitor initialized (interval: 30s)
17:51:22.703 ExecutionOrchestrator initialized
17:51:22.704 🔄 Creating shared global EnhancedArtifactCollector instance
17:51:22.704 ✅ Initialized r2 storage backend for artifacts
17:51:22.704 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
17:51:22.704 EnhancedArtifactCollector initialized with CloudflareR2StorageBackend cloud storage backend (R2-ONLY MODE)
17:51:22.704 Loaded existing artifacts from storage
17:51:22.704 ArtifactCollector initialized successfully
17:51:22.704 🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud
17:51:22.704 ✅ Shared global EnhancedArtifactCollector instance initialized
17:51:22.704 Execution orchestrator initialized with ExecutionRepository.
INFO:     Application startup complete.
17:51:22.706 Loading active/stale sessions from database...
17:51:22.706 Cleaning up old, unfinished sessions from database...
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
17:51:23.395 OPTIONS /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640
17:51:23.398 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:64940 - "OPTIONS /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640 HTTP/1.1" 200 OK
INFO:     127.0.0.1:64941 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
17:51:23.400 GET /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640
17:51:23.406   FastAPI arguments
17:51:23.407     Getting execution status for c0edb58b-30fb-49e8-b288-baa5dc07f640
17:51:23.407     Orchestrator instance ID: 5165928720
17:51:23.407     Has active_executions attr: True
17:51:23.407     Active executions: []
17:51:23.407     Execution c0edb58b-30fb-49e8-b288-baa5dc07f640 not found in active executions, checking saved results
17:51:23.408 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:23.408   FastAPI arguments
17:51:23.409     ✅ ProjectManagerService: MongoDB mode enabled
17:51:23.684     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:23.685     Service operation: list_projects
17:51:23.774 Processed 0 stale sessions from database.
17:51:23.980 No old, unfinished sessions to clean up.
             GET /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
17:51:24.066     Found saved execution c0edb58b-30fb-49e8-b288-baa5dc07f640
17:51:24.066     🔍 Returning saved execution status for c0edb58b-30fb-49e8-b288-baa5dc07f640
17:51:24.066     📊 Saved result type: <class 'src.models.standard_result.StandardResult'>
17:51:24.066     📋 Saved metadata present: True
17:51:24.066     🧠 AI Analysis in saved metadata: True
17:51:24.066     📊 AI Analysis Status in saved metadata: completed
17:51:24.067     📋 Saved result dict keys: ['execution_id', 'test_type', 'test_id', 'suite_id', 'project_id', 'status', 'started_at', 'completed_at', 'start_time', 'end_time', 'duration_ms', 'summary', 'steps', 'errors', 'artifacts', 'configuration', 'success', 'message', 'error', 'raw_data', 'raw_result', 'metadata']
17:51:24.067     🧠 AI Analysis in saved result dict: True
INFO:     127.0.0.1:64940 - "GET /api/v2/tests/execution/c0edb58b-30fb-49e8-b288-baa5dc07f640 HTTP/1.1" 200 OK
17:51:25.266     ✅ Project found: Web Agent Page
17:51:25.266     ✅ Test suite found: Funcionalidad core
17:51:25.267     ✅ Test case found: Login
17:51:25.267     ✅ Test case found: Login
INFO:     127.0.0.1:64943 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
17:51:32.802 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:32.804   FastAPI arguments
17:51:32.805 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
17:51:32.805 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
17:51:32.805     ✅ ProjectManagerService: MongoDB mode enabled
INFO:     127.0.0.1:64965 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
17:51:32.807     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:32.808     Service operation: list_projects
INFO:     127.0.0.1:64967 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
17:51:32.810 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
17:51:32.810   FastAPI arguments
17:51:32.811     ✅ ProjectManagerService: MongoDB mode enabled
17:51:32.811     Service operation: list_projects
17:51:33.491     ✅ Project found: Web Agent Page
17:51:33.491     ✅ Test suite found: Funcionalidad core
17:51:33.492     ✅ Test case found: Login
17:51:33.492     ✅ Test case found: Login
INFO:     127.0.0.1:64963 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
17:51:33.493 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:33.495   FastAPI arguments
17:51:33.495 OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
17:51:33.496     ✅ ProjectManagerService: MongoDB mode enabled
INFO:     127.0.0.1:64963 - "OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
17:51:33.497     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:33.497     Service operation: list_projects
17:51:33.498 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
17:51:33.498   FastAPI arguments
17:51:33.499     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:33.499     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
17:51:33.499     ✅ Orchestrator and execution repository initialized
17:51:33.658     ✅ Project found: Web Agent Page
17:51:33.658     📝 Project type: <class 'src.utilities.project_manager.Project'>
17:51:33.658     📝 Has environments attr: True
17:51:33.658     📝 Environments count: 2
INFO:     127.0.0.1:64967 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
17:51:33.660 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
17:51:33.660   FastAPI arguments
17:51:33.661     ✅ ProjectManagerService: MongoDB mode enabled
17:51:33.661     Service operation: list_projects
17:51:33.838     ✅ Project found: Web Agent Page
17:51:33.838     ✅ Test suite found: Funcionalidad core
17:51:33.839     ✅ Test case found: Login
17:51:33.839     ✅ Test case found: Login
INFO:     127.0.0.1:64969 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
17:51:34.013     ✅ Project found: Web Agent Page
17:51:34.013     📝 Project type: <class 'src.utilities.project_manager.Project'>
17:51:34.013     📝 Has environments attr: True
17:51:34.013     📝 Environments count: 2
INFO:     127.0.0.1:64967 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
17:51:34.014 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
17:51:34.015   FastAPI arguments
17:51:34.015     ✅ ProjectManagerService: MongoDB mode enabled
17:51:34.015     Service operation: list_projects
17:51:34.359     ✅ Project found: Web Agent Page
17:51:34.360     📝 Project type: <class 'src.utilities.project_manager.Project'>
17:51:34.360     📝 Has environments attr: True
17:51:34.360     📝 Environments count: 2
INFO:     127.0.0.1:64969 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
17:51:37.902     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:37.902     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:64963 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
17:51:37.906 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
17:51:37.906   FastAPI arguments
17:51:37.907     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:37.907     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
17:51:37.907     ✅ Orchestrator and execution repository initialized
17:51:37.961 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
17:51:37.961   FastAPI arguments
17:51:37.962     ✅ ProjectManagerService: MongoDB mode enabled
17:51:37.962     Service operation: list_projects
17:51:38.635     ✅ Project found: Web Agent Page
17:51:38.635     📝 Project type: <class 'src.utilities.project_manager.Project'>
17:51:38.635     📝 Has environments attr: True
17:51:38.635     📝 Environments count: 2
INFO:     127.0.0.1:64981 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
17:51:42.176     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:42.177     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:64963 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
17:51:43.286 OPTIONS /api/v2/tests/execute
INFO:     127.0.0.1:64963 - "OPTIONS /api/v2/tests/execute HTTP/1.1" 200 OK
17:51:43.288 POST /api/v2/tests/execute
17:51:43.289   FastAPI arguments
17:51:43.289     🔧 BROWSER CONFIG: Resolving configuration for execution type case
17:51:43.290     🔧 BROWSER CONFIG: MongoDB query filters: {'execution_types': {'$in': ['case']}, 'is_active': True}
17:51:43.462     Found 1 configurations for execution type: case
17:51:43.463       Config 1: Test Case (Default) - execution_types: ['case', 'full'] - model_name: None
17:51:43.463     Ordered configurations by priority - OpenRouter configs first
17:51:43.463       1. Test Case (Default) (provider: openrouter, usage: 0)
17:51:43.463     🔧 BROWSER CONFIG: Found DB config 'Test Case (Default)' for type case
17:51:43.463     🔧 BROWSER CONFIG: MongoDB settings: model_provider=openrouter, model_name=None, headless=True
17:51:43.463     🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: openai/gpt-4.1-mini
17:51:43.463     🔧 BROWSER CONFIG: DB config failed (BrowserConfig.__init__() got an unexpected keyword argument 'capture_beyond_viewport'), using hardcoded fallback
17:51:43.463     🔧 BROWSER CONFIG: Applied hardcoded fallback for type case with headless=True
17:51:43.463     ✅ ProjectManagerService: MongoDB mode enabled
17:51:43.463     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
17:51:43.464     Service operation: list_projects
17:51:43.818     ✅ Project found: Web Agent Page
17:51:43.818     ✅ Test suite found: Funcionalidad core
17:51:43.818     ✅ Test case found: Login
17:51:44.163     🔍 EXECUTION DEBUG: Environment name resolved: QA
17:51:44.163     Created execution context c4810e32-1864-4515-9d40-e0e9f8ada6f0 for TestType.CASE
17:51:44.163     🔍 CONTEXT DEBUG: Setting application_version to: 1.1.1
17:51:44.164     Application version set for execution c4810e32-1864-4515-9d40-e0e9f8ada6f0: v1.1.1
17:51:44.164     Registering execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in orchestrator.active_executions
17:51:44.164     Orchestrator instance ID: 5165928720
17:51:44.164     Active executions before: []
17:51:44.164     Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 status: ExecutionStatus.RUNNING
17:51:44.164     Active executions after: ['c4810e32-1864-4515-9d40-e0e9f8ada6f0']
17:51:44.164     Successfully registered c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:44.164     Simple tracking has: ['c4810e32-1864-4515-9d40-e0e9f8ada6f0']
17:51:44.164     Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 started and returned immediately
INFO:     127.0.0.1:64985 - "POST /api/v2/tests/execute HTTP/1.1" 200 OK
17:51:44.165 Background execution started for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:44.165 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 status: ExecutionStatus.RUNNING
17:51:44.511 Using specified environment: QA
17:51:44.512 🔍 ORCHESTRATOR DEBUG: Resolved environment: Environment(name='QA', base_url='https://web-agent-playground.lovable.app', is_default=True)
17:51:44.512 🔍 ORCHESTRATOR DEBUG: Environment details - ID: 079f669c-ea2c-43f4-bf76-0a98897b1353, Name: QA
17:51:44.512 Using absolute URL: https://web-agent-playground.lovable.app
17:51:44.512 🔍 ORCHESTRATOR DEBUG: Constructed URL: https://web-agent-playground.lovable.app
17:51:44.512 Environment info set for execution c4810e32-1864-4515-9d40-e0e9f8ada6f0: QA -> https://web-agent-playground.lovable.app
17:51:44.512 🔍 ORCHESTRATOR DEBUG: Environment info set in context
17:51:44.513 BrowserPool initialized successfully
17:51:44.513 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
17:51:44.513   - headless: True
17:51:44.513   - model_provider: openrouter
17:51:44.513   - model_name: openai/gpt-4.1-mini
17:51:44.513   - highlight_elements: False
17:51:44.513   - temperature: 0.1
17:51:44.514 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added disable_security=False from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added enable_memory=False from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added generate_gif=True from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added headless=True from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added max_failures=3 from MongoDB config
17:51:44.514 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added model_name=openai/gpt-4.1-mini from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added model_provider=openrouter from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added overrides={} from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added retry_delay=5 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added stealth=False from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
17:51:44.515 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
17:51:44.516 🔍 BROWSER POOL: Ensured critical field headless=True
17:51:44.516 🔍 BROWSER POOL: Ensured critical field disable_security=False
17:51:44.516 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
17:51:44.516 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'model_name', 'model_provider', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
17:51:44.516 🔍 BROWSER POOL: headless value being passed: True
17:51:44.516 🔍 BROWSER POOL: model_provider=openrouter, model_name=openai/gpt-4.1-mini
17:51:44.516 🔍 BROWSER POOL: BrowserProfile created - headless=True
17:51:44.517 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=openrouter
17:51:44.567 Created browser 99ae2801-402f-4993-a164-5969101b3f13 with config hash 20b966c0
17:51:44.567 Created new browser 99ae2801-402f-4993-a164-5969101b3f13 for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:44.567 Executing with strategy: TestCaseStrategy
17:51:44.567 Executing test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 using TestCaseStrategy.
17:51:44.567 Created initial actions to navigate to: https://web-agent-playground.lovable.app
17:51:44.567 🔧 STRATEGY CONFIG: Using resolved config from context: headless=True, max_steps=50
17:51:44.568 🔧 STRATEGY CONFIG: Created agent config with max_steps=50 from DB config
17:51:44.568 Creating OpenRouter LLM with model: openai/gpt-4.1-mini
17:51:44.568 Using provided Gherkin scenario for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO     [browser_use.agent.service] 💾 File system path: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browser_use_agent_90a2e7a8
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e7a8 🅟 16] 🧠 Starting a browser-use agent 0.5.4 with base_model=openai/gpt-4.1-mini +vision extraction_model=openai/gpt-4.1-mini +file_system
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 16] 🚀 Starting task: Caracterstica: Autenticacin de Usuario

Escenario: Inicio de sesin exitoso con credenciales vlidas
  Dado que el usuario est en la pgina de inicio de sesin
  Cuando el usuario ingresa "<EMAIL>" como nombre de usuario y "admin123" como contrasea
  Y el usuario intenta iniciar sesin
  Entonces el usuario debera iniciar sesin exitosamente
17:51:44.817 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:64989 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
17:51:44.826 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:64991 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e3ed:None #24] 🎭 Launching new local browser playwright:chromium keep_alive=False user_data_dir= ~/.config/browseruse/profiles/default
WARNING  [browser_use.BrowserSession🆂 e3ed:None #24] ⚠️ SingletonLock conflict detected. Profile at ~/.config/browseruse/profiles/default is locked. Using temporary profile instead: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-4y3ib7px
INFO     [browser_use.BrowserSession🆂 e3ed:None #24]  ↳ Spawning Chrome subprocess listening on CDP http://127.0.0.1:64994/ with user_data_dir= /private/var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-4y3ib7px
17:51:46.469 GET /api/v1/events
17:51:46.469   FastAPI arguments
17:51:46.470 GET /api/v1/stats
17:51:46.471   FastAPI arguments
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] 🌎 Connecting to newly spawned browser via CDP http://127.0.0.1:64994/ -> browser_pid=44967 (local)
INFO     [browser_use] {"timestamp":"2025-07-12T17:51:47.665467Z","level":"INFO","message":"Consultando 100 eventos (total: 128)","event_type":"info","correlation_id":"997ae2da-9257-4dc7-9bbb-caf8df791b77","metadata":{"filters":{},"limit":100,"skip":0,"results_count":100,"total_count":128}}
INFO:     127.0.0.1:65003 - "GET /api/v1/events HTTP/1.1" 200 OK
17:51:48.079 OPTIONS /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
INFO:     127.0.0.1:65003 - "OPTIONS /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:51:48.081 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:48.081   FastAPI arguments
17:51:48.082     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65003 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use] {"timestamp":"2025-07-12T17:51:49.617488Z","level":"INFO","message":"Consultando estadísticas: 128 eventos totales","event_type":"info","correlation_id":"434981c2-3181-4f49-9ba3-51d7fabf0170","metadata":{"filters":{},"total_events":128,"total_sessions":40,"total_tasks":39}}
INFO:     127.0.0.1:65004 - "GET /api/v1/stats HTTP/1.1" 200 OK
INFO     [browser_use.controller.service] 🔗  Opened new tab #1 with url https://web-agent-playground.lovable.app
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ☑️ Executed action 1/1: go_to_url()
17:51:51.078 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:51.079   FastAPI arguments
17:51:51.079     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65004 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 2: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
17:51:54.079 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:54.080   FastAPI arguments
17:51:54.081     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65004 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 💡 Thinking:
The user request is to test the user authentication feature with a successful login scenario using valid credentials: email "<EMAIL>" and password "admin123". The current page is a login form with input fields for email and password, and a button labeled "Iniciar Sesión" (Login). The email and password fields already have the correct placeholder values matching the test credentials. The next step is to input the email and password into their respective fields and then click the login button to attempt login. After that, I will need to verify if the login was successful by checking for any confirmation or redirection.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 👍 Eval: The login page with input fields and login button is loaded successfully, ready for input.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🧠 Memory: The login page is loaded with placeholders for email and password matching the test credentials. The login button is visible and ready to be clicked after input.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🎯 Next goal: Input the email "<EMAIL>" into the email field, input the password "admin123" into the password field, and then click the login button to attempt login.

17:51:57.074 🧠 openai/gpt-4.1-mini | 🆕 2.8k + 💾 4.5k | 📤 232
17:51:57.179 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:51:57.179   FastAPI arguments
17:51:57.180     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65043 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 2: Ran 1 actions in 7.22s: ✅ 1
17:51:57.411 POST /api/browser-use/v1/api/v1/events
17:51:57.412 POST /api/browser-use/v1/api/v1/oauth/device/authorize
INFO:     127.0.0.1:65046 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:65047 - "POST /api/browser-use/v1/api/v1/oauth/device/authorize HTTP/1.1" 404 Not Found
WARNING  [browser_use.sync.auth] Cloud sync authentication endpoint not found (404). Check your BROWSER_USE_CLOUD_API_URL setting.
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 3: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
17:52:00.080 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:00.080   FastAPI arguments
17:52:00.080     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65049 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:03.079 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:03.080   FastAPI arguments
17:52:03.080     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65049 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 💡 Thinking:
The email field has been successfully filled with '<EMAIL>'. The next step is to input the password 'admin123' into the password field, which is the input element with index 4. After that, I will click the 'Iniciar Sesión' button (index 6) to attempt login. This follows the user request scenario for successful login with valid credentials.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 👍 Eval: Successfully input the email '<EMAIL>' into the email field. Verdict: Success
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🧠 Memory: The email field is filled with '<EMAIL>'. The password field and login button are visible and ready for interaction.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🎯 Next goal: Input the password 'admin123' into the password field (index 4) and then click the 'Iniciar Sesión' button (index 6) to attempt login.

17:52:03.346 🧠 openai/gpt-4.1-mini | 🆕 1.2k + 💾 4.5k | 📤 182
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 3: Ran 1 actions in 6.25s: ✅ 1
17:52:03.661 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:65052 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 4: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
17:52:06.082 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:06.082   FastAPI arguments
17:52:06.083     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65055 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:09.082 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:09.082   FastAPI arguments
17:52:09.083     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65055 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 💡 Thinking:
The email and password fields have been successfully filled with the valid credentials '<EMAIL>' and 'admin123'. The next step is to click the 'Iniciar Sesión' button (index 6) to attempt login and verify if the user can log in successfully as per the scenario described in the user request.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 👍 Eval: Successfully input the password 'admin123' into the password field. Verdict: Success
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🧠 Memory: The login form is filled with valid credentials: email '<EMAIL>' and password 'admin123'. The login button 'Iniciar Sesión' is visible and ready to be clicked.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login and verify successful user authentication.

17:52:09.502 🧠 openai/gpt-4.1-mini | 🆕 1.1k + 💾 4.7k | 📤 162
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 4: Ran 1 actions in 6.00s: ✅ 1
17:52:09.661 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:65057 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 5: Evaluating page with 19 interactive elements on: https://web-agent-playground.lovable.app/
17:52:12.079 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:12.080   FastAPI arguments
17:52:12.080     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65060 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:14.518 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
17:52:14.518   - headless: True
17:52:14.518   - model_provider: None
17:52:14.518   - model_name: None
17:52:14.519   - highlight_elements: False
17:52:14.519   - temperature: 0.1
17:52:14.519 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added headless=True from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
17:52:14.519 🔍 BROWSER POOL: Added overrides={} from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added stealth=False from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
17:52:14.520 🔍 BROWSER POOL: Ensured critical field headless=True
17:52:14.520 🔍 BROWSER POOL: Ensured critical field disable_security=True
17:52:14.520 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
17:52:14.520 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
17:52:14.520 🔍 BROWSER POOL: headless value being passed: True
17:52:14.520 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
17:52:14.521 🔍 BROWSER POOL: BrowserProfile created - headless=True
17:52:14.521 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
17:52:14.572 Created browser 33d7a5ee-3bd1-4cf7-b33c-073416c09880 with config hash 20b966c0
17:52:14.573 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
17:52:14.573   - headless: True
17:52:14.573   - model_provider: None
17:52:14.573   - model_name: None
17:52:14.574   - highlight_elements: False
17:52:14.574   - temperature: 0.1
17:52:14.574 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added headless=True from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
17:52:14.574 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added overrides={} from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added stealth=False from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
17:52:14.575 🔍 BROWSER POOL: Ensured critical field headless=True
17:52:14.575 🔍 BROWSER POOL: Ensured critical field disable_security=True
17:52:14.576 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
17:52:14.576 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
17:52:14.576 🔍 BROWSER POOL: headless value being passed: True
17:52:14.576 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
17:52:14.576 🔍 BROWSER POOL: BrowserProfile created - headless=True
17:52:14.576 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
17:52:14.627 Created browser 380a94e5-6a76-4929-9b40-fbd314d335d9 with config hash 20b966c0
17:52:15.080 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:15.081   FastAPI arguments
17:52:15.081     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65064 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:18.079 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:18.080   FastAPI arguments
17:52:18.080     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65064 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 💡 Thinking:
The user requested to verify the successful login scenario with valid credentials. The login was performed with the email '<EMAIL>' and password 'admin123'. After clicking the login button, the page navigated to a dashboard view with a visible message 'Login exitoso' and 'Bienvenido al sistema de testing', which confirms the successful login. This matches the expected outcome in the user scenario. Therefore, the test for successful login with valid credentials is complete and verified.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 👍 Eval: The login was successfully completed and the dashboard page with a success message was displayed, confirming the user logged in successfully. Verdict: Success
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🧠 Memory: The login process was completed with valid credentials and the system responded with a success message on the dashboard page, confirming the user authentication was successful.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 🎯 Next goal: Prepare to finalize the task by reporting the successful login verification according to the user scenario.

17:52:18.144 🧠 openai/gpt-4.1-mini | 🆕 2.4k + 💾 4.9k | 📤 293
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ☑️ Executed action 1/1: done()
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📄 Result: The user authentication feature was tested successfully. The scenario 'Inicio de sesión exitoso con credenciales válidas' was executed by entering the email '<EMAIL>' and password 'admin123'. The system responded with a successful login message and redirected to the dashboard, confirming the user logged in successfully. Task completed as requested.
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] 📍 Step 5: Ran 1 actions in 8.60s: ✅ 1
INFO     [browser_use.Agent🅰 e7a8 on 🆂 e3ed 🅟 44] ✅ Task completed successfully
17:52:18.250 📊 Per-Model Usage Breakdown:
17:52:18.250   🤖 openai/gpt-4.1-mini: 27.0k tokens | ⬅️ 26.1k | ➡️ 869 | 📞 4 calls | 📈 6.7k/call
17:52:18.258 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:65068 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
17:52:18.267 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:65070 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24] 🛑 Closing cdp_url=http://127.0.0.1:64994/ browser context  <Browser type=<BrowserType name=chromium executable_path=/Users/<USER>/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium> version=138.0.7204.23>
INFO     [browser_use.BrowserSession🆂 e3ed:64994 #24]  ↳ Killing browser_pid=44967 ~/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium (terminate() called)
17:52:18.617 Processed 4 screenshots from history (artifacts already created by collector)
17:52:18.618 🗂️  Retrieved 0 artifacts for execution c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:18.618 Processing 4 history items from history.history
17:52:18.618 [STEP EXTRACT] Processing step 1: type=<class 'browser_use.agent.views.AgentHistory'>
17:52:18.618 [STEP EXTRACT] Successfully processed step 1: action_type=unknown, success=True
17:52:18.619 [STEP EXTRACT] Traceback: NoneType: None

17:52:18.619 [STEP EXTRACT] Processing step 2: type=<class 'browser_use.agent.views.AgentHistory'>
17:52:18.619 [STEP EXTRACT] Successfully processed step 2: action_type=unknown, success=True
17:52:18.619 [STEP EXTRACT] Traceback: NoneType: None

17:52:18.619 [STEP EXTRACT] Processing step 3: type=<class 'browser_use.agent.views.AgentHistory'>
17:52:18.619 [STEP EXTRACT] Successfully processed step 3: action_type=unknown, success=True
17:52:18.619 [STEP EXTRACT] Traceback: NoneType: None

17:52:18.619 [STEP EXTRACT] Processing step 4: type=<class 'browser_use.agent.views.AgentHistory'>
17:52:18.619 [STEP EXTRACT] Successfully processed step 4: action_type=unknown, success=True
17:52:18.619 [STEP EXTRACT] Traceback: NoneType: None

17:52:18.619 Checking 4 model actions for done action
17:52:18.620 Final step count: 4 steps processed
17:52:18.620 🔍 Searching for artifacts with execution_id: c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:18.791 🔍 Found 0 total artifacts for execution
17:52:18.791 🔍 Searching with query: {'execution_id': 'c4810e32-1864-4515-9d40-e0e9f8ada6f0', 'type': 'screenshot'}
17:52:19.130 🔍 Found 0 screenshot artifacts
17:52:19.130 ❌ No artifact found for step 1 with step_name: step_1
17:52:19.130 🔍 Searching for artifacts with execution_id: c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:19.304 🔍 Found 0 total artifacts for execution
17:52:19.304 🔍 Searching with query: {'execution_id': 'c4810e32-1864-4515-9d40-e0e9f8ada6f0', 'type': 'screenshot'}
17:52:19.652 🔍 Found 0 screenshot artifacts
17:52:19.652 ❌ No artifact found for step 2 with step_name: step_2
17:52:19.652 🔍 Searching for artifacts with execution_id: c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:19.838 🔍 Found 0 total artifacts for execution
17:52:19.838 🔍 Searching with query: {'execution_id': 'c4810e32-1864-4515-9d40-e0e9f8ada6f0', 'type': 'screenshot'}
17:52:20.183 🔍 Found 0 screenshot artifacts
17:52:20.184 ❌ No artifact found for step 3 with step_name: step_3
17:52:20.184 🔍 Searching for artifacts with execution_id: c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:20.357 🔍 Found 0 total artifacts for execution
17:52:20.358 🔍 Searching with query: {'execution_id': 'c4810e32-1864-4515-9d40-e0e9f8ada6f0', 'type': 'screenshot'}
17:52:20.699 🔍 Found 0 screenshot artifacts
17:52:20.699 ❌ No artifact found for step 4 with step_name: step_4
17:52:20.699 Agent completed successfully based on final_result: The user authentication feature was tested successfully. The scenario 'Inicio de sesión exitoso con credenciales válidas' was executed by entering the email '<EMAIL>' and password 'admin123'. The system responded with a successful login message and redirected to the dashboard, confirming the user logged in successfully. Task completed as requested.
17:52:20.699 Captured 4 raw result items for frontend processing
17:52:20.699 Processing 4 steps to find done action
17:52:20.699 Last step (#4) success: True, action_type: unknown
17:52:20.700 Found success indicator in formatted raw result: Status: ✅ Success
Completion: ✅ Complete
Memory: Task completed: True - The user authentication feat...
17:52:20.700 SUCCESS INDICATOR: Last step successful with completion message - marking as successful
17:52:20.700 SUCCESS DETERMINATION: Using done action success: True
17:52:20.700 SUCCESS DETERMINATION: Done action successful - overriding any intermediate step failures
17:52:20.700 FINAL SUCCESS DETERMINATION: success=True, done_action_success=True, last_step_success=True, agent_completed_successfully=True
17:52:20.700 🤖 Starting AI-powered test analysis...
17:52:20.700 🔍 PRIORITY EXTRACTION: Attempting to extract screenshots directly from history
17:52:20.700 🔍 HISTORY: Attempting to extract screenshots from history.history
17:52:20.700 ✅ Keeping original screenshot 1 quality - small file (56,343 bytes)
17:52:20.700 ✅ HISTORY: Extracted screenshot 1 from state (75124 chars)
17:52:20.701 ✅ Keeping original screenshot 2 quality - small file (48,926 bytes)
17:52:20.701 ✅ HISTORY: Extracted screenshot 2 from state (65236 chars)
17:52:20.701 ✅ Keeping original screenshot 3 quality - small file (47,558 bytes)
17:52:20.701 ✅ HISTORY: Extracted screenshot 3 from state (63412 chars)
17:52:20.701 ✅ Keeping original screenshot 4 quality - small file (124,470 bytes)
17:52:20.701 ✅ HISTORY: Extracted screenshot 4 from state (165960 chars)
17:52:20.701 🖼️ HISTORY: Successfully extracted 4 screenshots for AI analysis
17:52:20.702 🔍 ARTIFACTS DEBUG: result.artifacts = Artifacts(screenshots=[], videos=[], logs=[], generated_code=None, history_file=None, gherkin_scenarios=[])
17:52:20.702 🔍 ARTIFACTS DEBUG: artifacts.screenshots = []
17:52:20.702 🔍 ARTIFACTS DEBUG: artifacts.__dict__ = {'screenshots': [], 'videos': [], 'logs': [], 'generated_code': None, 'history_file': None, 'gherkin_scenarios': []}
17:52:20.702 📸 AI Analysis: Prepared 4 screenshots for analysis
17:52:20.702 🔍 Background jobs check: BACKGROUND_JOBS_AVAILABLE=True, USE_BACKGROUND_JOBS=true, use_background_jobs=True
17:52:20.702 🔄 Background jobs available - will process AI analysis asynchronously
17:52:20.702 ✅ JobManager using local Redis: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
17:52:20.704 Created job ai_analysis_cf13ee31 for execution c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:20.758 🚀 AI analysis job created: ai_analysis_cf13ee31
17:52:20.758 Processed browser_history result: c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:20.758 🔍 ORCHESTRATOR DEBUG: Final status: ExecutionStatus.SUCCESS (type: <enum 'ExecutionStatus'>)
17:52:21.079 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:21.080   FastAPI arguments
17:52:21.081     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:65080 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:22.315 ✅ Created document in executions: 6872a0d4363160e7152dcbcc
17:52:22.316 💾 Saved execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 to the database.
17:52:23.174 ✅ Updated document in projects: 6861fe7bb4804a69aa767eca
17:52:23.175 Added execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 to test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 history
17:52:23.175 📊 Updated history for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 with execution c4810e32-1864-4515-9d40-e0e9f8ada6f0.
17:52:23.175 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 status: ExecutionStatus.SUCCESS
17:52:23.175 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 completed successfully
17:52:23.175 Browser 99ae2801-402f-4993-a164-5969101b3f13 contaminated: 
17:52:23.175 Disposed browser 99ae2801-402f-4993-a164-5969101b3f13
17:52:23.175 Background execution completed for c4810e32-1864-4515-9d40-e0e9f8ada6f0 with status ExecutionStatus.SUCCESS
17:52:24.084 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:24.085   FastAPI arguments
17:52:24.086     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:24.086     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:24.592     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:24.593     📋 MongoDB metadata present: True
17:52:24.593     🧠 AI Analysis in MongoDB metadata: False
17:52:24.593     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:65080 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:24.714 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:24.716   FastAPI arguments
17:52:24.716     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:24.716     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:24.906     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:24.906     📋 MongoDB metadata present: True
17:52:24.906     🧠 AI Analysis in MongoDB metadata: False
17:52:24.906     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:65080 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:24.936 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:24.937   FastAPI arguments
INFO:     127.0.0.1:65080 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:24.939 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:24.939   FastAPI arguments
INFO:     127.0.0.1:65080 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:27.079 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:27.080   FastAPI arguments
INFO:     127.0.0.1:65080 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:29.079 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:29.080   FastAPI arguments
INFO:     127.0.0.1:65080 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:31.083 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:31.083   FastAPI arguments
INFO:     127.0.0.1:65099 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:33.082 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:33.083   FastAPI arguments
INFO:     127.0.0.1:65099 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:35.079 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:35.080   FastAPI arguments
INFO:     127.0.0.1:65106 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:37.085 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:37.086   FastAPI arguments
INFO:     127.0.0.1:65106 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:39.080 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:39.081   FastAPI arguments
INFO:     127.0.0.1:65106 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:41.080 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:41.081   FastAPI arguments
INFO:     127.0.0.1:65112 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:43.083 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:43.084   FastAPI arguments
INFO:     127.0.0.1:65112 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:45.080 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:45.080   FastAPI arguments
INFO:     127.0.0.1:65117 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:47.079 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:47.079   FastAPI arguments
INFO:     127.0.0.1:65117 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:48.940 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:48.940   FastAPI arguments
17:52:48.940     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:48.940     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:48.941 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:48.941   FastAPI arguments
INFO:     127.0.0.1:65139 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
             GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
               GET /api/v2/tests/execution/{execution_id} (get_execution_status)
17:52:49.803     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:49.803     📋 MongoDB metadata present: True
17:52:49.803     🧠 AI Analysis in MongoDB metadata: False
17:52:49.803     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:65117 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:50.940 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:50.941   FastAPI arguments
INFO:     127.0.0.1:65117 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:51.810 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:51.811   FastAPI arguments
17:52:51.811     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:51.811     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:52.659     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:52.659     📋 MongoDB metadata present: True
17:52:52.659     🧠 AI Analysis in MongoDB metadata: False
17:52:52.659     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:65117 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:52.940 GET /api/v2/background-jobs/ai_analysis_cf13ee31/status
17:52:52.940   FastAPI arguments
INFO:     127.0.0.1:65117 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/status HTTP/1.1" 200 OK
17:52:52.944 GET /api/v2/background-jobs/ai_analysis_cf13ee31/result
17:52:52.945   FastAPI arguments
INFO:     127.0.0.1:65117 - "GET /api/v2/background-jobs/ai_analysis_cf13ee31/result HTTP/1.1" 200 OK
17:52:53.951 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:53.951   FastAPI arguments
17:52:53.951     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:53.952     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:54.625     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:54.626     📋 MongoDB metadata present: True
17:52:54.626     🧠 AI Analysis in MongoDB metadata: False
17:52:54.626     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:65117 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
17:52:56.636 GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:56.637   FastAPI arguments
17:52:56.637     Found execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 in simple tracking with status ExecutionStatus.SUCCESS
17:52:56.637     🔍 Execution c4810e32-1864-4515-9d40-e0e9f8ada6f0 is complete, checking MongoDB for latest data including AI analysis
17:52:57.479     ✅ Found updated execution data in MongoDB for c4810e32-1864-4515-9d40-e0e9f8ada6f0
17:52:57.479     📋 MongoDB metadata present: True
17:52:57.479     🧠 AI Analysis in MongoDB metadata: True
17:52:57.479     📊 AI Analysis Status in MongoDB metadata: completed
INFO:     127.0.0.1:65117 - "GET /api/v2/tests/execution/c4810e32-1864-4515-9d40-e0e9f8ada6f0 HTTP/1.1" 200 OK
