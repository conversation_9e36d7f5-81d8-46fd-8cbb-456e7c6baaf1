"""
Gemini Service Provider - Maintains compatibility with existing Gemini usage
Adapts current Gemini implementation to the new LLM service interface
"""

import os
import logging
import base64
from typing import Dict, Any, List, Optional
# Removed <PERSON><PERSON><PERSON><PERSON> dependency to avoid temperature parameter issues
import google.generativeai as genai
from PIL import Image
import io

from ..base_llm_service import BaseLLMService, LLMRequest, LLMResponse, MessageContent, ImageContent

logger = logging.getLogger(__name__)


class GeminiService(BaseLLMService):
    """Gemini LLM service provider - wraps existing Gemini implementation."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini service.
        
        Args:
            api_key: Google API key (defaults to env GOOGLE_API_KEY)
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        self._client = None
        self._available = False
        
        if self.api_key:
            try:
                # Initialize Gemini client using native Google AI SDK only
                genai.configure(api_key=self.api_key)
                model_name = os.getenv("LLM_MODEL", "gemini-2.5-flash")
                self._client = genai.GenerativeModel(model_name)
                self._vision_model = genai.GenerativeModel(model_name)

                self._available = True
                logger.info(f"Gemini service initialized successfully with model: {model_name}")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini service: {e}")
                self._available = False
    
    def is_available(self) -> bool:
        """Check if Gemini service is available."""
        return self._available and self._client is not None
    
    def get_provider_name(self) -> str:
        """Get provider name."""
        return "gemini"
    
    def get_supported_use_cases(self) -> List[str]:
        """Get supported use cases."""
        return [
            "gherkin",
            "validation", 
            "translation",
            "enhancement",
            "test_analysis",  # Test analysis with step validation and completion analysis
            "script_generation",  # AI-powered automation script generation
            "browser_automation",
            "general"
        ]
    
    def make_request(self, request: LLMRequest) -> LLMResponse:
        """Make request to Gemini with multimodal support."""
        if not self.is_available():
            return LLMResponse(
                content="",
                model_used="gemini",
                success=False,
                error="Gemini service not available"
            )
        
        try:
            # Check if request has images for multimodal processing
            if request.has_images:
                return self._make_vision_request(request)
            else:
                return self._make_text_request(request)
                
        except Exception as e:
            logger.error(f"Gemini request failed: {e}")
            return LLMResponse(
                content="",
                model_used="gemini",
                success=False,
                error=str(e)
            )
    
    def _make_text_request(self, request: LLMRequest) -> LLMResponse:
        """Make text-only request to Gemini."""
        # Convert messages to Gemini format
        if len(request.messages) == 1:
            # Single message
            prompt = request.messages[0]["content"]
        else:
            # Multiple messages - combine system and user messages
            system_msgs = [msg["content"] for msg in request.messages if msg["role"] == "system"]
            user_msgs = [msg["content"] for msg in request.messages if msg["role"] == "user"]
            
            prompt = ""
            if system_msgs:
                prompt += "System: " + " ".join(system_msgs) + "\n\n"
            if user_msgs:
                prompt += "User: " + " ".join(user_msgs)
        
        # Add language instruction if specified
        if request.language and request.language != "en":
            if request.language == "es":
                prompt += "\n\nRespond in Spanish."
        
        # Make request using native Google AI SDK
        response = self._client.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=request.temperature or 0.1,
                max_output_tokens=request.max_tokens or 4096
            )
        )

        return LLMResponse(
            content=response.text,
            model_used="gemini",
            usage=getattr(response, 'usage_metadata', None),
            metadata={
                "provider": "gemini",
                "request_type": "text"
            }
        )
    
    def _make_vision_request(self, request: LLMRequest) -> LLMResponse:
        """Make multimodal request to Gemini with vision support."""
        # Build content list for multimodal request
        content_parts = []
        
        # Process messages with mixed content
        for message in request.messages:
            if message["role"] == "system":
                content_parts.append(f"System: {message['content']}")
            elif message["role"] == "user":
                if isinstance(message["content"], list):
                    # Mixed content (text + images)
                    for content_item in message["content"]:
                        if isinstance(content_item, MessageContent):
                            if content_item.text:
                                content_parts.append(content_item.text)
                            if content_item.image:
                                # Convert base64 to PIL Image
                                image_data = base64.b64decode(content_item.image.data)
                                image = Image.open(io.BytesIO(image_data))
                                content_parts.append(image)
                        elif isinstance(content_item, dict):
                            # Handle dict format
                            if "text" in content_item:
                                content_parts.append(content_item["text"])
                            if "image" in content_item:
                                image_data = base64.b64decode(content_item["image"]["data"])
                                image = Image.open(io.BytesIO(image_data))
                                content_parts.append(image)
                else:
                    # Text only
                    content_parts.append(message["content"])
        
        # Add language instruction if specified
        if request.language and request.language != "en":
            if request.language == "es":
                content_parts.append("\n\nRespond in Spanish.")
        
        # Make vision request
        response = self._vision_model.generate_content(
            content_parts,
            generation_config=genai.types.GenerationConfig(
                temperature=request.temperature or 0.1,
                max_output_tokens=request.max_tokens or 4096
            )
        )
        
        return LLMResponse(
            content=response.text,
            model_used="gemini-2.5-flash",
            usage=getattr(response, 'usage_metadata', None),
            metadata={
                "provider": "gemini",
                "request_type": "vision",
                "use_case": request.use_case
            }
        )
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get Gemini usage statistics."""
        if not self.is_available():
            return {"status": "unavailable", "error": "Service not available"}
        
        return {
            "provider": "gemini",
            "status": "available",
            "model": os.getenv("LLM_MODEL", "gemini-2.5-flash"),
            "api_key_configured": bool(self.api_key)
        }
