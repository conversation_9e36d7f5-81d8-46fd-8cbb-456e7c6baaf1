"""
LLM Service Factory - Central orchestrator for all LLM operations
Handles provider selection, fallbacks, and routing based on use cases
"""

import logging
from typing import Dict, Any, List, Optional, Type
from .base_llm_service import BaseLLMService, LLMRequest, LLMResponse
from .providers.gemini_service import GeminiService
from .providers.openrouter_service import OpenRouterService

logger = logging.getLogger(__name__)


class LLMServiceFactory:
    """Factory for managing multiple LLM service providers with intelligent routing."""
    
    def __init__(self):
        """Initialize the factory with available providers."""
        self._providers: Dict[str, BaseLLMService] = {}
        self._use_case_routing: Dict[str, List[str]] = {}
        self._initialize_providers()
        self._configure_routing()
    
    def _initialize_providers(self):
        """Initialize all available LLM providers."""
        # Try to initialize each provider (OpenRouter first as primary)
        providers_to_try = [
            ("openrouter", OpenRouterService),
            ("gemini", GeminiService)
        ]
        
        for name, provider_class in providers_to_try:
            try:
                provider = provider_class()
                if provider.is_available():
                    self._providers[name] = provider
                    logger.info(f"LLM provider '{name}' initialized successfully")
                else:
                    logger.warning(f"LLM provider '{name}' not available")
            except Exception as e:
                logger.warning(f"Failed to initialize LLM provider '{name}': {e}")
        
        if not self._providers:
            logger.error("No LLM providers available!")
    
    def _configure_routing(self):
        """Configure which providers to use for each use case."""
        self._use_case_routing = {
            # OpenRouter first for cost-effective operations
            "gherkin": ["openrouter", "gemini"],
            "validation": ["openrouter", "gemini"], 
            "translation": ["openrouter", "gemini"],
            "enhancement": ["openrouter", "gemini"],
            "test_analysis": ["openrouter", "gemini"],  # Test analysis with step validation and completion analysis
            "script_generation": ["openrouter", "gemini"],  # AI-powered automation script generation
            
            # Gemini first for browser automation (stays in /libs)
            "browser_automation": ["gemini", "openrouter"],
            
            # Default fallback order (OpenRouter first as primary)
            "default": ["openrouter", "gemini"]
        }
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return list(self._providers.keys())
    
    def get_provider(self, name: str) -> Optional[BaseLLMService]:
        """Get a specific provider by name."""
        return self._providers.get(name)
    
    def make_request(self, request: LLMRequest, 
                    preferred_provider: Optional[str] = None) -> LLMResponse:
        """Make request with automatic provider selection and fallback.
        
        Args:
            request: The LLM request to process
            preferred_provider: Optional preferred provider name
            
        Returns:
            LLMResponse with result or error
        """
        # Determine provider order
        if preferred_provider and preferred_provider in self._providers:
            provider_order = [preferred_provider]
            # Add fallbacks
            fallbacks = self._use_case_routing.get(request.use_case, 
                                                 self._use_case_routing["default"])
            provider_order.extend([p for p in fallbacks if p != preferred_provider])
        else:
            provider_order = self._use_case_routing.get(request.use_case,
                                                      self._use_case_routing["default"])
        
        last_error = None
        
        # Try providers in order
        for provider_name in provider_order:
            provider = self._providers.get(provider_name)
            if not provider:
                continue
                
            try:
                logger.info(f"Trying provider '{provider_name}' for use case '{request.use_case}'")
                
                # Validate request
                if not provider.validate_request(request):
                    logger.warning(f"Request validation failed for provider '{provider_name}'")
                    continue
                
                # Make request
                response = provider.make_request(request)
                
                if response.success:
                    logger.info(f"Success with provider '{provider_name}' for use case '{request.use_case}'")
                    return response
                else:
                    logger.warning(f"Provider '{provider_name}' returned unsuccessful response: {response.error}")
                    last_error = response.error
                    continue
                    
            except Exception as e:
                logger.warning(f"Error with provider '{provider_name}': {e}")
                last_error = str(e)
                continue
        
        # All providers failed
        error_msg = f"All providers failed for use case '{request.use_case}'. Last error: {last_error}"
        logger.error(error_msg)
        
        return LLMResponse(
            content="",
            model_used="none",
            success=False,
            error=error_msg
        )
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics from all providers."""
        stats = {}
        for name, provider in self._providers.items():
            try:
                provider_stats = provider.get_usage_stats()
                stats[name] = provider_stats
            except Exception as e:
                stats[name] = {"error": str(e)}
        return stats
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all providers."""
        status = {
            "total_providers": len(self._providers),
            "available_providers": list(self._providers.keys()),
            "providers": {}
        }
        
        for name, provider in self._providers.items():
            try:
                is_available = provider.is_available()
                provider_name = provider.get_provider_name()
                supported_cases = provider.get_supported_use_cases()
                
                status["providers"][name] = {
                    "available": is_available,
                    "provider_name": provider_name,
                    "supported_use_cases": supported_cases
                }
            except Exception as e:
                status["providers"][name] = {
                    "available": False,
                    "error": str(e)
                }
        
        return status


class BackgroundJobsLLMServiceFactory(LLMServiceFactory):
    """Factory for background jobs with paid models and cost control."""
    
    def __init__(self):
        """Initialize the factory with background jobs providers."""
        self._providers: Dict[str, BaseLLMService] = {}
        self._use_case_routing: Dict[str, List[str]] = {}
        self._initialize_background_jobs_providers()
        self._configure_routing()
    
    def _initialize_background_jobs_providers(self):
        """Initialize providers specifically for background jobs with paid models."""
        providers_to_try = [
            ("openrouter", self._create_background_jobs_openrouter),
            ("gemini", GeminiService)
        ]
        
        for name, provider_factory in providers_to_try:
            try:
                if name == "openrouter":
                    provider = provider_factory()
                else:
                    provider = provider_factory()
                
                if provider.is_available():
                    self._providers[name] = provider
                    logger.info(f"Background job LLM provider '{name}' initialized successfully")
                else:
                    logger.warning(f"Background job LLM provider '{name}' not available")
            except Exception as e:
                logger.warning(f"Failed to initialize background job LLM provider '{name}': {e}")
        
        if not self._providers:
            logger.error("No background job LLM providers available!")
        else:
            logger.info("🎯 Background Jobs LLM Factory initialized with $2 cost limit")
    
    def _create_background_jobs_openrouter(self):
        """Create OpenRouter service specifically for background jobs."""
        from .providers.openrouter_service import get_openrouter_service_for_background_jobs
        logger.info("🎯 Creating OpenRouter service for background jobs with $2.0 limit")
        return get_openrouter_service_for_background_jobs(max_monthly_spend=2.0)


# Singleton instance
_llm_factory = None

def get_llm_factory() -> LLMServiceFactory:
    """Get singleton LLM factory instance."""
    global _llm_factory
    if _llm_factory is None:
        _llm_factory = LLMServiceFactory()
    return _llm_factory

# Background jobs factory instance
_background_jobs_llm_factory = None

def get_background_jobs_llm_factory() -> BackgroundJobsLLMServiceFactory:
    """Get LLM factory configured for background jobs with paid models."""
    return BackgroundJobsLLMServiceFactory()
