[2025-07-12 15:26:30,107: INFO/MainProcess] ⏭️ Model meta-llama/llama-4-maverick overloaded/timeout, trying next model...
[2025-07-12 15:26:30,107: INFO/MainProcess] Trying 💰 PAID PAID model anthropic/claude-3-haiku for test_analysis (3/3) [8s timeout]
[2025-07-12 15:26:30,107: INFO/MainProcess] Making request to 💰 PAID PAID model: anthropic/claude-3-haiku [8s timeout]
[2025-07-12 15:26:33,662: WARNING/MainProcess] ❌ Error with model anthropic/claude-3-haiku: HTTP 413: {"error":{"message":"Provider returned error","code":413,"metadata":{"raw":"{\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"Prompt is too long\"}}","provider_name":"Google"}},"user_id":"user_2zNqsWtL005XBrngsQqGOylqzYZ"}
[2025-07-12 15:26:33,662: ERROR/MainProcess] Failed after trying 3 models for test_analysis (limited to 3 to prevent API blocking). Last error: HTTP 413: {"error":{"message":"Provider returned error","code":413,"metadata":{"raw":"{\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"Prompt is too long\"}}","provider_name":"Google"}},"user_id":"user_2zNqsWtL005XBrngsQqGOylqzYZ"}
[2025-07-12 15:26:33,662: WARNING/MainProcess] Provider 'openrouter' returned unsuccessful response: Failed after trying 3 models for test_analysis (limited to 3 to prevent API blocking). Last error: HTTP 413: {"error":{"message":"Provider returned error","code":413,"metadata":{"raw":"{\"type\":\"error\",\"error\":{\"type\":\"invalid_request_error\",\"message\":\"Prompt is too long\"}}","provider_name":"Google"}},"user_id":"user_2zNqsWtL005XBrngsQqGOylqzYZ"}
[2025-07-12 15:26:33,662: INFO/MainProcess] Trying provider 'gemini' for use case 'test_analysis'
[2025-07-12 15:26:33,662: WARNING/MainProcess] /Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py:483: UserWarning: Convert_system_message_to_human will be deprecated!
  warnings.warn("Convert_system_message_to_human will be deprecated!")

[2025-07-12 15:26:33,663: ERROR/MainProcess] Gemini request failed: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 15:26:33,663: WARNING/MainProcess] Provider 'gemini' returned unsuccessful response: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 15:26:33,663: ERROR/MainProcess] All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 15:26:33,663: ERROR/MainProcess] LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 15:26:33,663: ERROR/MainProcess] Error analyzing step 3: LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
Traceback (most recent call last):
  File "/Users/<USER>/Proyectos/qak/src/services/test_analysis_service.py", line 178, in analyze_step
    raise Exception(f"LLM request failed: {result.get('error', 'Unknown error')}")
Exception: LLM request failed: All providers failed for use case 'test_analysis'. Last error: GenerativeServiceClient.generate_content() got an unexpected keyword argument 'temperature'
[2025-07-12 15:26:33,663: INFO/MainProcess] 📸 Step 4: Analyzing with screenshot (373564 chars)
[2025-07-12 15:26:33,663: INFO/MainProcess] 📸 Background Jobs: Compressing screenshot for step 4 AI analysis
[2025-07-12 15:26:33,708: INFO/MainProcess] 🗜️ Background Jobs: Screenshot compressed for AI analysis: 280,173 → 43,234 bytes (15.4%) | (2984, 1682) → (1200, 676) | Q:55, Max:1200px
[2025-07-12 15:26:33,708: INFO/MainProcess] 📸 Background Jobs: Original: 373564 chars → Compressed: 57648 chars
[2025-07-12 15:26:33,708: INFO/MainProcess] 💰 Step 4: Screenshot available but skipping for cost optimization - text analysis sufficient
[2025-07-12 15:26:33,709: INFO/MainProcess] Trying provider 'openrouter' for use case 'test_analysis'
[2025-07-12 15:26:33,709: INFO/Ma