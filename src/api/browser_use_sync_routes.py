"""Rutas de API para sincronización de eventos de browser-use.

Este módulo reemplaza la funcionalidad de sincronización en la nube de browser-use
para que los eventos se envíen a tu API local en lugar de a los servidores externos.

Ahora con soporte para MongoDB, logging estructurado JSON y correlación de IDs.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Header, Request, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from ..database.connection import get_database
from ..database.repositories.browser_use_event_repository import (
    BrowserUseEventRepository,
    BrowserUseSessionRepository,
    BrowserUseTaskRepository,
    BrowserUseStepRepository,
    BrowserUseFileRepository
)
from ..database.models.browser_use_event import (
    BrowserUseEventDocument,
    BrowserUseSessionDocument,
    BrowserUseTaskDocument,
    BrowserUseStepDocument,
    BrowserUseFileDocument,
    EventBatch,
    EventStats
)
from ..services.browser_use_logging_service import (
    browser_use_logger,
    log_session_event,
    log_task_event,
    log_step_event,
    log_error_event,
    log_file_event,
    EventType
)
from ..config.user_config import get_real_user_id

logger = logging.getLogger("src.api.browser_use_sync")

router = APIRouter(tags=["Browser Use Sync"])

# Funciones auxiliares para correlación de IDs
def generate_correlation_id() -> str:
    """Generar un ID de correlación único."""
    return str(uuid.uuid4())

def extract_client_info(request: Request) -> tuple[Optional[str], Optional[str]]:
    """Extraer información del cliente de la request."""
    source_ip = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    return source_ip, user_agent

def extract_request_context(request: Request, authorization: Optional[str] = None) -> Dict[str, Any]:
    """Extraer contexto completo de la request para determinación de user_id."""
    source_ip, user_agent = extract_client_info(request)
    
    # Convertir headers a dict para facilitar el acceso
    headers_dict = dict(request.headers.items())
    
    # Agregar authorization header si está presente
    if authorization:
        headers_dict['authorization'] = authorization
    
    return {
        'source_ip': source_ip,
        'user_agent': user_agent,
        'headers': headers_dict,
        'query_params': dict(request.query_params.items()),
        'method': request.method,
        'url': str(request.url)
    }

def get_or_create_correlation_ids(
    event_data: Dict[str, Any],
    event_type: str
) -> tuple[str, Optional[str], Optional[str]]:
    """Obtener o crear IDs de correlación basados en el tipo de evento."""
    
    correlation_id = event_data.get('correlation_id') or generate_correlation_id()
    session_correlation_id = None
    task_correlation_id = None
    
    if event_type == 'CreateAgentSessionEvent':
        session_correlation_id = event_data.get('id') or correlation_id
    elif event_type == 'CreateAgentTaskEvent':
        task_correlation_id = event_data.get('id') or correlation_id
        session_correlation_id = event_data.get('agent_session_id')
    elif event_type in ['CreateAgentStepEvent', 'CreateOutputFileEvent']:
        task_correlation_id = event_data.get('agent_task_id')
        # Buscar session_correlation_id desde la tarea
    elif event_type == 'UpdateAgentTaskEvent':
        task_correlation_id = event_data.get('id')
    
    return correlation_id, session_correlation_id, task_correlation_id

def filter_event_data_for_storage(event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filtra los datos del evento para remover imágenes base64 grandes que pueden causar
    problemas de memoria en MongoDB.
    
    Args:
        event_data: Datos originales del evento
        
    Returns:
        Dict con datos filtrados, sin imágenes base64 grandes
    """
    if not isinstance(event_data, dict):
        return event_data
    
    filtered_data = event_data.copy()
    
    # Lista de campos que típicamente contienen imágenes base64
    base64_fields = [
        'screenshot',
        'screenshot_data', 
        'screenshot_base64',
        'image',
        'image_data',
        'capture',
        'thumbnail',
        'screenshot_url'  # También puede contener data URLs
    ]
    
    # Filtrar campos de nivel superior
    for field in base64_fields:
        if field in filtered_data:
            value = filtered_data[field]
            if isinstance(value, str) and is_large_base64_data(value):
                # Reemplazar con referencia en lugar de eliminar completamente
                filtered_data[field] = f"<large_base64_data_removed_{len(value)}_bytes>"
    
    # Filtrar cualquier string que sea claramente base64 grande, sin importar el nombre del campo
    for key, value in list(filtered_data.items()):
        if isinstance(value, str) and is_large_base64_data(value):
            filtered_data[key] = f"<large_base64_data_removed_{len(value)}_bytes>"
        elif isinstance(value, dict):
            filtered_data[key] = filter_event_data_for_storage(value)
        elif isinstance(value, list):
            filtered_data[key] = [
                filter_event_data_for_storage(item) if isinstance(item, dict) else 
                (f"<large_base64_data_removed_{len(item)}_bytes>" if isinstance(item, str) and is_large_base64_data(item) else item)
                for item in value
            ]
    
    return filtered_data


def is_large_base64_data(value: str) -> bool:
    """
    Determina si un string es probablemente datos base64 grandes que deben ser filtrados.
    
    Args:
        value: String a verificar
        
    Returns:
        True si parece ser datos base64 grandes
    """
    if not isinstance(value, str) or len(value) < 100:
        return False
    
    # Verificar data URLs de imágenes
    if value.startswith('data:image/'):
        return True
    
    # Verificar firmas comunes de base64 para imágenes
    base64_signatures = [
        '/9j/',  # JPEG
        'iVBORw0KGgo',  # PNG
        'UklGR',  # WebP
        'R0lGOD',  # GIF
    ]
    
    for signature in base64_signatures:
        if value.startswith(signature):
            return True
    
    # Si es muy largo y parece base64 (solo caracteres base64 válidos)
    if len(value) > 1000:
        base64_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
        value_chars = set(value[:100])  # Verificar solo los primeros 100 caracteres para eficiencia
        
        # Si al menos el 90% de los caracteres son válidos para base64
        if len(value_chars.intersection(base64_chars)) / len(value_chars) > 0.9:
            return True
    
    return False

@router.post("/v1/events", summary="Recibir eventos de browser-use")
async def receive_events(
    event_batch: EventBatch,
    request: Request,
    authorization: Optional[str] = Header(None)
):
    """Endpoint principal para recibir eventos de browser-use.
    
    Este endpoint reemplaza la funcionalidad de sincronización en la nube
    de browser-use, almacenando los eventos en MongoDB con logging estructurado
    y correlación de IDs.
    """
    
    # Inicializar repositorios
    event_repo = BrowserUseEventRepository()
    session_repo = BrowserUseSessionRepository()
    task_repo = BrowserUseTaskRepository()
    step_repo = BrowserUseStepRepository()
    file_repo = BrowserUseFileRepository()
    
    # Extraer información del cliente
    source_ip, user_agent = extract_client_info(request)
    
    # Extraer contexto completo para determinación de user_id
    request_context = extract_request_context(request, authorization)
    
    try:
        processed_events = []
        
        with browser_use_logger.timed_operation("process_event_batch"):
            for event_data in event_batch.events:
                event_type = event_data.get('event_type')
                
                # Obtener user_id real usando el nuevo sistema
                original_user_id = event_data.get('user_id')
                real_user_id = get_real_user_id(request_context)
                
                # Si el user_id original es el temporal, reemplazarlo
                if original_user_id == '99999999-9999-9999-9999-999999999999' or not original_user_id:
                    event_data['user_id'] = real_user_id
                    event_data['original_user_id'] = original_user_id  # Mantener referencia
                else:
                    # Si ya tiene un user_id real, respetarlo pero agregar el calculado como alternativo
                    event_data['calculated_user_id'] = real_user_id
                
                user_id = event_data['user_id']
                device_id = event_data.get('device_id')
                
                # Generar IDs de correlación
                correlation_id, session_correlation_id, task_correlation_id = get_or_create_correlation_ids(
                    event_data, event_type
                )
                
                # Configurar contexto de logging
                browser_use_logger.context.set_request_context(source_ip, user_agent)
                if user_id:
                    browser_use_logger.context.user_id = user_id
                if device_id:
                    browser_use_logger.context.device_id = device_id
                
                try:
                    # Procesar según el tipo de evento
                    if event_type == 'CreateAgentSessionEvent':
                        session_doc = BrowserUseSessionDocument(
                            session_id=event_data.get('id'),
                            correlation_id=correlation_id,
                            user_id=user_id,
                            device_id=device_id,
                            browser_session_id=event_data.get('browser_session_id', ''),
                            browser_session_live_url=event_data.get('browser_session_live_url', ''),
                            browser_session_cdp_url=event_data.get('browser_session_cdp_url', ''),
                            browser_session_stopped=event_data.get('browser_session_stopped', False),
                            browser_state=event_data.get('browser_state', {}),
                            browser_session_data=event_data.get('browser_session_data'),
                            metadata={
                                'source_ip': source_ip,
                                'user_agent': user_agent,
                                'original_event': event_data
                            }
                        )
                        await session_repo.create(session_doc)
                        
                        log_session_event(
                            f"Nueva sesión creada: {session_doc.session_id}",
                            session_doc.session_id,
                            user_id,
                            device_id,
                            correlation_id=correlation_id
                        )
                        
                    elif event_type == 'CreateAgentTaskEvent':
                        task_doc = BrowserUseTaskDocument(
                            task_id=event_data.get('id'),
                            correlation_id=correlation_id,
                            session_correlation_id=session_correlation_id,
                            agent_session_id=event_data.get('agent_session_id', ''),
                            user_id=user_id,
                            device_id=device_id,
                            llm_model=event_data.get('llm_model', ''),
                            task=event_data.get('task', ''),
                            stopped=event_data.get('stopped', False),
                            paused=event_data.get('paused', False),
                            done_output=event_data.get('done_output'),
                            scheduled_task_id=event_data.get('scheduled_task_id'),
                            started_at=datetime.fromisoformat(event_data.get('started_at', datetime.utcnow().isoformat())),
                            finished_at=datetime.fromisoformat(event_data['finished_at']) if event_data.get('finished_at') else None,
                            agent_state=event_data.get('agent_state', {}),
                            metadata={
                                'source_ip': source_ip,
                                'user_agent': user_agent,
                                'original_event': event_data
                            }
                        )
                        await task_repo.create(task_doc)
                        
                        log_task_event(
                            f"Nueva tarea creada: {task_doc.task_id}",
                            task_doc.task_id,
                            session_correlation_id,
                            correlation_id=correlation_id
                        )
                        
                    elif event_type == 'CreateAgentStepEvent':
                        step_doc = BrowserUseStepDocument(
                            task_correlation_id=task_correlation_id,
                            session_correlation_id=session_correlation_id,
                            task_id=event_data.get('agent_task_id', ''),
                            user_id=user_id,
                            device_id=device_id,
                            step_number=event_data.get('step', 0),
                            action_type=event_data.get('evaluation_previous_goal', 'unknown'),
                            action_data={
                                'memory': event_data.get('memory', ''),
                                'next_goal': event_data.get('next_goal', ''),
                                'actions': event_data.get('actions', []),
                                'url': event_data.get('url', '')
                            },
                            screenshot_url=event_data.get('screenshot_url'),
                            metadata={
                                'source_ip': source_ip,
                                'user_agent': user_agent,
                                'original_event': event_data
                            }
                        )
                        await step_repo.create(step_doc)
                        
                        log_step_event(
                            f"Nuevo paso #{step_doc.step_number} para tarea {step_doc.task_id}",
                            step_doc.step_number,
                            step_doc.task_id,
                            correlation_id=correlation_id
                        )
                        
                    elif event_type == 'CreateAgentOutputFileEvent':
                        file_doc = BrowserUseFileDocument(
                            task_correlation_id=task_correlation_id,
                            session_correlation_id=session_correlation_id,
                            task_id=event_data.get('task_id', ''),
                            user_id=user_id,
                            device_id=device_id,
                            file_name=event_data.get('file_name', 'unknown'),
                            file_path=event_data.get('file_name', ''),  # Usar file_name como path por defecto
                            file_type=event_data.get('content_type', 'unknown'),
                            content_type=event_data.get('content_type'),
                            metadata={
                                'source_ip': source_ip,
                                'user_agent': user_agent,
                                'file_content_base64': event_data.get('file_content'),
                                'original_event': event_data
                            }
                        )
                        await file_repo.create(file_doc)
                        
                        log_file_event(
                            f"Nuevo archivo '{file_doc.file_name}' para tarea {file_doc.task_id}",
                            file_doc.file_name,
                            correlation_id=correlation_id
                        )
                        
                    elif event_type == 'UpdateAgentTaskEvent':
                        # Buscar y actualizar tarea existente
                        task_id = event_data.get('id')
                        existing_tasks = await task_repo.find({'task_id': task_id})
                        existing_task = existing_tasks[0] if existing_tasks else None
                        
                        if existing_task:
                            update_data = {}
                            if 'stopped' in event_data:
                                update_data['stopped'] = event_data['stopped']
                            if 'paused' in event_data:
                                update_data['paused'] = event_data['paused']
                            if 'done_output' in event_data:
                                update_data['done_output'] = event_data['done_output']
                            if 'finished_at' in event_data:
                                update_data['finished_at'] = datetime.fromisoformat(event_data['finished_at'])
                            if 'agent_state' in event_data:
                                update_data['agent_state'] = event_data['agent_state']
                            
                            # Use the new update_by_id method instead of update
                            await task_repo.update_by_id(str(existing_task.id), update_data)
                            
                            log_task_event(
                                f"Tarea actualizada: {task_id}",
                                task_id,
                                existing_task.session_correlation_id,
                                correlation_id=correlation_id
                            )                    # Filtrar datos del evento para remover imágenes base64 grandes
                    original_size = len(str(event_data))
                    filtered_event_data = filter_event_data_for_storage(event_data)
                    filtered_size = len(str(filtered_event_data))
                    
                    # Log de optimización si se removieron datos significativos
                    if original_size > filtered_size:
                        size_reduction = original_size - filtered_size
                        size_reduction_pct = (size_reduction / original_size) * 100
                        browser_use_logger.info(
                            f"Filtrado de datos base64: {size_reduction} bytes removidos ({size_reduction_pct:.1f}%)",
                            "optimization",
                            metadata={
                                "original_size": original_size,
                                "filtered_size": filtered_size,
                                "size_reduction": size_reduction,
                                "event_type": event_type
                            }
                        )
                    
                    # Crear evento general
                    general_event = await event_repo.create_event(
                        event_type=event_type,
                        data=filtered_event_data,
                        user_id=user_id,
                        device_id=device_id,
                        correlation_id=correlation_id,
                        session_correlation_id=session_correlation_id,
                        task_correlation_id=task_correlation_id,
                        metadata={
                            'processed_at': datetime.utcnow().isoformat(),
                            'authorization_header': bool(authorization)
                        },
                        source_ip=source_ip,
                        user_agent=user_agent
                    )
                    
                    processed_events.append(general_event.event_id)
                    
                except Exception as event_error:
                    log_error_event(
                        f"Error procesando evento {event_type}: {str(event_error)}",
                        error_code="EVENT_PROCESSING_ERROR",
                        error_details={
                            "event_type": event_type,
                            "event_data": event_data,
                            "error": str(event_error)
                        },
                        correlation_id=correlation_id
                    )
                    # Continuar con el siguiente evento en lugar de fallar todo el lote
                    continue
        
        browser_use_logger.info(
            f"Procesados {len(processed_events)} eventos de browser-use",
            EventType.SESSION,
            metadata={
                "events_processed": len(processed_events),
                "total_in_batch": len(event_batch.events)
            }
        )
        
        return {
            "status": "success",
            "message": f"Procesados {len(processed_events)} eventos",
            "events_processed": len(processed_events),
            "correlation_ids": processed_events
        }
        
    except Exception as e:
        log_error_event(
            f"Error crítico procesando lote de eventos: {str(e)}",
            error_code="BATCH_PROCESSING_ERROR",
            error_details={
                "batch_size": len(event_batch.events),
                "error": str(e)
            }
        )
        raise HTTPException(
            status_code=500,
            detail=f"Error procesando eventos: {str(e)}"
        )

@router.get("/v1/sessions", summary="Obtener sesiones de agente")
async def get_sessions(
    user_id: Optional[str] = Query(None, description="Filtrar por ID de usuario"),
    device_id: Optional[str] = Query(None, description="Filtrar por ID de dispositivo"),
    correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación"),
    limit: int = Query(100, ge=1, le=1000, description="Límite de resultados"),
    skip: int = Query(0, ge=0, description="Número de resultados a omitir")
):
    """Obtener sesiones de agente con filtros y paginación."""
    try:
        session_repo = BrowserUseSessionRepository()
        
        # Construir filtros
        filters = {}
        if user_id:
            filters['user_id'] = user_id
        if device_id:
            filters['device_id'] = device_id
        if correlation_id:
            filters['correlation_id'] = correlation_id
        
        page = (skip // limit) + 1 if limit > 0 else 1
        result = await session_repo.find_paginated(
            query=filters,
            page=page,
            page_size=limit,
            sort=[('created_at', -1)]
        )
        sessions = result.items
        total_count = result.total
        
        browser_use_logger.info(
            f"Consultando {len(sessions)} sesiones (total: {total_count})",
            EventType.SESSION,
            metadata={
                "filters": filters,
                "limit": limit,
                "skip": skip,
                "results_count": len(sessions)
            }
        )
        
        return {
            "status": "success",
            "sessions": [session.dict() for session in sessions],
            "total": total_count,
            "limit": limit,
            "skip": skip,
            "filters": filters
        }
        
    except Exception as e:
        log_error_event(
            f"Error obteniendo sesiones: {str(e)}",
            error_code="GET_SESSIONS_ERROR",
            error_details={"error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo sesiones: {str(e)}")

@router.get("/v1/sessions/{session_id}", summary="Obtener sesión específica")
async def get_session(
    session_id: str
):
    """Obtener una sesión específica por su ID."""
    try:
        session_repo = BrowserUseSessionRepository()
        
        sessions = await session_repo.find({'session_id': session_id})
        session = sessions[0] if sessions else None
        
        if not session:
            raise HTTPException(status_code=404, detail="Sesión no encontrada")
        
        browser_use_logger.info(
            f"Consultando sesión: {session_id}",
            EventType.SESSION,
            metadata={"session_id": session_id}
        )
        
        return {
            "status": "success",
            "session": session.dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error obteniendo sesión {session_id}: {str(e)}",
            error_code="GET_SESSION_ERROR",
            error_details={"session_id": session_id, "error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo sesión: {str(e)}")

@router.get("/v1/tasks", summary="Obtener tareas de agente")
async def get_tasks(
    user_id: Optional[str] = Query(None, description="Filtrar por ID de usuario"),
    device_id: Optional[str] = Query(None, description="Filtrar por ID de dispositivo"),
    session_correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación de sesión"),
    correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación"),
    stopped: Optional[bool] = Query(None, description="Filtrar por estado detenido"),
    paused: Optional[bool] = Query(None, description="Filtrar por estado pausado"),
    limit: int = Query(100, ge=1, le=1000, description="Límite de resultados"),
    skip: int = Query(0, ge=0, description="Número de resultados a omitir")
):
    """Obtener tareas de agente con filtros y paginación."""
    try:
        task_repo = BrowserUseTaskRepository()
        
        # Construir filtros
        filters = {}
        if user_id:
            filters['user_id'] = user_id
        if device_id:
            filters['device_id'] = device_id
        if session_correlation_id:
            filters['session_correlation_id'] = session_correlation_id
        if correlation_id:
            filters['correlation_id'] = correlation_id
        if stopped is not None:
            filters['stopped'] = stopped
        if paused is not None:
            filters['paused'] = paused
        
        page = (skip // limit) + 1 if limit > 0 else 1
        result = await task_repo.find_paginated(
            query=filters,
            page=page,
            page_size=limit,
            sort=[('created_at', -1)]
        )
        tasks = result.items
        total_count = result.total
        
        browser_use_logger.info(
            f"Consultando {len(tasks)} tareas (total: {total_count})",
            EventType.TASK,
            metadata={
                "filters": filters,
                "limit": limit,
                "skip": skip,
                "results_count": len(tasks)
            }
        )
        
        return {
            "status": "success",
            "tasks": [task.dict() for task in tasks],
            "total": total_count,
            "limit": limit,
            "skip": skip,
            "filters": filters
        }
        
    except Exception as e:
        log_error_event(
            f"Error obteniendo tareas: {str(e)}",
            error_code="GET_TASKS_ERROR",
            error_details={"error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo tareas: {str(e)}")

@router.get("/v1/tasks/{task_id}", summary="Obtener tarea específica")
async def get_task(
    task_id: str,
    include_steps: bool = Query(True, description="Incluir pasos de la tarea"),
    include_files: bool = Query(True, description="Incluir archivos de la tarea")
):
    """Obtener una tarea específica por su ID con pasos y archivos relacionados."""
    try:
        task_repo = BrowserUseTaskRepository()
        step_repo = BrowserUseStepRepository()
        file_repo = BrowserUseFileRepository()
        
        # Obtener tarea
        tasks = await task_repo.find({'task_id': task_id})
        task = tasks[0] if tasks else None
        
        if not task:
            raise HTTPException(status_code=404, detail="Tarea no encontrada")
        
        result = {
            "status": "success",
            "task": task.dict()
        }
        
        # Obtener pasos si se solicita
        if include_steps:
            steps = await step_repo.find(
                query={'task_id': task_id},
                sort=[('step_number', 1)]
            )
            result["steps"] = [step.dict() for step in steps]
            result["steps_count"] = len(steps)
        
        # Obtener archivos si se solicita
        if include_files:
            files = await file_repo.find(
                query={'task_id': task_id},
                sort=[('created_at', 1)]
            )
            result["files"] = [file.dict() for file in files]
            result["files_count"] = len(files)
        
        browser_use_logger.info(
            f"Consultando tarea: {task_id}",
            EventType.TASK,
            metadata={
                "task_id": task_id,
                "include_steps": include_steps,
                "include_files": include_files,
                "steps_count": result.get("steps_count", 0),
                "files_count": result.get("files_count", 0)
            }
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error obteniendo tarea {task_id}: {str(e)}",
            error_code="GET_TASK_ERROR",
            error_details={"task_id": task_id, "error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo tarea: {str(e)}")

@router.get("/v1/tasks/{task_id}/steps", summary="Obtener pasos de una tarea")
async def get_task_steps(
    task_id: str,
    limit: int = Query(100, ge=1, le=1000, description="Límite de resultados"),
    skip: int = Query(0, ge=0, description="Número de resultados a omitir")
):
    """Obtener todos los pasos de una tarea específica con paginación."""
    try:
        task_repo = BrowserUseTaskRepository()
        step_repo = BrowserUseStepRepository()
        
        # Verificar que la tarea existe
        tasks = await task_repo.find({'task_id': task_id})
        task = tasks[0] if tasks else None
        if not task:
            raise HTTPException(status_code=404, detail="Tarea no encontrada")
        
        # Obtener pasos
        page = (skip // limit) + 1 if limit > 0 else 1
        result = await step_repo.find_paginated(
            query={'task_id': task_id},
            page=page,
            page_size=limit,
            sort=[('step_number', 1)]
        )
        steps = result.items
        total_count = result.total
        
        browser_use_logger.info(
            f"Consultando {len(steps)} pasos para tarea: {task_id} (total: {total_count})",
            EventType.STEP,
            metadata={
                "task_id": task_id,
                "limit": limit,
                "skip": skip,
                "results_count": len(steps),
                "total_count": total_count
            }
        )
        
        return {
            "status": "success",
            "task_id": task_id,
            "steps": [step.dict() for step in steps],
            "total": total_count,
            "limit": limit,
            "skip": skip
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error obteniendo pasos de tarea {task_id}: {str(e)}",
            error_code="GET_TASK_STEPS_ERROR",
            error_details={"task_id": task_id, "error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo pasos: {str(e)}")

@router.get("/v1/events", summary="Obtener todos los eventos")
async def get_events(
    event_type: Optional[str] = Query(None, description="Filtrar por tipo de evento"),
    user_id: Optional[str] = Query(None, description="Filtrar por ID de usuario"),
    device_id: Optional[str] = Query(None, description="Filtrar por ID de dispositivo"),
    correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación"),
    session_correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación de sesión"),
    task_correlation_id: Optional[str] = Query(None, description="Filtrar por ID de correlación de tarea"),
    source_ip: Optional[str] = Query(None, description="Filtrar por IP de origen"),
    date_from: Optional[str] = Query(None, description="Fecha desde (ISO format)"),
    date_to: Optional[str] = Query(None, description="Fecha hasta (ISO format)"),
    limit: int = Query(100, ge=1, le=1000, description="Límite de resultados"),
    skip: int = Query(0, ge=0, description="Número de resultados a omitir")
):
    """Obtener eventos con filtros avanzados y paginación."""
    try:
        event_repo = BrowserUseEventRepository()
        
        # Construir filtros
        filters = {}
        if event_type:
            filters['event_type'] = event_type
        if user_id:
            filters['user_id'] = user_id
        if device_id:
            filters['device_id'] = device_id
        if correlation_id:
            filters['correlation_id'] = correlation_id
        if session_correlation_id:
            filters['session_correlation_id'] = session_correlation_id
        if task_correlation_id:
            filters['task_correlation_id'] = task_correlation_id
        if source_ip:
            filters['source_ip'] = source_ip
        
        # Filtros de fecha
        date_filters = {}
        if date_from:
            try:
                date_filters['$gte'] = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha_desde inválido")
        if date_to:
            try:
                date_filters['$lte'] = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha_hasta inválido")
        
        if date_filters:
            filters['created_at'] = date_filters
        
        # Obtener eventos
        page = (skip // limit) + 1 if limit > 0 else 1
        result = await event_repo.find_paginated(
            query=filters,
            page=page,
            page_size=limit,
            sort=[('created_at', -1)]
        )
        events = result.items
        total_count = result.total
        
        browser_use_logger.info(
            f"Consultando {len(events)} eventos (total: {total_count})",
            "info",
            metadata={
                "filters": filters,
                "limit": limit,
                "skip": skip,
                "results_count": len(events),
                "total_count": total_count
            }
        )
        
        return {
            "status": "success",
            "events": [event.dict() for event in events],
            "total": total_count,
            "limit": limit,
            "skip": skip,
            "filters": {
                "event_type": event_type,
                "user_id": user_id,
                "device_id": device_id,
                "correlation_id": correlation_id,
                "session_correlation_id": session_correlation_id,
                "task_correlation_id": task_correlation_id,
                "source_ip": source_ip,
                "date_from": date_from,
                "date_to": date_to
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error obteniendo eventos: {str(e)}",
            error_code="GET_EVENTS_ERROR",
            error_details={"error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo eventos: {str(e)}")

@router.delete("/v1/events", summary="Limpiar todos los eventos")
async def clear_events(
    confirm: bool = Query(False, description="Confirmar eliminación de todos los eventos"),
    older_than_days: Optional[int] = Query(None, ge=1, description="Eliminar solo eventos más antiguos que X días")
):
    """Limpiar eventos almacenados con confirmación y filtros opcionales."""
    try:
        if not confirm:
            raise HTTPException(
                status_code=400, 
                detail="Debe confirmar la eliminación estableciendo confirm=true"
            )
        
        event_repo = BrowserUseEventRepository()
        session_repo = BrowserUseSessionRepository()
        task_repo = BrowserUseTaskRepository()
        step_repo = BrowserUseStepRepository()
        file_repo = BrowserUseFileRepository()
        
        # Construir filtros de fecha si se especifica
        date_filter = {}
        if older_than_days:
            cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)
            date_filter = {'created_at': {'$lt': cutoff_date}}
        
        # Contar elementos antes de eliminar
        events_count = await event_repo.count(date_filter)
        sessions_count = await session_repo.count(date_filter)
        tasks_count = await task_repo.count(date_filter)
        steps_count = await step_repo.count(date_filter)
        files_count = await file_repo.count(date_filter)
        
        # Eliminar elementos
        if date_filter:
            await event_repo.delete_many(date_filter)
            await session_repo.delete_many(date_filter)
            await task_repo.delete_many(date_filter)
            await step_repo.delete_many(date_filter)
            await file_repo.delete_many(date_filter)
            message = f"Eventos más antiguos que {older_than_days} días han sido eliminados"
        else:
            await event_repo.delete_many({})
            await session_repo.delete_many({})
            await task_repo.delete_many({})
            await step_repo.delete_many({})
            await file_repo.delete_many({})
            message = "Todos los eventos han sido eliminados"
        
        browser_use_logger.info(
            f"Limpieza completa: {events_count} eventos, {sessions_count} sesiones, {tasks_count} tareas, {steps_count} pasos, {files_count} archivos",
            "info",
            metadata={
                "events_deleted": events_count,
                "sessions_deleted": sessions_count,
                "tasks_deleted": tasks_count,
                "steps_deleted": steps_count,
                "files_deleted": files_count,
                "older_than_days": older_than_days
            }
        )
        
        return {
            "status": "success",
            "message": message,
            "deleted": {
                "events": events_count,
                "sessions": sessions_count,
                "tasks": tasks_count,
                "steps": steps_count,
                "files": files_count
            },
            "filter": {
                "older_than_days": older_than_days
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error limpiando eventos: {str(e)}",
            error_code="CLEAR_EVENTS_ERROR",
            error_details={"error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error limpiando eventos: {str(e)}")

@router.get("/v1/stats", summary="Estadísticas de eventos")
async def get_stats(
    user_id: Optional[str] = Query(None, description="Filtrar estadísticas por ID de usuario"),
    device_id: Optional[str] = Query(None, description="Filtrar estadísticas por ID de dispositivo"),
    date_from: Optional[str] = Query(None, description="Fecha desde (ISO format)"),
    date_to: Optional[str] = Query(None, description="Fecha hasta (ISO format)")
):
    """Obtener estadísticas detalladas de los eventos almacenados."""
    try:
        event_repo = BrowserUseEventRepository()
        session_repo = BrowserUseSessionRepository()
        task_repo = BrowserUseTaskRepository()
        step_repo = BrowserUseStepRepository()
        file_repo = BrowserUseFileRepository()
        
        # Construir filtros
        filters = {}
        if user_id:
            filters['user_id'] = user_id
        if device_id:
            filters['device_id'] = device_id
        
        # Filtros de fecha
        date_filters = {}
        if date_from:
            try:
                date_filters['$gte'] = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha_desde inválido")
        if date_to:
            try:
                date_filters['$lte'] = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Formato de fecha_hasta inválido")
        
        if date_filters:
            filters['created_at'] = date_filters
        
        # Obtener conteos
        total_events = await event_repo.count(filters)
        total_sessions = await session_repo.count(filters)
        total_tasks = await task_repo.count(filters)
        total_steps = await step_repo.count(filters)
        total_files = await file_repo.count(filters)
        
        # Obtener estadísticas por tipo de evento usando agregación
        pipeline = [
            {'$match': filters},
            {'$group': {'_id': '$event_type', 'count': {'$sum': 1}}}
        ]
        event_types_stats = await event_repo.aggregate(pipeline)
        
        # Estadísticas de tareas
        task_filters = filters.copy()
        active_tasks = await task_repo.count({**task_filters, 'stopped': False, 'paused': False})
        stopped_tasks = await task_repo.count({**task_filters, 'stopped': True})
        paused_tasks = await task_repo.count({**task_filters, 'paused': True})
        
        # Estadísticas temporales (últimas 24 horas)
        last_24h_filter = {
            **filters,
            'created_at': {
                **filters.get('created_at', {}),
                '$gte': datetime.utcnow() - timedelta(hours=24)
            }
        }
        
        events_last_24h = await event_repo.count(last_24h_filter)
        sessions_last_24h = await session_repo.count(last_24h_filter)
        tasks_last_24h = await task_repo.count(last_24h_filter)
        
        browser_use_logger.info(
            f"Consultando estadísticas: {total_events} eventos totales",
            "info",
            metadata={
                "filters": filters,
                "total_events": total_events,
                "total_sessions": total_sessions,
                "total_tasks": total_tasks
            }
        )
        
        return {
            "status": "success",
            "totals": {
                "events": total_events,
                "sessions": total_sessions,
                "tasks": total_tasks,
                "steps": total_steps,
                "files": total_files
            },
            "event_types": event_types_stats,
            "task_status": {
                "active": active_tasks,
                "stopped": stopped_tasks,
                "paused": paused_tasks,
                "completed": stopped_tasks  # Asumimos que stopped = completed
            },
            "last_24_hours": {
                "events": events_last_24h,
                "sessions": sessions_last_24h,
                "tasks": tasks_last_24h
            },
            "filters_applied": {
                "user_id": user_id,
                "device_id": device_id,
                "date_from": date_from,
                "date_to": date_to
            },
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log_error_event(
            f"Error obteniendo estadísticas: {str(e)}",
            error_code="GET_STATS_ERROR",
            error_details={"error": str(e)}
        )
        raise HTTPException(status_code=500, detail=f"Error obteniendo estadísticas: {str(e)}")