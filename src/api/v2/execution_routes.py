"""
Unified Test Execution API v2

Consolidates 15+ legacy endpoints into a single, unified API endpoint
that handles all types of test execution through discriminated unions.
"""

from typing import Dict, Any, Optional, Union, List, Literal
import os

from src.database.models.execution import Execution
from src.database.repositories.project_repository import ProjectRepository
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum
import logging

# Import unified components
from src.core.configuration_manager import get_config, ConfigProfile, Environment
from src.models.standard_result import StandardResult, TestType, ExecutionStatus
from src.core.execution_orchestrator import get_orchestrator

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(tags=["Test Execution v2"])

# Simple in-memory tracking for executions (temporary solution)
# This will be replaced with proper orchestrator integration once the singleton issue is resolved
_active_executions: Dict[str, Any] = {}

# Initialize project repository for ObjectId handling
_project_repo = ProjectRepository()

class ExecutionTypeEnum(str, Enum):
    """Types of test execution."""
    SMOKE = "smoke"
    FULL = "full"
    CASE = "case" 
    SUITE = "suite"
    CODEGEN = "codegen"


# Request Models using Discriminated Unions

class BaseExecutionRequest(BaseModel):
    """Base request for all execution types."""
    type: ExecutionTypeEnum
    config_profile: Optional[Union[str, ConfigProfile]] = "balanced"
    environment: Optional[Union[str, Environment]] = None
    environment_id: Optional[str] = Field(None, description="Environment ID to use for execution")
    application_version: Optional[str] = Field(None, description="Version of the application being tested")
    config_overrides: Optional[Dict[str, Any]] = None
    options: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(use_enum_values=True)


class SmokeTestRequest(BaseExecutionRequest):
    """Request model for smoke tests."""
    type: Literal[ExecutionTypeEnum.SMOKE] = ExecutionTypeEnum.SMOKE
    url: str = Field(..., description="Target URL for smoke test")
    instructions: Optional[str] = Field(None, description="Custom test instructions")
    max_steps: Optional[int] = Field(None, description="Override max steps")


class FullTestRequest(BaseExecutionRequest):
    """Request model for full tests."""
    type: Literal[ExecutionTypeEnum.FULL] = ExecutionTypeEnum.FULL
    url: str = Field(..., description="Target URL for full test")
    gherkin_scenarios: List[str] = Field(..., description="Gherkin scenarios to execute")
    instructions: Optional[str] = Field(None, description="Additional test instructions")


class TestCaseRequest(BaseExecutionRequest):
    """Request model for individual test case execution."""
    type: Literal[ExecutionTypeEnum.CASE] = ExecutionTypeEnum.CASE
    test_id: str = Field(..., description="Test case ID")
    project_id: Optional[str] = None
    suite_id: Optional[str] = None
    execution_times: int = Field(1, description="Number of times to execute")

    result: Optional[Dict[str, Any]] = None  # Changed to Dict to support custom serialization
    error: Optional[str] = None

    model_config = ConfigDict(use_enum_values=True)


class SuiteRequest(BaseExecutionRequest):
    """Request model for test suite execution."""
    type: Literal[ExecutionTypeEnum.SUITE] = ExecutionTypeEnum.SUITE
    suite_id: str = Field(..., description="Test suite ID")
    project_id: Optional[str] = None
    parallel: bool = Field(False, description="Execute tests in parallel")
    delay_between_tests: float = Field(0.5, description="Delay between test executions")


class CodegenRequest(BaseExecutionRequest):
    """Request model for codegen test execution."""
    type: Literal[ExecutionTypeEnum.CODEGEN] = ExecutionTypeEnum.CODEGEN
    session_id: Optional[str] = None
    execution_id: Optional[str] = None
    generated_code: Optional[str] = None
    convert_from_playwright: bool = Field(True, description="Convert from Playwright format")


# Discriminated Union for all request types
ExecutionRequest = Union[
    SmokeTestRequest,
    FullTestRequest, 
    TestCaseRequest,
    SuiteRequest,
    CodegenRequest
]


class ExecutionResponse(BaseModel):
    """Unified response model."""
    success: bool
    execution_id: str
    status: ExecutionStatus
    message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None  # Changed to Dict to support custom serialization
    error: Optional[str] = None

    model_config = ConfigDict(use_enum_values=True)


@router.post("/execute", response_model=ExecutionResponse)
async def execute_test(
    request: ExecutionRequest,
    background_tasks: BackgroundTasks
) -> ExecutionResponse:
    """
    Unified test execution endpoint.
    
    Handles all types of test execution by routing requests to the
    ExecutionOrchestrator. Starts execution in background and returns
    immediately with execution_id for real-time control.
    
    Args:
        request: Execution request (type determines the specific handler)
        background_tasks: FastAPI background tasks
        
    Returns:
        ExecutionResponse: Immediate response with execution_id and RUNNING status
    """
    try:
       
        # 1. Resolve configuration from MongoDB based on execution type
        logger.info(f"🔧 BROWSER CONFIG: Resolving configuration for execution type {request.type}")

        config = None
        try:
            from src.services.browser_configuration_service import BrowserConfigurationService
            browser_config_service = BrowserConfigurationService()

            # Get MongoDB configurations for this execution type
            mongo_configs = await browser_config_service.get_configurations_by_execution_type(
                request.type
            )

            if mongo_configs:
                # Use the first active configuration for this execution type
                mongo_config = mongo_configs[0]
                logger.info(f"🔧 BROWSER CONFIG: Found DB config '{mongo_config.name}' for type {request.type}")

                # USE MONGODB SETTINGS EXACTLY AS THEY COME - NO FILTERING, NO MAGIC
                settings = mongo_config.settings
                logger.info(f"🔧 BROWSER CONFIG: MongoDB settings: model_provider={settings.get('model_provider')}, model_name={settings.get('model_name')}, headless={settings.get('headless')}")

                # Fix: If provider is gemini but model_name is None/empty, assign a valid Gemini model
                if settings.get('model_provider') == 'gemini' and not settings.get('model_name'):
                    settings['model_name'] = 'gemini-1.5-flash'
                    logger.info(f"🔧 BROWSER CONFIG FIX: Assigned default Gemini model for provider=gemini: {settings['model_name']}")

                # Fix: If provider is openrouter but model_name is None/empty, assign a valid OpenRouter model
                if settings.get('model_provider') == 'openrouter' and not settings.get('model_name'):
                    settings['model_name'] = 'openai/gpt-4.1-mini'  # Use same model as JSON configs
                    logger.info(f"🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: {settings['model_name']}")

                # Create BrowserConfig using MongoDB settings directly
                from src.core.configuration_manager import BrowserConfig
                
                # Use ALL MongoDB settings as-is, let BrowserConfig handle what it needs
                config = BrowserConfig(**settings)

                # Create browser_config for browser-use using ALL MongoDB settings
                config.browser_config = type('BrowserUseConfig', (), settings)()

                logger.info(f"🔧 BROWSER CONFIG: Using MongoDB config AS-IS: headless={config.headless}, model_provider={getattr(config, 'model_provider', 'NOT_SET')}, model_name={getattr(config, 'model_name', 'NOT_SET')}")
            else:
                logger.warning(f"🔧 BROWSER CONFIG: No DB configs found for type {request.type}, using fallback")
                raise Exception("No MongoDB config found")

        except Exception as e:
            logger.warning(f"🔧 BROWSER CONFIG: DB config failed ({e}), using hardcoded fallback")

            # Fallback to hardcoded configurations
            from src.config.browser_config import BrowserConfigurations
            from src.core.configuration_manager import BrowserConfig

            type_config_map = {
                "case": BrowserConfigurations.get_test_case_config,
                "smoke": BrowserConfigurations.get_smoke_config,
                "full": BrowserConfigurations.get_exploration_config,
                "suite": BrowserConfigurations.get_test_suite_config,
                "codegen": BrowserConfigurations.get_exploration_config
            }

            config_func = type_config_map.get(request.type, BrowserConfigurations.get_test_case_config)
            hardcoded_config = config_func()

            # Convert to BrowserConfig format using only valid fields
            # Use the headless value from hardcoded_config instead of forcing True
            config = BrowserConfig(
                headless=getattr(hardcoded_config, 'headless', False),  # Default to visible (False) not headless
                use_vision=getattr(hardcoded_config, 'use_vision', True),
                enable_memory=False,
                max_steps=getattr(hardcoded_config, 'max_steps', 50),
                wait_between_actions=getattr(hardcoded_config, 'wait_between_actions', 1.0),
                minimum_wait_page_load_time=getattr(hardcoded_config, 'minimum_wait_page_load_time', 0.5),
                retry_delay=getattr(hardcoded_config, 'retry_delay', 10),
                max_failures=getattr(hardcoded_config, 'max_failures', 3),
                temperature=getattr(hardcoded_config, 'temperature', 0.1),
                disable_security=getattr(hardcoded_config, 'disable_security', True),
                stealth=getattr(hardcoded_config, 'stealth', False),
                generate_gif=getattr(hardcoded_config, 'generate_gif', False),
                highlight_elements=getattr(hardcoded_config, 'highlight_elements', True),  # Enable for debugging
                keep_alive=getattr(hardcoded_config, 'keep_alive', False),
                # Add OpenRouter as default model provider for fallback (matching JSON configs)
                model_provider='openrouter',
                model_name='openai/gpt-4.1-mini'  # Use same model as JSON configs
            )

            # Remove the smoke test override since now we respect config values
            # # Only smoke tests should be visual (headless=False)
            # if request.type == "smoke":
            #     config.headless = False

            logger.info(f"🔧 BROWSER CONFIG: Applied hardcoded fallback for type {request.type} with headless={config.headless}")

        # Apply any request-level overrides
        if request.config_overrides:
            for key, value in request.config_overrides.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                    logger.info(f"🔧 BROWSER CONFIG: Applied override {key}={value}")
        
        # 2. Prepare execution parameters from the request model
        # Exclude common fields already handled (type, config, etc.)
        execution_params = request.model_dump(exclude_unset=True)
        
        # Ensure environment_id is always included in execution_params
        if hasattr(request, 'environment_id') and request.environment_id:
            execution_params['environment_id'] = request.environment_id

        # 2.1. For TestCase requests, load the actual test case data
        if request.type == ExecutionTypeEnum.CASE and isinstance(request, TestCaseRequest):
            if not request.project_id or not request.suite_id:
                raise HTTPException(
                    status_code=400, 
                    detail="project_id and suite_id are required for test case execution"
                )
            
            # Load test case data from the project manager
            from src.core.test_service import TestService
            import os
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))
            
            test_case_data = await test_service.get_test_case(
                project_id=request.project_id,
                suite_id=request.suite_id, 
                test_id=request.test_id
            )
            
            if not test_case_data:
                raise HTTPException(
                    status_code=404,
                    detail=f"Test case {request.test_id} not found in project {request.project_id}, suite {request.suite_id}"
                )
            
       
            execution_params.update({
                "test_case_id": request.test_id,
                "test_id": request.test_id,  # Add this for execution history tracking
                "project_id": request.project_id,  # Ensure project_id is explicitly included
                "suite_id": request.suite_id,  # Ensure suite_id is explicitly included
                "environment_id": request.environment_id,  # Environment ID for multi-environment support
                "gherkin_scenario": test_case_data.get("gherkin", ""),
                "url": test_case_data.get("url", ""),
                "test_name": test_case_data.get("name", ""),
                "test_description": test_case_data.get("description", ""),
                "test_instructions": test_case_data.get("instrucciones", "")
            })
            
            # Add environment_name if environment_id is provided
            if request.environment_id:
                try:
                    project_doc = await _project_repo.get_by_project_id(request.project_id)
                    if project_doc:
                        environment = project_doc.get_environment(request.environment_id)
                        if environment:
                            execution_params["environment_name"] = environment.name
                            logger.info(f"🔍 EXECUTION DEBUG: Environment name resolved: {environment.name}")
                        else:
                            logger.warning(f"🔍 EXECUTION DEBUG: Environment {request.environment_id} not found in project {request.project_id}")
                    else:
                        logger.warning(f"🔍 EXECUTION DEBUG: Project {request.project_id} not found")
                except Exception as e:
                    logger.error(f"🔍 EXECUTION DEBUG: Failed to resolve environment name: {e}")
            else:
                logger.info(f"🔍 EXECUTION DEBUG: No environment_id provided")
      
        # 2.2. For Suite requests, load the suite and all test cases data
        elif request.type == ExecutionTypeEnum.SUITE and isinstance(request, SuiteRequest):
            if not request.project_id:
                raise HTTPException(
                    status_code=400, 
                    detail="project_id is required for test suite execution"
                )
            
            # Load test service and suite data
            from src.core.test_service import TestService
            import os
            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))
            
            # Get suite data
            suite_data = await test_service.get_test_suite(
                project_id=request.project_id,
                suite_id=request.suite_id
            )
            
            if not suite_data:
                raise HTTPException(
                    status_code=404,
                    detail=f"Test suite {request.suite_id} not found in project {request.project_id}"
                )
            
            # Get all test cases in the suite
            test_cases_data = await test_service.get_suite_test_cases(
                project_id=request.project_id,
                suite_id=request.suite_id
            )
            
            if not test_cases_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"No test cases found in suite {request.suite_id}"
                )
            
            # Format test cases for SuiteStrategy
            formatted_test_cases = []
            for i, test_case in enumerate(test_cases_data):
                formatted_test_case = {
                    "id": test_case.get("test_id"),
                    "gherkin_scenario": test_case.get("gherkin", ""),
                    "url": test_case.get("url", ""),
                    "name": test_case.get("name", ""),
                    "description": test_case.get("description", ""),
                    "instructions": test_case.get("instrucciones", "")
                }
                logger.info(f"🔍 SUITE API DEBUG: Formatted test case {i+1}: {formatted_test_case}")
                formatted_test_cases.append(formatted_test_case)
            
            logger.info(f"🔍 SUITE EXECUTION DEBUG: Loading suite {request.suite_id} with {len(formatted_test_cases)} test cases")
            
            # Add required fields for SuiteStrategy
            execution_params.update({
                "suite_id": request.suite_id,
                "project_id": request.project_id,
                "environment_id": request.environment_id,  # Environment ID for multi-environment support
                "test_cases": formatted_test_cases,
                "suite_name": suite_data.get("name", ""),
                "suite_description": suite_data.get("description", ""),
                "parallel": request.parallel,
                "delay_between_tests": request.delay_between_tests
            })
            
            # Add environment_name if environment_id is provided
            if request.environment_id:
                try:
                    project_doc = await _project_repo.get_by_project_id(request.project_id)
                    if project_doc:
                        environment = project_doc.get_environment(request.environment_id)
                        if environment:
                            execution_params["environment_name"] = environment.name
                            logger.info(f"🔍 SUITE EXECUTION DEBUG: Environment name resolved: {environment.name}")
                        else:
                            logger.warning(f"🔍 SUITE EXECUTION DEBUG: Environment {request.environment_id} not found in project {request.project_id}")
                    else:
                        logger.warning(f"🔍 SUITE EXECUTION DEBUG: Project {request.project_id} not found")
                except Exception as e:
                    logger.error(f"🔍 SUITE EXECUTION DEBUG: Failed to resolve environment name: {e}")
            else:
                logger.info(f"🔍 SUITE EXECUTION DEBUG: No environment_id provided")
            
           
        # 3. Create execution context immediately
        from src.core.execution_context import ExecutionContext
        from src.core.execution_orchestrator import get_orchestrator
        import uuid
        
        orchestrator = await get_orchestrator()
        
        # Generate execution ID and create context with proper parameters
        execution_id = str(uuid.uuid4())
        
        context = ExecutionContext(
            execution_id=execution_id,
            test_type=TestType(request.type),
            config_profile=f"db_resolved_{request.type}",  # Descriptive name for resolved config
            environment=request.environment,
            config_overrides=request.config_overrides or {},
            metadata=execution_params,
            resolved_config=config
        )
        
        # Set application version if provided
        if hasattr(request, 'application_version') and request.application_version:
            logger.info(f"🔍 CONTEXT DEBUG: Setting application_version to: {request.application_version}")
            context.set_application_version(request.application_version)
        else:
            logger.info(f"🔍 CONTEXT DEBUG: No application_version provided or empty")
        
        # Set the orchestrator in the context for SuiteStrategy to use
        context.set_orchestrator(orchestrator)
        
        
        # 4. Start execution in background
        async def run_execution():
            """Background task to run the actual execution."""
            try:
                logger.info(f"Background execution started for {context.execution_id}")
                result = await orchestrator.execute(context)
                logger.info(f"Background execution completed for {context.execution_id} with status {result.status}")
                
                # Update simple tracking with final status
                if context.execution_id in _active_executions:
                    _active_executions[context.execution_id]["status"] = result.status
                    _active_executions[context.execution_id]["result"] = result
                    
            except Exception as e:
                logger.error(f"Background execution failed for {context.execution_id}: {e}", exc_info=True)
                context.add_error(str(e))
                context.update_status(ExecutionStatus.ERROR)
                
                # Update simple tracking with error status
                if context.execution_id in _active_executions:
                    _active_executions[context.execution_id]["status"] = ExecutionStatus.ERROR
        
        # Register the context in active executions before starting background task
        logger.info(f"Registering execution {context.execution_id} in orchestrator.active_executions")
        logger.info(f"Orchestrator instance ID: {id(orchestrator)}")
        logger.info(f"Active executions before: {list(orchestrator.active_executions.keys())}")
        
        orchestrator.active_executions[context.execution_id] = context
        context.update_status(ExecutionStatus.RUNNING)
        
        # Also add to our simple in-memory tracking
        _active_executions[context.execution_id] = {
            "context": context,
            "status": ExecutionStatus.RUNNING,
            "orchestrator": orchestrator
        }
        
        logger.info(f"Active executions after: {list(orchestrator.active_executions.keys())}")
        logger.info(f"Successfully registered {context.execution_id}")
        logger.info(f"Simple tracking has: {list(_active_executions.keys())}")
        
        background_tasks.add_task(run_execution)
        
        # 5. Return immediate response with RUNNING status
        response = ExecutionResponse(
            success=True,
            execution_id=context.execution_id,
            status=ExecutionStatus.RUNNING,
            message="Execution started in background",
            result=None,  # No result yet since execution is running
            error=None
        )
        
        logger.info(f"Execution {context.execution_id} started and returned immediately")
        return response
        
    except Exception as e:
        logger.exception(f"Test execution request failed: {e}")
        
        # Create a structured error response for immediate failures
        import uuid
        error_execution_id = str(uuid.uuid4())
        
        return ExecutionResponse(
            success=False,
            execution_id=error_execution_id,
            status=ExecutionStatus.ERROR,
            message=f"Failed to start execution: {str(e)}",
            result=None,
            error=str(e)
        )


@router.get("/execution/{execution_id}", response_model=ExecutionResponse)
async def get_execution_status(execution_id: str) -> ExecutionResponse:
    """Get execution status by ID."""
    try:
        # First check our simple in-memory tracking
        if execution_id in _active_executions:
            tracking_data = _active_executions[execution_id]
            context = tracking_data["context"]
            status = tracking_data["status"]
            result = tracking_data.get("result")
            
            logger.info(f"Found execution {execution_id} in simple tracking with status {status}")
            
            # If execution is complete, check MongoDB for updated data (AI analysis may have been added)
            if status in [ExecutionStatus.SUCCESS, ExecutionStatus.FAILURE, ExecutionStatus.ERROR]:
                logger.info(f"🔍 Execution {execution_id} is complete, checking MongoDB for latest data including AI analysis")
                try:
                    # Try to get the latest data from MongoDB
                    orchestrator = await get_orchestrator()
                    saved_result = await orchestrator.get_saved_execution_result(execution_id)
                    if saved_result:
                        logger.info(f"✅ Found updated execution data in MongoDB for {execution_id}")
                        # DEBUG: Log the MongoDB result
                        if hasattr(saved_result, 'metadata'):
                            logger.info(f"📋 MongoDB metadata present: {bool(saved_result.metadata)}")
                            if saved_result.metadata:
                                logger.info(f"🧠 AI Analysis in MongoDB metadata: {bool(saved_result.metadata.get('ai_analysis'))}")
                                logger.info(f"📊 AI Analysis Status in MongoDB metadata: {saved_result.metadata.get('ai_analysis_status')}")
                        
                        saved_result_dict = saved_result.to_dict() if hasattr(saved_result, 'to_dict') else saved_result
                        return ExecutionResponse(
                            success=True,
                            execution_id=execution_id,
                            status=saved_result.status,
                            message="Execution completed",
                            result=saved_result_dict,
                            error=saved_result.error
                        )
                    else:
                        logger.warning(f"⚠️ No data found in MongoDB for {execution_id}, using in-memory data")
                except Exception as e:
                    logger.error(f"❌ Error getting data from MongoDB for {execution_id}: {e}, using in-memory data")
                
                # Fallback to in-memory data if MongoDB query fails
                if result:
                    result_dict = result.to_dict() if hasattr(result, 'to_dict') else result
                    return ExecutionResponse(
                        success=True,
                        execution_id=execution_id,
                        status=status,
                        message="Execution completed",
                        result=result_dict,
                        error=getattr(result, 'error', None)
                    )
            else:
                # Execution still in progress
                return ExecutionResponse(
                    success=True,
                    execution_id=execution_id,
                    status=status,
                    message="Execution in progress",
                    result=None,
                    error=None
                )
        
        # Try to get the execution from the orchestrator first (active executions)
        orchestrator = await get_orchestrator()
        logger.info(f"Getting execution status for {execution_id}")
        logger.info(f"Orchestrator instance ID: {id(orchestrator)}")
        logger.info(f"Has active_executions attr: {hasattr(orchestrator, 'active_executions')}")
        logger.info(f"Active executions: {list(orchestrator.active_executions.keys()) if hasattr(orchestrator, 'active_executions') else 'N/A'}")
        
        if hasattr(orchestrator, 'active_executions') and execution_id in orchestrator.active_executions:
            context = orchestrator.active_executions[execution_id]
            logger.info(f"Found active execution {execution_id} with status {context.status}")
            # For running executions, return status in ExecutionResponse format
            return ExecutionResponse(
                success=True,
                execution_id=execution_id,
                status=context.status,
                message="Execution in progress",
                result=None,  # No result yet since execution is running
                error=None
            )
        
        # Try to get completed execution from saved results
        logger.info(f"Execution {execution_id} not found in active executions, checking saved results")
        saved_result = await orchestrator.get_saved_execution_result(execution_id)
        if saved_result:
            logger.info(f"Found saved execution {execution_id}")
            # DEBUG: Log the saved result before returning
            logger.info(f"🔍 Returning saved execution status for {execution_id}")
            logger.info(f"📊 Saved result type: {type(saved_result)}")
            if hasattr(saved_result, 'metadata'):
                logger.info(f"📋 Saved metadata present: {bool(saved_result.metadata)}")
                if saved_result.metadata:
                    logger.info(f"🧠 AI Analysis in saved metadata: {bool(saved_result.metadata.get('ai_analysis'))}")
                    logger.info(f"📊 AI Analysis Status in saved metadata: {saved_result.metadata.get('ai_analysis_status')}")
            
            saved_result_dict = saved_result.to_dict()
            logger.info(f"📋 Saved result dict keys: {list(saved_result_dict.keys()) if isinstance(saved_result_dict, dict) else 'Not a dict'}")
            if isinstance(saved_result_dict, dict) and 'metadata' in saved_result_dict:
                logger.info(f"🧠 AI Analysis in saved result dict: {bool(saved_result_dict['metadata'].get('ai_analysis') if saved_result_dict['metadata'] else False)}")
            
            return ExecutionResponse(
                success=True,
                execution_id=execution_id,
                status=saved_result.status,
                message="Execution completed",
                result=saved_result_dict,
                error=saved_result.error
            )
        
        # Execution not found
        logger.warning(f"Execution {execution_id} not found anywhere")
        raise HTTPException(
            status_code=404, 
            detail=f"Execution {execution_id} not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving execution {execution_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/execution/{execution_id}/pause", status_code=202)
async def pause_execution(execution_id: str) -> Dict[str, str]:
    """Pause a running execution."""
    # Check simple tracking first
    if execution_id in _active_executions:
        tracking_data = _active_executions[execution_id]
        context = tracking_data["context"]
        context.pause()
        tracking_data["status"] = ExecutionStatus.PAUSED
        return {"status": "accepted", "message": f"Execution {execution_id} pause request accepted."}
    
    # Fallback to orchestrator
    orchestrator = await get_orchestrator()
    success = await orchestrator.pause_execution(execution_id)
    if not success:
        raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found or could not be paused.")
    
    return {"status": "accepted", "message": f"Execution {execution_id} pause request accepted."}


@router.post("/execution/{execution_id}/resume", status_code=202)
async def resume_execution(execution_id: str) -> Dict[str, str]:
    """Resume a paused execution."""
    # Check simple tracking first
    if execution_id in _active_executions:
        tracking_data = _active_executions[execution_id]
        context = tracking_data["context"]
        context.resume()
        tracking_data["status"] = ExecutionStatus.RUNNING
        return {"status": "accepted", "message": f"Execution {execution_id} resume request accepted."}
    
    # Fallback to orchestrator
    orchestrator = await get_orchestrator()
    success = await orchestrator.resume_execution(execution_id)
    if not success:
        raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found or could not be resumed.")

    return {"status": "accepted", "message": f"Execution {execution_id} resume request accepted."}


@router.post("/execution/{execution_id}/stop", status_code=202)
async def stop_execution(execution_id: str) -> Dict[str, str]:
    """Stop a running execution."""
    # Check simple tracking first
    if execution_id in _active_executions:
        tracking_data = _active_executions[execution_id]
        context = tracking_data["context"]
        context.cancel()
        tracking_data["status"] = ExecutionStatus.CANCELLED
        return {"status": "accepted", "message": f"Execution {execution_id} stop request accepted."}
    
    # Fallback to orchestrator
    orchestrator = await get_orchestrator()
    success = await orchestrator.stop_execution(execution_id)
    if not success:
        raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found or could not be stopped.")

    return {"status": "accepted", "message": f"Execution {execution_id} stop request accepted."}


@router.get("/config/profiles")
async def get_available_profiles() -> Dict[str, List[str]]:
    """Get available configuration profiles and environments."""
    from src.core.configuration_manager import config_manager
    
    return {
        "profiles": config_manager.get_available_profiles(),
        "environments": config_manager.get_available_environments(),
        "legacy_configs": config_manager.get_legacy_configs()
    }


@router.post("/validate")
async def validate_request(request: ExecutionRequest) -> Dict[str, Any]:
    """Validate execution request without executing."""
    try:
        # Validate configuration
        config = get_config(
            request.config_profile,
            request.environment, 
            request.config_overrides
        )
        
        return {
            "valid": True,
            "resolved_config": config.__dict__,
            "message": "Request is valid"
        }
        
    except Exception as e:
        return {
            "valid": False,
            "error": str(e),
            "message": "Request validation failed"
        }


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """Basic health check."""
    return {"status": "ok"}


@router.get("/{project_id}/{suite_id}/{test_id}/executions")
async def get_test_executions(
    project_id: str, 
    suite_id: str, 
    test_id: str,
    environment_id: Optional[str] = None,
    application_version: Optional[str] = None,
    limit: int = 20,  # Límite por defecto para paginación
    include_ai_analysis: bool = True,  # Controlar si incluir análisis de IA
    summary_only: bool = False  # Solo información resumida
):
    """Get all executions for a specific test case."""
    try:
        logger.info(f"🔍 Getting executions for test_id: {test_id}")
        logger.info(f"🔍 Project ID: {project_id}, Suite ID: {suite_id}")
        
        # Obtener el orquestador para acceder al repositorio de ejecuciones
        orchestrator = await get_orchestrator()
        
        if not orchestrator.execution_repo:
            logger.error(f"❌ Execution repository not initialized")
            raise HTTPException(status_code=500, detail="Execution repository not initialized")
            
        logger.info(f"✅ Orchestrator and execution repository initialized")
        
        # Usar get_by_project con los nuevos filtros y límite optimizado
        executions = await orchestrator.execution_repo.get_by_project(
            project_id=project_id,
            environment_id=environment_id,
            application_version=application_version,
            limit=limit * 2  # Obtener más para filtrar, pero limitado
        )
        
        # Filtrar por test_id y suite_id en Python ya que el query es más complejo
        filtered_executions = [
            ex for ex in executions 
            if ex.test_id == test_id and ex.suite_id == suite_id
        ]
        
        # Aplicar límite después del filtrado
        executions = filtered_executions[:limit]
        total_available = len(filtered_executions)
        
        logger.info(f"📊 Found {total_available} total executions, returning {len(executions)} (limit: {limit}) for test_id: {test_id}")
        
        if not executions:
            logger.info(f"📭 No executions found for test_id: {test_id}, returning empty list")
            return {
                "executions": [],
                "total_count": 0,
                "test_id": test_id,
                "project_id": project_id,
                "suite_id": suite_id,
                "message": f"No executions found for test case {test_id}"
            }
            
        # Convertir cada ejecución a formato de respuesta de manera optimizada
        results = []
        for i, execution in enumerate(executions):
            logger.debug(f"🔄 Processing execution {i+1}/{len(executions)}: {execution.execution_id}")
            
            try:
                if summary_only:
                    # Usar método optimizado para solo resumen
                    summary_data = execution.get_execution_summary_for_ui()
                    response = {
                        "success": True,
                        "execution_id": execution.execution_id,
                        "status": execution.status,
                        "message": execution.message or "Execution completed",
                        "result": summary_data,
                        "error": execution.error
                    }
                else:
                    # Convertir documento de MongoDB a StandardResult
                    standard_result = execution.to_standard_result() if hasattr(execution, "to_standard_result") else None
                    
                    # Optimizar metadata si no se necesita AI analysis
                    if standard_result and not include_ai_analysis:
                        # Remover campos de AI analysis para reducir payload
                        if standard_result.metadata:
                            ai_fields = ['ai_analysis', 'ai_insights', 'test_analysis', 'step_analysis']
                            for field in ai_fields:
                                standard_result.metadata.pop(field, None)
                    
                    response = {
                        "success": True,
                        "execution_id": execution.execution_id,
                        "status": execution.status,
                        "message": execution.message or "Execution completed",
                        "result": standard_result.to_dict_optimized() if standard_result else None,
                        "error": execution.error
                    }
                
                logger.debug(f"✅ Processing successful for execution {execution.execution_id}")
            except Exception as e:
                logger.error(f"❌ Processing failed for execution {execution.execution_id}: {e}")
                response = {
                    "success": False,
                    "execution_id": execution.execution_id,
                    "status": "error",
                    "message": f"Failed to process execution: {str(e)}",
                    "result": None,
                    "error": str(e)
                }
            
            results.append(response)
            
        logger.info(f"✅ Successfully processed {len(results)} executions for test_id: {test_id}")
        
        # Return with additional metadata for better frontend handling and pagination info
        return {
            "executions": results,
            "total_count": len(results),
            "total_available": total_available,
            "limit": limit,
            "has_more": total_available > limit,
            "test_id": test_id,
            "project_id": project_id,
            "suite_id": suite_id,
            "summary_only": summary_only,
            "include_ai_analysis": include_ai_analysis,
            "message": f"Found {len(results)} executions (of {total_available} total) for test case {test_id}"
        }
        
    except Exception as e:
        logger.exception(f"❌ Failed to get test executions for test_id {test_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get test executions: {str(e)}")


@router.get("/{project_id}/{suite_id}/{test_id}/executions/summary")
async def get_test_executions_summary(
    project_id: str, 
    suite_id: str, 
    test_id: str,
    environment_id: Optional[str] = None,
    application_version: Optional[str] = None,
    limit: int = 50  # Límite más alto para resúmenes ya que son más ligeros
):
    """Get execution summaries for a specific test case (optimized for performance)."""
    try:
        logger.info(f"🚀 Getting execution summaries for test_id: {test_id}")
        
        # Obtener el orquestador para acceder al repositorio de ejecuciones
        orchestrator = await get_orchestrator()
        
        if not orchestrator.execution_repo:
            logger.error(f"❌ Execution repository not initialized")
            raise HTTPException(status_code=500, detail="Execution repository not initialized")
            
        # Usar get_by_project con límite optimizado
        executions = await orchestrator.execution_repo.get_by_project(
            project_id=project_id,
            environment_id=environment_id,
            application_version=application_version,
            limit=limit * 2
        )
        
        # Filtrar por test_id y suite_id
        filtered_executions = [
            ex for ex in executions 
            if ex.test_id == test_id and ex.suite_id == suite_id
        ]
        
        # Aplicar límite
        executions = filtered_executions[:limit]
        total_available = len(filtered_executions)
        
        logger.info(f"📊 Found {total_available} total executions, returning {len(executions)} summaries")
        
        if not executions:
            return {
                "summaries": [],
                "total_count": 0,
                "total_available": 0,
                "test_id": test_id,
                "message": f"No executions found for test case {test_id}"
            }
            
        # Convertir a resúmenes optimizados
        summaries = []
        for execution in executions:
            try:
                summary = execution.get_execution_summary_for_ui()
                summaries.append(summary)
            except Exception as e:
                logger.error(f"❌ Failed to get summary for execution {execution.execution_id}: {e}")
                # Crear resumen básico en caso de error
                summaries.append({
                    'execution_id': execution.execution_id,
                    'status': execution.status,
                    'error': str(e)
                })
        
        logger.info(f"✅ Successfully processed {len(summaries)} execution summaries")
        
        return {
            "summaries": summaries,
            "total_count": len(summaries),
            "total_available": total_available,
            "limit": limit,
            "has_more": total_available > limit,
            "test_id": test_id,
            "project_id": project_id,
            "suite_id": suite_id,
            "message": f"Found {len(summaries)} execution summaries (of {total_available} total)"
        }
        
    except Exception as e:
        logger.exception(f"❌ Failed to get execution summaries for test_id {test_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get execution summaries: {str(e)}")


@router.delete("/executions/{execution_id}", status_code=200)
async def delete_execution(execution_id: str):
    """Delete an execution by its ID."""
    try:
        orchestrator = await get_orchestrator()
        if not orchestrator.execution_repo:
            raise HTTPException(status_code=500, detail="Execution repository not initialized")

        success = await orchestrator.execution_repo.delete_by_execution_id(execution_id)

        if not success:
            raise HTTPException(status_code=404, detail=f"Execution {execution_id} not found")

        return {"status": "success", "message": f"Execution {execution_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to delete execution {execution_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete execution: {str(e)}")

