"""Rutas de API para gestión de casos de prueba."""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse

from src.core.test_service import TestService
from src.api.models import (
    TestCaseCreateRequest,
    TestCaseUpdateRequest,
    TestCaseStatusUpdateRequest,
    TestCaseResponse,
    TestExecutionResponse,
    SuccessResponse
)
import os

# Router para casos de prueba
router = APIRouter(
    prefix="/api/projects/{project_id}/suites/{suite_id}/tests",
    tags=["Test Cases"]
)


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


@router.post("/", response_model=TestCaseResponse, summary="Create a new test case", description="Creates a new test case within a specified project and test suite.")
async def create_test_case(
    project_id: str,
    suite_id: str,
    request: TestCaseCreateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Crea un nuevo caso de prueba en una suite."""
    try:
        import logging
        logger = logging.getLogger(__name__)
        
        # Log the incoming request data for debugging
        logger.info(f"📝 Request data: name='{request.name}', description='{request.description}', url='{request.url}'")
        logger.info(f"📝 Request fields: instrucciones={len(request.instrucciones) if request.instrucciones else 0} chars, "
                   f"gherkin={len(request.gherkin) if request.gherkin else 0} chars, "
                   f"historia_de_usuario={len(request.historia_de_usuario) if request.historia_de_usuario else 0} chars")
        logger.info(f"📝 Tags: {request.tags}")
        
        test_case_data = await test_service.create_test_case(
            project_id=project_id,
            suite_id=suite_id,
            name=request.name,
            description=request.description,
            instrucciones=request.instrucciones,
            historia_de_usuario=request.historia_de_usuario,
            gherkin=request.gherkin,
            url=request.url,
            tags=request.tags
        )
        if not test_case_data:
            raise HTTPException(status_code=404, detail="Suite de pruebas no encontrada")

        return TestCaseResponse(**test_case_data)
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"❌ Validation error creating test case: {e}")
        raise HTTPException(status_code=422, detail=f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"❌ Unexpected error creating test case: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{test_id}", response_model=TestCaseResponse, summary="Obtener caso de prueba", operation_id="get_testcase_by_id")
async def get_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene un caso de prueba por su ID."""
    try:
        import logging
        logger = logging.getLogger(__name__)
                
        test_case_data = await test_service.get_test_case(project_id, suite_id, test_id)
        
        if not test_case_data:
            logger.warning(f"❌ Test case not found: project_id={project_id}, suite_id={suite_id}, test_id={test_id}")
            raise HTTPException(status_code=404, detail="Caso de prueba no encontrado")

        logger.info(f"✅ Test case found: {test_case_data.get('name', 'Unknown name')}")
        return TestCaseResponse(**test_case_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error getting test case: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{test_id}", response_model=TestCaseResponse, summary="Actualizar caso de prueba", operation_id="update_testcase_by_id", description="Updates an existing test case with new data.")
async def update_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    request: TestCaseUpdateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Actualiza un caso de prueba existente."""
    try:
        test_case_data = await test_service.update_test_case(
            project_id=project_id,
            suite_id=suite_id,
            test_id=test_id,
            name=request.name,
            description=request.description,
            instrucciones=request.instrucciones,
            historia_de_usuario=request.historia_de_usuario,
            gherkin=request.gherkin,
            url=request.url,
            tags=request.tags
        )
        if not test_case_data:
            raise HTTPException(status_code=404, detail="Caso de prueba no encontrado")

        return TestCaseResponse(**test_case_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/{test_id}/status", response_model=TestCaseResponse,
             summary="Actualizar estado del caso de prueba", operation_id="update_testcase_status_by_id")
async def update_test_case_status(
    project_id: str,
    suite_id: str,
    test_id: str,
    request: TestCaseStatusUpdateRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Actualiza el estado de un caso de prueba."""
    try:
        test_case_data = await test_service.update_test_case_status(
            project_id=project_id,
            suite_id=suite_id,
            test_id=test_id,
            status=request.status
        )
        if not test_case_data:
            raise HTTPException(status_code=404, detail="Caso de prueba no encontrado")

        return TestCaseResponse(**test_case_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{test_id}", summary="Eliminar caso de prueba", operation_id="delete_testcase_by_id", description="Deletes a test case by its ID.", status_code=204)
async def delete_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Elimina un caso de prueba."""
    try:
        success = await test_service.delete_test_case(project_id, suite_id, test_id)
        if not success:
            raise HTTPException(status_code=404, detail="Caso de prueba no encontrado")

        return
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class TestCaseExecutionRequest(BaseModel):
    execution_times: int = Field(1, gt=0, description="Número de veces que se ejecutará el caso de prueba")


@router.post("/{test_id}/execute", response_model=TestExecutionResponse,
            summary="Ejecutar caso de prueba", operation_id="execute_testcase_by_id")
async def execute_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    request: TestCaseExecutionRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta un caso de prueba específico."""
    try:
        all_results = []
        for _ in range(request.execution_times):
            result = await test_service.execute_test_case(project_id, suite_id, test_id)
            all_results.append(result)

        # Aquí puedes decidir cómo agregar los resultados. Por ahora, solo devolvemos el último.
        final_result = all_results[-1]

        # Importar las funciones de transformación desde response_transformers
        from src.utilities.response_transformers import transform_backend_response_to_frontend_format, clean_data_for_json_serialization

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(final_result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)

        # Crear respuesta estructurada
        response_data = {
            "success": cleaned_result.get("metadata", {}).get("success", False),
            "test_id": test_id,
            "result": cleaned_result,
            "error": cleaned_result.get("error") if not cleaned_result.get("metadata", {}).get("success", False) else None
        }

        return TestExecutionResponse(**response_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
