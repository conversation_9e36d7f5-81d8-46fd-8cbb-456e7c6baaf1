"""
Background Jobs API Routes

FastAPI routes for managing background jobs and analysis tasks.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

# Check if background jobs are available
from src.core.background_jobs import BACKGROUND_JOBS_AVAILABLE

if BACKGROUND_JOBS_AVAILABLE:
    from src.core.background_jobs.job_manager import get_job_manager
    from src.core.background_jobs.tasks.analysis_tasks import analyze_test_background, analyze_tests_batch
    from src.core.background_jobs.models import JobStatus
else:
    get_job_manager = None
    analyze_test_background = None
    analyze_tests_batch = None
    JobStatus = None

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v2/background-jobs", tags=["Background Jobs"])


@router.get("/health")
async def background_jobs_health():
    """
    Health check for background jobs system.
    
    Returns:
        Dict with system status
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        return {
            "status": "unavailable",
            "message": "Background jobs dependencies not installed",
            "install_command": "pip install celery[redis] redis"
        }
    
    try:
        # Test Redis connection (Upstash or local)
        import os
        upstash_url = os.getenv("UPSTASH_REDIS_REST_URL")
        upstash_token = os.getenv("UPSTASH_REDIS_REST_TOKEN")
        
        if upstash_url and upstash_token:
            import urllib.parse
            host = urllib.parse.urlparse(upstash_url).netloc
            redis_url = f"rediss://default:{upstash_token}@{host}:6380"
            redis_type = f"Upstash ({host})"
        else:
            redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            redis_type = "Local Redis"
        
        from redis import Redis
        redis_client = Redis.from_url(redis_url, decode_responses=True)
        redis_client.ping()
        redis_status = f"connected to {redis_type}"
    except Exception as e:
        redis_status = f"disconnected: {str(e)}"
    
    try:
        # Test Celery worker availability
        from src.core.background_jobs.celery_app import celery_app
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        worker_count = len(active_workers) if active_workers else 0
        worker_status = f"{worker_count} workers active"
    except Exception as e:
        worker_status = f"unavailable: {str(e)}"
    
    return {
        "status": "available",
        "components": {
            "redis": redis_status,
            "celery_workers": worker_status
        },
        "message": "Background jobs system is ready"
    }


class StartAnalysisRequest(BaseModel):
    """Request to start background analysis."""
    execution_id: str = Field(..., description="Test execution ID to analyze")
    result_data: Dict[str, Any] = Field(..., description="Test result data")
    screenshot_data: Optional[list] = Field(None, description="Optional screenshot data")


class BatchAnalysisRequest(BaseModel):
    """Request to start batch analysis for multiple test executions."""
    tests: list[Dict[str, Any]] = Field(..., description="List of test data for batch processing")
    batch_size: Optional[int] = Field(5, description="Maximum number of tests to process in a single batch")
    
    class Config:
        schema_extra = {
            "example": {
                "tests": [
                    {
                        "execution_id": "exec_123",
                        "result_data": {"steps": [], "success": True},
                        "screenshot_data": None
                    },
                    {
                        "execution_id": "exec_456", 
                        "result_data": {"steps": [], "success": False},
                        "screenshot_data": ["base64_data"]
                    }
                ],
                "batch_size": 3
            }
        }


class JobStatusResponse(BaseModel):
    """Job status response."""
    job_id: str
    execution_id: str
    status: str
    progress: int
    message: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    has_result: bool
    has_error: bool


class JobResultResponse(BaseModel):
    """Job result response."""
    job_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time_ms: Optional[int] = None


@router.post("/analysis/start", response_model=Dict[str, Any])
async def start_analysis(request: StartAnalysisRequest):
    """
    Start background analysis for a test execution.
    
    Args:
        request: Analysis request data
        
    Returns:
        Dict with job_id and initial status
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    try:
        job_manager = get_job_manager()
        
        # Create job
        job_id = job_manager.create_job(request.execution_id, "analysis")
        
        # Start background task
        analyze_test_background.delay(
            job_id=job_id,
            result_data=request.result_data,
            screenshot_data=request.screenshot_data
        )
        
        logger.info(f"Started background analysis job {job_id} for execution {request.execution_id}")
        
        return {
            "job_id": job_id,
            "execution_id": request.execution_id,
            "status": JobStatus.PENDING.value,
            "message": "Análisis iniciado en background. Use el job_id para consultar el progreso.",
            "endpoints": {
                "status": f"/api/v2/background-jobs/{job_id}/status",
                "result": f"/api/v2/background-jobs/{job_id}/result"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start analysis: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to start analysis: {str(e)}")


@router.post("/analysis/batch", response_model=Dict[str, Any])
async def start_batch_analysis(request: BatchAnalysisRequest):
    """
    Start batch analysis for multiple test executions to optimize costs.
    
    Args:
        request: Batch analysis request data
        
    Returns:
        Dict with batch_job_id and individual job_ids
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    try:
        job_manager = get_job_manager()
        
        # Validate batch size
        batch_size = min(request.batch_size or 5, 10)  # Max 10 tests per batch
        tests = request.tests[:batch_size]  # Limit to batch size
        
        if not tests:
            raise HTTPException(status_code=400, detail="No tests provided for batch analysis")
        
        # Create individual jobs for tracking
        job_ids = []
        for test_data in tests:
            execution_id = test_data.get("execution_id", "unknown")
            job_id = job_manager.create_job(execution_id, "batch_analysis")
            job_ids.append(job_id)
        
        # Create batch job ID for overall tracking
        batch_job_id = f"batch_{job_ids[0]}_{len(job_ids)}"
        
        # Start batch analysis task
        analyze_tests_batch.delay(
            batch_job_id=batch_job_id,
            job_ids=job_ids,
            tests_data=tests
        )
        
        logger.info(f"Started batch analysis {batch_job_id} with {len(tests)} tests")
        
        return {
            "batch_job_id": batch_job_id,
            "individual_job_ids": job_ids,
            "batch_size": len(tests),
            "status": JobStatus.PENDING.value,
            "message": f"Análisis por lotes iniciado para {len(tests)} pruebas. Optimización de costos activada.",
            "endpoints": {
                "individual_status": [f"/api/v2/background-jobs/{job_id}/status" for job_id in job_ids],
                "individual_results": [f"/api/v2/background-jobs/{job_id}/result" for job_id in job_ids]
            },
            "cost_optimization": {
                "enabled": True,
                "estimated_savings": f"{max(0, len(tests) - 1) * 20}% vs individual analysis",
                "batch_processing": True
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start batch analysis: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to start batch analysis: {str(e)}")


@router.get("/{job_id}/status", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """
    Get job status and progress.
    
    Args:
        job_id: Job identifier
        
    Returns:
        Job status information
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    job_manager = get_job_manager()
    
    status_info = job_manager.get_job_status(job_id)
    if not status_info:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    return JobStatusResponse(**status_info)


@router.get("/{job_id}/result", response_model=JobResultResponse)
async def get_job_result(job_id: str):
    """
    Get job result (only available after completion).
    
    Args:
        job_id: Job identifier
        
    Returns:
        Job result or error
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    job_manager = get_job_manager()
    
    result = job_manager.get_job_result(job_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    # Check if job is still running
    if result.status in [JobStatus.PENDING, JobStatus.RUNNING]:
        raise HTTPException(
            status_code=202, 
            detail=f"Job {job_id} is still {result.status.value.lower()}. Check status endpoint for progress."
        )
    
    return result


@router.post("/{job_id}/cancel")
async def cancel_job(job_id: str):
    """
    Cancel a running job.
    
    Args:
        job_id: Job identifier
        
    Returns:
        Cancellation confirmation
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    job_manager = get_job_manager()
    
    # Check if job exists
    job = job_manager.get_job(job_id)
    if not job:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")
    
    # Check if job can be cancelled
    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        raise HTTPException(
            status_code=400, 
            detail=f"Cannot cancel job {job_id} with status {job.status.value}"
        )
    
    # Cancel the job
    job_manager.cancel_job(job_id)
    
    logger.info(f"Cancelled job {job_id}")
    
    return {
        "job_id": job_id,
        "status": "CANCELLED",
        "message": f"Job {job_id} has been cancelled"
    }


@router.get("/", response_model=Dict[str, Any])
async def list_jobs(limit: int = 50, execution_id: Optional[str] = None):
    """
    List recent jobs (for monitoring/debugging).
    
    Args:
        limit: Maximum number of jobs to return
        execution_id: Filter by execution ID
        
    Returns:
        List of jobs
    """
    # This is a simplified implementation
    # In production, you might want to store job metadata in a separate index
    
    return {
        "message": "Job listing not implemented yet",
        "note": "Use specific job_id endpoints to query individual jobs"
    }


@router.post("/cleanup")
async def cleanup_old_jobs(background_tasks: BackgroundTasks, max_age_hours: int = 24):
    """
    Trigger cleanup of old jobs.
    
    Args:
        background_tasks: FastAPI background tasks
        max_age_hours: Maximum age in hours
        
    Returns:
        Cleanup confirmation
    """
    def cleanup():
        job_manager = get_job_manager()
        job_manager.cleanup_old_jobs(max_age_hours)
    
    background_tasks.add_task(cleanup)
    
    return {
        "message": f"Cleanup of jobs older than {max_age_hours} hours has been scheduled",
        "max_age_hours": max_age_hours
    }


@router.get("/analysis/{job_id}/result", response_model=Dict[str, Any])
async def get_analysis_result(job_id: str):
    """
    Get AI analysis result for a specific job.
    
    Args:
        job_id: Job identifier from test execution metadata
        
    Returns:
        AI analysis result or status information
    """
    if not BACKGROUND_JOBS_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="Background jobs not available. Please install: pip install celery[redis] redis"
        )
    
    job_manager = get_job_manager()
    
    result = job_manager.get_job_result(job_id)
    if not result:
        raise HTTPException(status_code=404, detail=f"Analysis job {job_id} not found")
    
    # Check if job is still running
    if result.status in [JobStatus.PENDING, JobStatus.RUNNING]:
        status_info = job_manager.get_job_status(job_id)
        return {
            "job_id": job_id,
            "status": "processing",
            "progress": status_info.get("progress", 0),
            "message": status_info.get("message", "Analysis in progress..."),
            "analysis_result": None
        }
    
    # Job completed - return result
    if result.status == JobStatus.SUCCESS:
        return {
            "job_id": job_id,
            "status": "completed", 
            "progress": 100,
            "message": "Analysis completed successfully",
            "analysis_result": result.result
        }
    else:
        return {
            "job_id": job_id,
            "status": "failed",
            "progress": 100,
            "message": f"Analysis failed: {result.error}",
            "analysis_result": None,
            "error": result.error
        }