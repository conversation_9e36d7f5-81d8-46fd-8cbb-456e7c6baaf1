"""Rutas de API para métricas y analíticas de pruebas - Versión corregida.

Esta versión usa ExecutionRepository como fuente única de verdad,
eliminando las discrepancias entre datos individuales y agregados.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query
from collections import defaultdict

from src.database.repositories.execution_repository import ExecutionRepository
from src.database.repositories.project_repository import ProjectRepository
from src.utilities.response_transformers import clean_data_for_json_serialization

router = APIRouter(tags=["Analytics"])


@router.get("/analytics-v2", summary="Obtener métricas agregadas de ejecución de tests (versión corregida)")
async def get_analytics_v2(
    start_date: Optional[str] = Query(None, description="Fecha inicio en formato YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="Fecha fin en formato YYYY-MM-DD"),
    project_id: Optional[str] = Query(None, description="Filtrar por ID de proyecto"),
    suite_id: Optional[str] = Query(None, description="Filtrar por ID de suite"),
    detailed: bool = Query(False, description="Incluir lista detallada de tests en la respuesta")
) -> Dict[str, Any]:
    """
    Calcula estadísticas usando ExecutionRepository como fuente única de verdad.
    Elimina las discrepancias entre datos individuales y agregados.
    """
    try:
        # 1. Parse date range
        today = datetime.utcnow().date()
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
        else:
            end_dt = today
        if start_date:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
        else:
            start_dt = end_dt - timedelta(days=29)

        # Convert to datetime for database queries
        start_datetime = datetime.combine(start_dt, datetime.min.time())
        end_datetime = datetime.combine(end_dt, datetime.max.time())

        # 2. Initialize repositories
        execution_repo = ExecutionRepository()
        project_repo = ProjectRepository()
        
        # 3. Create mappings for suite and test names
        suite_name_map = {}  # suite_id -> suite_name
        test_name_map = {}   # test_id -> test_name
        
        # Get all projects to build name mappings
        if project_id:
            projects = [await project_repo.get_by_project_id(project_id)] if await project_repo.get_by_project_id(project_id) else []
        else:
            projects = await project_repo.get_all()
        
        for project in projects:
            if project:
                # Map suite names
                for suite in project.test_suites.values():
                    suite_name_map[suite.suite_id] = suite.name
                    
                    # Map test names
                    for test_case in suite.test_cases.values():
                        test_name_map[test_case.test_id] = test_case.name

        # 4. Build query filters
        query_filters = {
            "started_at": {"$gte": start_datetime, "$lte": end_datetime}
        }
        if project_id:
            query_filters["project_id"] = project_id
        if suite_id:
            query_filters["suite_id"] = suite_id

        # 5. Get executions from database
        from src.database.models.execution import Execution
        executions = await Execution.find(query_filters).to_list()

        # 6. Calculate statistics
        total_tests_in_scope = 0
        tests_run_in_range = len(executions)
        passed_tests_in_range = 0
        durations: List[float] = []
        daily_map: Dict[str, Dict[str, int]] = defaultdict(lambda: {"passed": 0, "failed": 0})
        suite_stats_map: Dict[str, Dict[str, Any]] = {}
        test_details: List[Dict[str, Any]] = []

        # Process each execution
        for execution in executions:
            # Count passed/failed
            if execution.status == "success":
                passed_tests_in_range += 1
                status_key = "passed"
            else:
                status_key = "failed"

            # Add to daily statistics
            exec_date = execution.started_at.strftime("%Y-%m-%d")
            daily_map[exec_date][status_key] += 1

            # Collect duration if available
            if execution.duration_ms:
                durations.append(execution.duration_ms / 1000.0)  # Convert to seconds

            # Add to suite statistics
            if execution.suite_id:
                if execution.suite_id not in suite_stats_map:
                    suite_stats_map[execution.suite_id] = {
                        "suiteId": execution.suite_id,
                        "suiteName": suite_name_map.get(execution.suite_id, execution.metadata.get("suite_name", f"Suite {execution.suite_id[:8]}..." if execution.suite_id else "Sin Suite")),
                        "runsInDateRange": 0,
                        "passedInDateRange": 0,
                        "lifetimeTests": 0,  # Will be calculated separately
                        "lifetimeRuns": 0,   # Will be calculated separately
                        "lifetimePassed": 0, # Will be calculated separately
                        "lifetimeFailed": 0, # Will be calculated separately
                        "lifetimeSuccessRate": 0,
                        "lastExecutionAt": None
                    }
                
                suite_stats_map[execution.suite_id]["runsInDateRange"] += 1
                if execution.status == "success":
                    suite_stats_map[execution.suite_id]["passedInDateRange"] += 1
                
                # Update last execution time
                current_last = suite_stats_map[execution.suite_id]["lastExecutionAt"]
                if not current_last or execution.started_at.isoformat() > current_last:
                    suite_stats_map[execution.suite_id]["lastExecutionAt"] = execution.started_at.isoformat()

            # Add detailed test info if requested
            if detailed:
                test_details.append({
                    "testId": execution.test_id or execution.execution_id,
                    "testName": test_name_map.get(execution.test_id, execution.metadata.get("test_name", f"Test {execution.test_id[:8]}..." if execution.test_id else "Test Sin ID")),
                    "suiteName": suite_name_map.get(execution.suite_id, execution.metadata.get("suite_name", f"Suite {execution.suite_id[:8]}..." if execution.suite_id else "Sin Suite")),
                    "suiteId": execution.suite_id,
                    "projectId": execution.project_id,
                    "status": "Passed" if execution.status == "success" else "Failed",
                    "lastExecution": execution.started_at.isoformat(),
                    "duration": execution.duration_ms / 1000.0 if execution.duration_ms else None
                })

        # 7. Calculate lifetime statistics for each suite
        for suite_id in suite_stats_map.keys():
            # Get all-time statistics for this suite
            lifetime_executions = await Execution.find({"suite_id": suite_id}).to_list()
            
            total_lifetime = len(lifetime_executions)
            passed_lifetime = sum(1 for ex in lifetime_executions if ex.status == "success")
            failed_lifetime = total_lifetime - passed_lifetime
            
            suite_stats_map[suite_id].update({
                "lifetimeRuns": total_lifetime,
                "lifetimePassed": passed_lifetime,
                "lifetimeFailed": failed_lifetime,
                "lifetimeSuccessRate": passed_lifetime / total_lifetime if total_lifetime > 0 else 0
            })

        # 8. Get total test count from projects
        if project_id:
            try:
                project = await project_repo.get_by_project_id(project_id)
                if project:
                    total_tests_in_scope = sum(len(suite.test_cases) for suite in project.test_suites)
            except Exception:
                total_tests_in_scope = 0
        else:
            # If no project filter, estimate from unique test_ids in executions
            unique_test_ids = set(ex.test_id for ex in executions if ex.test_id)
            total_tests_in_scope = len(unique_test_ids)

        # 9. Build daily executions array
        daily_executions = []
        current = start_dt
        while current <= end_dt:
            key = current.strftime("%Y-%m-%d")
            counts = daily_map.get(key, {"passed": 0, "failed": 0})
            daily_executions.append({
                "date": current.strftime("%b %d"),
                "passed": counts["passed"],
                "failed": counts["failed"],
                "total": counts["passed"] + counts["failed"],
            })
            current += timedelta(days=1)

        # 10. Calculate average duration
        avg_duration = sum(durations) / len(durations) if durations else 0

        # 11. Build final response
        response = {
            "totalTests": total_tests_in_scope,
            "testsRun": tests_run_in_range,
            "passRate": (passed_tests_in_range / tests_run_in_range) if tests_run_in_range else 0,
            "avgDuration": avg_duration,
            "dailyExecutions": daily_executions,
            "suiteStats": list(suite_stats_map.values()),
            "dataSource": "ExecutionRepository",  # Indicator of data source
            "dateRange": {
                "start": start_dt.isoformat(),
                "end": end_dt.isoformat()
            }
        }

        if detailed:
            response["testDetails"] = test_details

        return clean_data_for_json_serialization(response)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating analytics: {str(e)}")


@router.get("/analytics-comparison", summary="Comparar datos entre sistema legacy y nuevo")
async def compare_analytics(
    start_date: Optional[str] = Query(None, description="Fecha inicio en formato YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="Fecha fin en formato YYYY-MM-DD"),
    project_id: Optional[str] = Query(None, description="Filtrar por ID de proyecto"),
    suite_id: Optional[str] = Query(None, description="Filtrar por ID de suite")
) -> Dict[str, Any]:
    """
    Endpoint para comparar los resultados entre el sistema legacy y el nuevo.
    Útil para debugging y validación de la migración.
    """
    try:
        # Import the original analytics function
        from src.api.analytics_routes import get_analytics
        from src.core.test_service import TestService
        import os
        
        # Get results from both systems
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY", ""))
        
        legacy_result = await get_analytics(
            start_date=start_date,
            end_date=end_date,
            project_id=project_id,
            suite_id=suite_id,
            test_service=test_service,
            detailed=False,
            include_history=False
        )
        
        new_result = await get_analytics_v2(
            start_date=start_date,
            end_date=end_date,
            project_id=project_id,
            suite_id=suite_id,
            detailed=False
        )
        
        # Calculate differences
        differences = {
            "totalTests": {
                "legacy": legacy_result.get("totalTests", 0),
                "new": new_result.get("totalTests", 0),
                "diff": new_result.get("totalTests", 0) - legacy_result.get("totalTests", 0)
            },
            "testsRun": {
                "legacy": legacy_result.get("testsRun", 0),
                "new": new_result.get("testsRun", 0),
                "diff": new_result.get("testsRun", 0) - legacy_result.get("testsRun", 0)
            },
            "passRate": {
                "legacy": legacy_result.get("passRate", 0),
                "new": new_result.get("passRate", 0),
                "diff": new_result.get("passRate", 0) - legacy_result.get("passRate", 0)
            }
        }
        
        return {
            "legacy_result": legacy_result,
            "new_result": new_result,
            "differences": differences,
            "summary": {
                "has_discrepancies": any(abs(d["diff"]) > 0.01 for d in differences.values()),
                "largest_discrepancy": max(abs(d["diff"]) for d in differences.values())
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error comparing analytics: {str(e)}")