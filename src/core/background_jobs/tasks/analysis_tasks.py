"""
Analysis Background Tasks

Celery tasks for AI-powered test analysis.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from celery import current_task

from src.core.background_jobs.celery_app import celery_app
from src.core.background_jobs.job_manager import get_job_manager
from src.core.background_jobs.models import JobStatus
from src.services.test_analysis_service import get_background_jobs_test_analysis_service
from src.database.repositories.execution_repository import ExecutionRepository
from src.core.result_transformer import StandardResult

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="analyze_test_background")
def analyze_test_background(self, job_id: str, result_data: Dict[str, Any], screenshot_data: Optional[list] = None):
    """
    Background task for AI-powered test analysis.
    
    Args:
        job_id: Job identifier
        result_data: Test result data to analyze
        screenshot_data: Optional screenshot data for analysis
        
    Returns:
        Dict containing analysis result
    """
    job_manager = get_job_manager()
    
    try:
        # Update job status to running
        job_manager.update_job_status(job_id, JobStatus.RUNNING, "Starting AI analysis...")
        job_manager.update_job_progress(job_id, 10, "Initializing analysis service...")
          # Import analysis service (done here to avoid circular imports)
        from src.services.test_analysis_service import get_background_jobs_test_analysis_service
        from src.models.standard_result import StandardResult
        
        analysis_service = get_background_jobs_test_analysis_service()
        
        # Convert result data to StandardResult object
        job_manager.update_job_progress(job_id, 20, "Preparing test data...")
        
        # Create StandardResult from data
        result = StandardResult.model_validate(result_data)
        
        job_manager.update_job_progress(job_id, 30, "Starting step analysis...")
        
        # Perform full AI analysis
        analysis_result = asyncio.run(analysis_service.analyze_full_test(result, screenshot_data))
        
        # Update progress throughout analysis
        job_manager.update_job_progress(job_id, 50, "Analyzing individual steps...")
        
        # Simulate progress updates (in real implementation, the analysis service would call back)
        job_manager.update_job_progress(job_id, 70, "Analyzing test completion...")
        job_manager.update_job_progress(job_id, 85, "Generating final verdict...")
        job_manager.update_job_progress(job_id, 95, "Finalizing analysis...")
        
        # Store the result in JobManager
        job_manager.store_job_result(job_id, analysis_result)

        # Debug: Log the analysis result structure
        logger.info(f"🔍 Analysis result structure: {list(analysis_result.keys()) if analysis_result else 'None'}")
        if analysis_result:
            logger.info(f"🔍 Has completion_analysis: {'completion_analysis' in analysis_result}")
            logger.info(f"🔍 Has step_analyses: {'step_analyses' in analysis_result}")
            logger.info(f"🔍 Has summary: {'summary' in analysis_result}")

        # Update the execution document with analysis results
        try:
            async def update_execution_with_analysis():
                from src.database.repositories.execution_repository import ExecutionRepository
                execution_repo = ExecutionRepository()
                
                # Get the execution_id from the job
                job = job_manager.get_job(job_id)
                if job and hasattr(job, 'execution_id'):
                    execution_id = job.execution_id
                      # Prepare update data - map analysis result fields to execution document fields
                    update_data = {
                        'ai_analysis': {
                            'step_analyses': analysis_result.get('step_analyses', []),
                            'completion_analysis': analysis_result.get('completion_analysis', {}),
                            'summary': analysis_result.get('summary', {}),
                            'analysis_timestamp': analysis_result.get('analysis_timestamp')
                        },
                        'ai_analysis_status': 'completed',
                        'metadata.ai_analysis': {
                            'step_analyses': analysis_result.get('step_analyses', []),
                            'completion_analysis': analysis_result.get('completion_analysis', {}),
                            'summary': analysis_result.get('summary', {}),
                            'analysis_timestamp': analysis_result.get('analysis_timestamp')
                        },
                        'metadata.ai_analysis_status': 'completed'
                    }
                      # Remove None values from nested structure
                    ai_analysis = update_data['ai_analysis']
                    ai_analysis = {k: v for k, v in ai_analysis.items() if v is not None}
                    
                    if ai_analysis:
                        update_data = {
                            'ai_analysis': ai_analysis,
                            'ai_analysis_status': 'completed',
                            'metadata.ai_analysis': ai_analysis,
                            'metadata.ai_analysis_status': 'completed'
                        }
                        logger.info(f"🔍 Updating execution {execution_id} with data keys: {list(ai_analysis.keys())}")
                        updated_execution = await execution_repo.update_by_execution_id(execution_id, update_data)
                        if updated_execution:
                            logger.info(f"✅ Updated execution {execution_id} with analysis results in MongoDB")
                        else:
                            logger.warning(f"⚠️ Execution {execution_id} not found in MongoDB")
                    else:
                        logger.warning(f"⚠️ No analysis data to update for execution {execution_id}")
                else:
                    logger.warning(f"Could not get execution_id from job {job_id}")
            
            # Run the async function
            asyncio.run(update_execution_with_analysis())
                
        except Exception as e:
            logger.error(f"Failed to update execution with analysis results: {e}", exc_info=True)
            # Don't fail the job if execution update fails
        
        logger.info(f"Successfully completed analysis for job {job_id}")
        return analysis_result
        
    except Exception as e:
        error_msg = f"Analysis failed: {str(e)}"
        logger.error(f"Job {job_id} failed: {error_msg}", exc_info=True)
        
        # Store error
        job_manager.store_job_error(job_id, error_msg)
        
        # Re-raise so Celery marks the task as failed
        raise


@celery_app.task(bind=True, name="analyze_tests_batch")
def analyze_tests_batch(self, batch_job_id: str, test_batches: list[Dict[str, Any]]):
    """
    Background task for batch AI-powered test analysis.
    Analyzes multiple tests in a single LLM call to optimize costs.
    
    Args:
        batch_job_id: Batch job identifier
        test_batches: List of test data batches, each containing:
            - job_id: Individual job identifier
            - result_data: Test result data to analyze
            - screenshot_data: Optional screenshot data for analysis
            
    Returns:
        Dict containing batch analysis results
    """
    job_manager = get_job_manager()
    
    try:
        # Update batch job status
        job_manager.update_job_status(batch_job_id, JobStatus.RUNNING, f"Starting batch AI analysis for {len(test_batches)} tests...")
        job_manager.update_job_progress(batch_job_id, 10, "Initializing batch analysis service...")
        
        # Import analysis service
        from src.services.test_analysis_service import get_background_jobs_test_analysis_service
        from src.models.standard_result import StandardResult
        
        analysis_service = get_background_jobs_test_analysis_service()
        
        # Prepare batch data
        job_manager.update_job_progress(batch_job_id, 20, "Preparing batch test data...")
        
        batch_results = {}
        prepared_tests = []
        
        # Validate and prepare each test in the batch
        for i, test_batch in enumerate(test_batches):
            try:
                job_id = test_batch.get("job_id")
                result_data = test_batch.get("result_data")
                screenshot_data = test_batch.get("screenshot_data")
                
                if not job_id or not result_data:
                    logger.warning(f"⚠️ Batch test {i+1}: Missing job_id or result_data, skipping")
                    continue
                
                # Convert to StandardResult
                result = StandardResult.model_validate(result_data)
                
                prepared_tests.append({
                    "job_id": job_id,
                    "result": result,
                    "screenshot_data": screenshot_data
                })
                
                # Update individual job status
                job_manager.update_job_status(job_id, JobStatus.RUNNING, "Queued for batch analysis...")
                
            except Exception as e:
                logger.error(f"❌ Failed to prepare test {i+1} in batch: {e}")
                job_id = test_batch.get("job_id")
                if job_id:
                    job_manager.store_job_error(job_id, f"Batch preparation failed: {str(e)}")
        
        if not prepared_tests:
            raise ValueError("No valid tests found in batch")
        
        logger.info(f"📦 Prepared {len(prepared_tests)} tests for batch analysis")
        
        # Perform batch AI analysis
        job_manager.update_job_progress(batch_job_id, 40, f"Analyzing {len(prepared_tests)} tests in batch...")
        
        # Call the new batch analysis method
        batch_analysis_results = asyncio.run(
            analysis_service.analyze_tests_batch(prepared_tests)
        )
        
        # Process individual results
        job_manager.update_job_progress(batch_job_id, 70, "Processing individual results...")
        
        for job_id, analysis_result in batch_analysis_results.items():
            try:
                # Store individual result
                job_manager.store_job_result(job_id, analysis_result)
                
                # Update execution document
                asyncio.run(_update_execution_with_analysis(job_id, analysis_result))
                
                batch_results[job_id] = {
                    "status": "completed",
                    "analysis": analysis_result
                }
                
                logger.info(f"✅ Completed analysis for job {job_id}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process result for job {job_id}: {e}")
                job_manager.store_job_error(job_id, f"Result processing failed: {str(e)}")
                batch_results[job_id] = {
                    "status": "failed",
                    "error": str(e)
                }
        
        # Store batch results
        job_manager.update_job_progress(batch_job_id, 95, "Finalizing batch analysis...")
        job_manager.store_job_result(batch_job_id, {
            "batch_summary": {
                "total_tests": len(test_batches),
                "processed_tests": len(prepared_tests),
                "successful_analyses": len([r for r in batch_results.values() if r["status"] == "completed"]),
                "failed_analyses": len([r for r in batch_results.values() if r["status"] == "failed"])
            },
            "individual_results": batch_results
        })
        
        logger.info(f"🎉 Successfully completed batch analysis for {len(prepared_tests)} tests")
        return batch_results
        
    except Exception as e:
        error_msg = f"Batch analysis failed: {str(e)}"
        logger.error(f"Batch job {batch_job_id} failed: {error_msg}", exc_info=True)
        
        # Store batch error
        job_manager.store_job_error(batch_job_id, error_msg)
        
        # Mark individual jobs as failed if they haven't been processed
        for test_batch in test_batches:
            job_id = test_batch.get("job_id")
            if job_id and job_id not in batch_results:
                job_manager.store_job_error(job_id, f"Batch analysis failed: {error_msg}")
        
        raise


async def _update_execution_with_analysis(job_id: str, analysis_result: Dict[str, Any]):
    """Helper function to update execution with analysis results."""
    try:
        execution_repo = ExecutionRepository()
        
        # Get the execution_id from the job
        job_manager = get_job_manager()
        job = job_manager.get_job(job_id)
        if job and hasattr(job, 'execution_id'):
            execution_id = job.execution_id
            
            # Prepare update data
            update_data = {
                'ai_analysis': {
                    'step_analyses': analysis_result.get('step_analyses', []),
                    'completion_analysis': analysis_result.get('completion_analysis', {}),
                    'summary': analysis_result.get('summary', {}),
                    'analysis_timestamp': analysis_result.get('analysis_timestamp'),
                    'batch_processed': True  # Mark as batch processed
                },
                'ai_analysis_status': 'completed',
                'metadata.ai_analysis': {
                    'step_analyses': analysis_result.get('step_analyses', []),
                    'completion_analysis': analysis_result.get('completion_analysis', {}),
                    'summary': analysis_result.get('summary', {}),
                    'analysis_timestamp': analysis_result.get('analysis_timestamp'),
                    'batch_processed': True  # Mark as batch processed
                },
                'metadata.ai_analysis_status': 'completed'
            }
            
            # Remove None values
            ai_analysis = update_data['ai_analysis']
            ai_analysis = {k: v for k, v in ai_analysis.items() if v is not None}
            
            if ai_analysis:
                update_data = {
                    'ai_analysis': ai_analysis,
                    'ai_analysis_status': 'completed',
                    'metadata.ai_analysis': ai_analysis,
                    'metadata.ai_analysis_status': 'completed'
                }
                updated_execution = await execution_repo.update_by_execution_id(execution_id, update_data)
                if updated_execution:
                    logger.info(f"✅ Updated execution {execution_id} with batch analysis results")
                else:
                    logger.warning(f"⚠️ Execution {execution_id} not found in MongoDB")
            else:
                logger.warning(f"⚠️ No analysis data to update for execution {execution_id}")
        else:
            logger.warning(f"Could not get execution_id from job {job_id}")
            
    except Exception as e:
        logger.error(f"Failed to update execution with analysis results for job {job_id}: {e}", exc_info=True)


@celery_app.task(name="cleanup_old_jobs")
def cleanup_old_jobs(max_age_hours: int = 24):
    """
    Background task to cleanup old jobs.
    
    Args:
        max_age_hours: Maximum age in hours before cleanup
    """
    try:
        job_manager = get_job_manager()
        job_manager.cleanup_old_jobs(max_age_hours)
        logger.info(f"Successfully cleaned up jobs older than {max_age_hours} hours")
    except Exception as e:
        logger.error(f"Failed to cleanup old jobs: {e}", exc_info=True)
        raise


# Progress callback function for analysis service
def analysis_progress_callback(job_id: str, progress: int, message: str):
    """
    Callback function for reporting analysis progress.
    
    Args:
        job_id: Job identifier
        progress: Progress percentage
        message: Progress message
    """
    job_manager = get_job_manager()
    job_manager.update_job_progress(job_id, progress, message)