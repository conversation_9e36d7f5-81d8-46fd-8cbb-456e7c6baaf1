"""
Celery Application Configuration for Background Jobs

Configures Celery for processing background tasks like AI analysis.
"""

import os
import platform
from celery import Celery
from kombu import Queue

# Set background jobs specific environment variables
os.environ.setdefault("OPENROUTER_MAX_MONTHLY_SPEND", "2.0")  # $2 cost limit for background jobs
os.environ.setdefault("AI_ANALYSIS_COST_OPTIMIZATION", "high")  # High cost optimization

# Detect Windows and set appropriate defaults
IS_WINDOWS = platform.system() == "Windows"

# Redis configuration - Check for explicit REDIS_URL first, then Upstash
REDIS_URL = os.getenv("REDIS_URL")

if not REDIS_URL:
    # Try to build from Upstash configuration
    UPSTASH_REDIS_REST_URL = os.getenv("UPSTASH_REDIS_REST_URL")
    UPSTASH_REDIS_REST_TOKEN = os.getenv("UPSTASH_REDIS_REST_TOKEN")
    
    if UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN:
        # Build Redis URL for Upstash
        import urllib.parse
        host = urllib.parse.urlparse(UPSTASH_REDIS_REST_URL).netloc
        
        # Try different formats that might work with Celery
        possible_urls = [
            f"redis://default:{UPSTASH_REDIS_REST_TOKEN}@{host}:6379",
            f"rediss://default:{UPSTASH_REDIS_REST_TOKEN}@{host}:6380",
            f"redis://:{UPSTASH_REDIS_REST_TOKEN}@{host}:6379"
        ]
        
        # For now, use the first one and let connection handling deal with SSL
        REDIS_URL = possible_urls[0]
        print(f"✅ Configured for Upstash Redis: {host}")
    else:
        # Fallback to local Redis
        REDIS_URL = "redis://localhost:6379/0"
        print(f"✅ Using local Redis: {REDIS_URL}")
else:
    print(f"✅ Using explicit REDIS_URL: {REDIS_URL}")

# Create Celery app
celery_app = Celery(
    "qak_background_jobs",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        "src.core.background_jobs.tasks.analysis_tasks",
    ]
)

# Configure Celery
celery_app.conf.update(
    # Broker transport options for connection tuning
    broker_transport_options={
        'visibility_timeout': 3600,  # Default visibility timeout
        'socket_connect_timeout': 10, # Timeout for initial connection
        'socket_timeout': 10,         # Timeout for socket operations
    },

    # Task routing
    task_routes={
        "analyze_test_background": {
            "queue": "analysis"
        },
    },
    
    # Task queues
    task_queues=(
        Queue("analysis", routing_key="analysis"),
        Queue("default", routing_key="default"),
    ),
    
    # Task settings
    task_default_queue="default",
    task_default_exchange="default",
    task_default_routing_key="default",
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        "master_name": "mymaster",
        "visibility_timeout": 3600,
    },    # Worker settings - Windows optimized
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Windows-specific settings
    worker_pool="solo" if IS_WINDOWS else "prefork",  # Use solo pool for Windows compatibility
    worker_concurrency=1 if IS_WINDOWS else 2,  # Single worker for Windows stability
    
    # Windows-specific broker settings
    broker_connection_timeout=30 if IS_WINDOWS else 4,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
    
    # Monitoring
    task_send_sent_event=True,
    task_track_started=True,
    worker_send_task_events=True,
    
    # Task result format
    result_serializer="json",
    accept_content=["json"],
    task_serializer="json",
    
    # Timezone
    timezone="UTC",
    enable_utc=True,
)