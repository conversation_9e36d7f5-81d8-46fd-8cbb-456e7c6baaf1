"""
Execution Strategies for the QAK Test Execution System

This module defines the strategy pattern for different test execution types.
Each strategy encapsulates the specific logic required to run a certain
kind of test (e.g., Smoke, Full, Suite).
"""

# Configure local browser-use before importing
import configure_browser_use_local

from abc import ABC, abstractmethod
from typing import Any, Dict
import logging
import asyncio

from src.core.execution_context import ExecutionContext
from src.models.standard_result import TestType, StandardResult
from browser_use import Agent as BrowserAgent

logger = logging.getLogger(__name__)


class ExecutionStrategy(ABC):
    """Abstract base class for all execution strategies."""

    @abstractmethod
    async def execute(self, context: ExecutionContext) -> Any:
        """
        Execute the test strategy.
        
        Args:
            context: The shared execution context.
            
        Returns:
            The raw result of the execution, to be processed by the ResultTransformer.
        """
        pass

    async def _collect_screenshots_from_history(self, history, execution_id: str, artifact_collector):
        """
        Extract screenshots from browser-use history and create artifacts.
        """
        try:
            import base64
            
            screenshot_count = 0
            if hasattr(history, 'history') and isinstance(history.history, list):
                for i, agent_history in enumerate(history.history):
                    # Look for screenshot data in state
                    if hasattr(agent_history, 'state') and agent_history.state:
                        state = agent_history.state
                        if hasattr(state, 'screenshot') and state.screenshot:
                            screenshot_data = state.screenshot
                            
                            try:
                                # Convert base64 to bytes
                                if isinstance(screenshot_data, str):
                                    # Remove data URL prefix if present
                                    if screenshot_data.startswith('data:image/'):
                                        screenshot_data = screenshot_data.split(',', 1)[1]
                                    
                                    screenshot_bytes = base64.b64decode(screenshot_data)
                                    
                                    # LOS ARTIFACTS YA FUERON CREADOS POR EL COLLECTOR
                                    # No crear duplicados aquí
                                    # artifact = await artifact_collector.collect_screenshot(
                                    #     execution_id=execution_id,
                                    #     screenshot_data=screenshot_bytes,
                                    #     step_name=f"step_{i+1}"
                                    # )
                                    
                                    # if artifact:
                                    #     screenshot_count += 1
                                    #     logger.debug(f"Created screenshot artifact for step {i+1}: {artifact.file_path}")
                                    
                                    # Los artifacts ya están en la base de datos
                                    screenshot_count += 1
                                    logger.debug(f"Screenshot data available for step {i+1}, artifacts already created by collector")
                                        
                            except Exception as e:
                                logger.warning(f"Failed to process screenshot for step {i+1}: {e}")
            
            logger.info(f"Processed {screenshot_count} screenshots from history (artifacts already created by collector)")
            
        except Exception as e:
            logger.warning(f"Error collecting screenshots from history: {e}")


class SmokeTestStrategy(ExecutionStrategy):
    """Strategy for executing simple, fast smoke tests."""

    async def execute(self, context: ExecutionContext) -> Any:
        url = context.metadata.get("url")
        instructions = context.metadata.get("instructions", "Perform basic functionality check")

        if not url:
            raise ValueError("URL is required for smoke tests")
        
        if not context.browser or not context.artifact_collector:
            raise ValueError("Browser and ArtifactCollector must be available in the context")

        logger.info(f"Executing smoke test for {url} with browser {context.browser.instance_id} using SmokeTestStrategy.")
        
        try:
            # The browser_object from the pool is the BrowserSession
            browser_session = context.browser.browser_object
            
            # Create LLM instance for the agent
            import os
            from src.utilities.browser_helper import create_llm_instance, BrowserHelperConfig, create_initial_actions_from_url
            
            # 🔧 USE RESOLVED CONFIG FROM DATABASE: Get the actual configuration from context
            # This configuration was already resolved from MongoDB and includes all DB settings
            resolved_config = context.config
            logger.info(f"🔧 SMOKE CONFIG: Using resolved config from context: headless={resolved_config.headless}, max_steps={resolved_config.max_steps}")
            
            # Create initial actions to start directly at the URL
            initial_actions = create_initial_actions_from_url(url)
            logger.info(f"Created initial actions to navigate to: {url}")
            
            agent_config = BrowserHelperConfig(
                model_provider=resolved_config.model_provider,
                model_name=getattr(resolved_config, 'model_name', None),  # Pass model_name from DB config
                use_vision=resolved_config.use_vision,
                max_steps=resolved_config.max_steps,  # Use DB config instead of hardcoded
                temperature=resolved_config.temperature,
                initial_actions=initial_actions
            )
            
            logger.info(f"🔧 SMOKE CONFIG: Created agent config with max_steps={agent_config.max_steps} from DB config")
            
            # Get appropriate API key based on provider from resolved config
            if resolved_config.model_provider == "openrouter":
                api_key = os.environ.get("OPENROUTER_API_KEY")
                if not api_key:
                    raise ValueError("OPENROUTER_API_KEY environment variable not found")
            elif resolved_config.model_provider == "gemini":
                api_key = os.environ.get("GOOGLE_API_KEY")
                if not api_key:
                    raise ValueError("GOOGLE_API_KEY environment variable not found")
            else:
                raise ValueError(f"Unsupported model provider: {resolved_config.model_provider}")
            
            llm_instance = create_llm_instance(agent_config, api_key)
            
            # Create a simplified Gherkin scenario for the agent - no need to mention URL navigation
            gherkin_scenario = f"""
Feature: Smoke Test
  Scenario: Execute smoke test based on instructions
    Given the user is already on the target page
    When the user follows these instructions: "{instructions}"
    Then the test should be completed successfully.
"""

            # Create the browser agent with initial actions
            import uuid
            unique_task_id = str(uuid.uuid4())[:8]  # Short unique ID for EventBus
            agent: BrowserAgent = BrowserAgent(
                task=gherkin_scenario,
                llm=llm_instance,
                browser_session=browser_session,
                use_vision=agent_config.use_vision,
                initial_actions=initial_actions,
                task_id=unique_task_id,
                execution_context=context,
                calculate_cost=False  # Disable cost tracking to avoid compatibility issues
            )

            # Run the agent
            history = await agent.run(max_steps=agent_config.max_steps)

            # Collect screenshots from history as artifacts
            await self._collect_screenshots_from_history(
                history=history,
                execution_id=context.execution_id,
                artifact_collector=context.artifact_collector
            )

            # For now, we return the raw history. 
            # The ResultTransformer will be responsible for converting this into a StandardResult.
            return history

        except Exception as e:
            logger.error(f"Error during smoke test execution: {e}", exc_info=True)
            await context.artifact_collector.collect_error_report(
                execution_id=context.execution_id,
                error_details={"type": e.__class__.__name__, "message": str(e)},
                stack_trace=str(e)
            )
            # Re-raise the exception to be handled by the orchestrator
            raise


class FullTestStrategy(ExecutionStrategy):
    """Strategy for executing comprehensive, Gherkin-based full tests."""

    async def execute(self, context: ExecutionContext) -> Any:
        url = context.metadata.get("url")
        gherkin_scenarios = context.metadata.get("gherkin_scenarios", [])

        if not url:
            raise ValueError("URL is required for full tests")
        if not gherkin_scenarios:
            raise ValueError("Gherkin scenarios are required for full tests")
            
        if not context.browser or not context.artifact_collector:
            raise ValueError("Browser and ArtifactCollector must be available in the context")

        logger.info(f"Executing full test for {url} with {len(gherkin_scenarios)} scenarios using FullTestStrategy.")

        try:
            # Create LLM instance and initial actions for the agent
            import os
            from src.utilities.browser_helper import create_llm_instance, BrowserHelperConfig, create_initial_actions_from_url
            
            # Create initial actions to start directly at the URL
            initial_actions = create_initial_actions_from_url(url)
            logger.info(f"Created initial actions to navigate to: {url}")
            
            # 🔧 USE RESOLVED CONFIG FROM DATABASE: Get the actual configuration from context
            # This configuration was already resolved from MongoDB and includes all DB settings
            resolved_config = context.config
            logger.info(f"🔧 FULL CONFIG: Using resolved config from context: headless={resolved_config.headless}, max_steps={resolved_config.max_steps}")
            
            agent_config = BrowserHelperConfig(
                model_provider=resolved_config.model_provider,
                model_name=getattr(resolved_config, 'model_name', None),  # Pass model_name from DB config
                use_vision=resolved_config.use_vision,
                max_steps=resolved_config.max_steps,  # Use DB config instead of hardcoded
                temperature=resolved_config.temperature,
                initial_actions=initial_actions
            )
            
            logger.info(f"🔧 FULL CONFIG: Created agent config with max_steps={agent_config.max_steps} from DB config")

            # Get appropriate API key based on provider from resolved config
            if resolved_config.model_provider == "openrouter":
                api_key = os.environ.get("OPENROUTER_API_KEY")
                if not api_key:
                    raise ValueError("OPENROUTER_API_KEY environment variable not found")
            elif resolved_config.model_provider == "gemini":
                api_key = os.environ.get("GOOGLE_API_KEY")
                if not api_key:
                    raise ValueError("GOOGLE_API_KEY environment variable not found")
            else:
                raise ValueError(f"Unsupported model provider: {resolved_config.model_provider}")
            
            llm_instance = create_llm_instance(agent_config, api_key)
            browser_session = context.browser.browser_object
            
            # Since we can have multiple scenarios, we'll execute the first one for now.
            # A more advanced implementation could run them sequentially in the same session.
            main_gherkin_scenario = gherkin_scenarios[0]

            # Create the browser agent with initial actions
            import uuid
            unique_task_id = str(uuid.uuid4())[:8]  # Short unique ID for EventBus
            agent: BrowserAgent = BrowserAgent(
                task=main_gherkin_scenario,
                llm=llm_instance,
                browser_session=browser_session,
                use_vision=agent_config.use_vision,
                initial_actions=initial_actions,
                task_id=unique_task_id,
                execution_context=context,
                calculate_cost=False  # Disable cost tracking to avoid compatibility issues
            )

            history = await agent.run(max_steps=agent_config.max_steps)

            # Collect screenshots from history as artifacts
            await self._collect_screenshots_from_history(
                history=history,
                execution_id=context.execution_id,
                artifact_collector=context.artifact_collector
            )
            
            return history

        except Exception as e:
            logger.error(f"Error during full test execution: {e}", exc_info=True)
            await context.artifact_collector.collect_error_report(
                execution_id=context.execution_id,
                error_details={"type": e.__class__.__name__, "message": str(e)},
                stack_trace=str(e)
            )
            raise


class TestCaseStrategy(ExecutionStrategy):
    """Strategy for executing a single test case, potentially multiple times."""

    async def execute(self, context: ExecutionContext) -> Any:
        test_case_id = context.metadata.get("test_case_id")
        # For now, assume Gherkin is passed directly.
        # A future implementation might load it from the project manager using the ID.
        gherkin_scenario = context.metadata.get("gherkin_scenario")
        test_instructions = context.metadata.get("test_instructions", "")
        url = context.metadata.get("url")

        # Allow test_instructions as fallback if gherkin_scenario is empty
        if not test_case_id or not url:
            raise ValueError("test_case_id and url are required for TestCaseStrategy")
        
        if not gherkin_scenario and not test_instructions:
            raise ValueError("Either gherkin_scenario or test_instructions must be provided for TestCaseStrategy")

        if not context.browser or not context.artifact_collector:
            raise ValueError("Browser and ArtifactCollector must be available in the context")

        logger.info(f"Executing test case {test_case_id} using TestCaseStrategy.")

        try:
            # Create LLM instance and initial actions for the agent
            import os
            from src.utilities.browser_helper import create_llm_instance, BrowserHelperConfig, create_initial_actions_from_url
            
            # Create initial actions to start directly at the URL
            initial_actions = create_initial_actions_from_url(url)
            logger.info(f"Created initial actions to navigate to: {url}")
            
            # 🔧 USE RESOLVED CONFIG FROM DATABASE: Get the actual configuration from context
            # This configuration was already resolved from MongoDB and includes all DB settings
            resolved_config = context.config
            logger.info(f"🔧 STRATEGY CONFIG: Using resolved config from context: headless={resolved_config.headless}, max_steps={resolved_config.max_steps}")
            
            # Create agent config using the resolved database configuration
            agent_config = BrowserHelperConfig(
                model_provider=resolved_config.model_provider,
                model_name=getattr(resolved_config, 'model_name', None),  # Pass model_name from DB config
                use_vision=resolved_config.use_vision,
                max_steps=resolved_config.max_steps,  # Use DB config instead of hardcoded
                temperature=resolved_config.temperature,
                initial_actions=initial_actions
            )
            
            logger.info(f"🔧 STRATEGY CONFIG: Created agent config with max_steps={agent_config.max_steps} from DB config")

            # Get appropriate API key based on provider from resolved config
            if resolved_config.model_provider == "openrouter":
                api_key = os.environ.get("OPENROUTER_API_KEY")
                if not api_key:
                    raise ValueError("OPENROUTER_API_KEY environment variable not found")
            elif resolved_config.model_provider == "gemini":
                api_key = os.environ.get("GOOGLE_API_KEY")
                if not api_key:
                    raise ValueError("GOOGLE_API_KEY environment variable not found")
            else:
                raise ValueError(f"Unsupported model provider: {resolved_config.model_provider}")
            
            llm_instance = create_llm_instance(agent_config, api_key)
            browser_session = context.browser.browser_object
            
            # Use gherkin_scenario if available, otherwise use test_instructions directly
            if gherkin_scenario and gherkin_scenario.strip():
                agent_task = gherkin_scenario
                logger.info(f"Using provided Gherkin scenario for test case {test_case_id}")
            else:
                # Use test_instructions directly as the task
                agent_task = test_instructions
                logger.info(f"Using test instructions directly as task for test case {test_case_id}")
            
            # Create the browser agent with initial actions
            import uuid
            unique_task_id = str(uuid.uuid4())[:8]  # Short unique ID for EventBus
            agent: BrowserAgent = BrowserAgent(
                task=agent_task,
                llm=llm_instance,
                browser_session=browser_session,
                use_vision=agent_config.use_vision,
                initial_actions=initial_actions,
                task_id=unique_task_id,
                execution_context=context,
                calculate_cost=False  # Disable cost tracking to avoid compatibility issues
            )
            
            history = await agent.run(max_steps=agent_config.max_steps)

            # Collect screenshots from history as artifacts
            await self._collect_screenshots_from_history(
                history=history,
                execution_id=context.execution_id,
                artifact_collector=context.artifact_collector
            )
            
            return history

        except Exception as e:
            logger.error(f"Error during test case execution {test_case_id}: {e}", exc_info=True)
            await context.artifact_collector.collect_error_report(
                execution_id=context.execution_id,
                error_details={"type": e.__class__.__name__, "message": str(e), "test_case_id": test_case_id},
                stack_trace=str(e)
            )
            raise


class SuiteStrategy(ExecutionStrategy):
    """Strategy for executing a test suite, which is a collection of test cases."""

    async def execute(self, context: ExecutionContext) -> Any:
        suite_id = context.metadata.get("suite_id")
        # For now, assume test cases are passed directly.
        # A future implementation might load them from the project manager using the ID.
        test_cases = context.metadata.get("test_cases", [])
        
        if not suite_id or not test_cases:
            raise ValueError("suite_id and a list of test_cases are required for SuiteStrategy")

        logger.info(f"Executing suite {suite_id} with {len(test_cases)} test cases using SuiteStrategy.")

        # This strategy orchestrates multiple test case executions.
        # It relies on the main orchestrator to run each individual test case.
        # This avoids duplicating logic and allows for better resource management.
        
        suite_results = []
        overall_success = True
        passed_count = 0
        failed_count = 0
        
        # We need access to the orchestrator to execute sub-tasks (the test cases)
        if not context.orchestrator:
            raise ValueError("Orchestrator is required in the context for SuiteStrategy")

        for test_case in test_cases:
            test_case_id = test_case.get("id")
            gherkin_scenario = test_case.get("gherkin_scenario")
            url = test_case.get("url")
            test_name = test_case.get("name", "")
            test_description = test_case.get("description", "")
            
            
            try:
                # Create a new, specific context for this single test case execution
                case_context = ExecutionContext(
                    execution_id=f"{context.execution_id}_{test_case_id}",
                    test_type=TestType.CASE,
                    config_profile=context.config_profile,
                    environment=context.environment,
                    config_overrides=context.config_overrides,
                    metadata={
                        "test_case_id": test_case_id,
                        "test_id": test_case_id,  # Add for compatibility
                        "gherkin_scenario": gherkin_scenario,
                        "url": url,
                        "test_name": test_name,
                        "test_description": test_description,
                        "test_instructions": test_case.get("instructions", ""),
                        "project_id": context.metadata.get("project_id"),
                        "suite_id": suite_id,
                        "execution_times": 1
                    }
                    # Note: We don't pass the browser here. 
                    # The orchestrator will acquire a fresh one from the pool.
                )
                
                # The result here will be a StandardResult object
                result: StandardResult = await context.orchestrator.execute(case_context)
                
                suite_results.append(result.to_dict())
                # Handle both enum and string status values
                result_status = result.status.value if hasattr(result.status, 'value') else str(result.status)
                if result_status == "success":
                    passed_count += 1
                else:
                    failed_count += 1
                    overall_success = False

            except Exception as e:
                logger.error(f"Error executing test case {test_case_id} in suite {suite_id}: {e}", exc_info=True)
                overall_success = False
                failed_count += 1
                # Create a minimal failure result for this test case
                suite_results.append({
                    "execution_id": f"{context.execution_id}_{test_case_id}",
                    "type": "case",
                    "status": "error",
                    "summary": {"error": str(e)},
                    "test_case_id": test_case_id
                })

        # The result of a suite is a collection of individual test case results.
        # The ResultTransformer will handle formatting this into a final StandardResult for the suite.
        return {
            "suite_id": suite_id,
            "total_tests": len(test_cases),
            "passed": passed_count,
            "failed": failed_count,
            "results": suite_results,
            "success": overall_success
        }


class CodegenStrategy(ExecutionStrategy):
    """Strategy for executing code generated by Playwright Codegen."""

    async def execute(self, context: ExecutionContext) -> Dict[str, Any]:
        session_id = context.metadata.get("session_id")

        if not session_id:
            raise ValueError("session_id is required for codegen execution")
            
        logger.info(f"Executing codegen session {session_id} using CodegenStrategy.")

        # Mock result for now
        return {
            "success": True,
            "session_id": session_id,
            "message": "Codegen execution simulation complete."
        }


class ExecutionStrategyFactory:
    """Factory to get the appropriate execution strategy."""

    def __init__(self):
        self._strategies = {
            TestType.SMOKE: SmokeTestStrategy(),
            TestType.FULL: FullTestStrategy(),
            TestType.CASE: TestCaseStrategy(),
            TestType.SUITE: SuiteStrategy(),
            TestType.CODEGEN: CodegenStrategy(),
        }

    def get_strategy(self, test_type: TestType) -> ExecutionStrategy:
        """
        Get the strategy for the given test type.
        
        Args:
            test_type: The type of the test.
            
        Returns:
            The execution strategy instance.
            
        Raises:
            ValueError: If the test type is not supported.
        """
        strategy = self._strategies.get(test_type)
        if not strategy:
            raise ValueError(f"Unsupported test type: {test_type}")
        return strategy