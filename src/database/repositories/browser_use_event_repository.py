"""Browser Use Event Repository for QAK MongoDB Implementation

Repositorio para operaciones CRUD de eventos de browser-use con
correlación de IDs y consultas optimizadas.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import DESCENDING, ASCENDING
from beanie import PydanticObjectId

from ..models.browser_use_event import (
    BrowserUseEventDocument,
    BrowserUseSessionDocument,
    BrowserUseTaskDocument,
    BrowserUseStepDocument,
    BrowserUseFileDocument,
    EventStats,
    CorrelationInfo
)
from .base import BaseRepository
from ..exceptions import DatabaseError, DocumentNotFoundError


class BrowserUseSessionRepository(BaseRepository[BrowserUseSessionDocument]):
    """Repositorio para sesiones de browser-use."""
    
    def __init__(self):
        super().__init__("browser_use_sessions")
    
    def to_document(self, model: BrowserUseSessionDocument) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserUseSessionDocument:
        """Convert MongoDB document to model."""
        return BrowserUseSessionDocument(**document)
    
    def get_document_id(self, model: BrowserUseSessionDocument) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_correlation_id(self, correlation_id: str) -> Optional[BrowserUseSessionDocument]:
        """Obtener sesión por ID de correlación."""
        try:
            collection = await self.collection
            document = await collection.find_one({"correlation_id": correlation_id})
            return self.from_document(document) if document else None
        except Exception as e:
            raise DatabaseError(f"Error obteniendo sesión por correlación: {str(e)}")
    
    async def find_sessions(
        self,
        user_id: Optional[str] = None,
        device_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[BrowserUseSessionDocument]:
        """Buscar sesiones con filtros."""
        try:
            collection = await self.collection
            query = {}
            
            if user_id:
                query["user_id"] = user_id
            if device_id:
                query["device_id"] = device_id
            if correlation_id:
                query["correlation_id"] = correlation_id
            
            cursor = collection.find(query).sort("created_at", DESCENDING).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            return [self.from_document(doc) for doc in documents]
        except Exception as e:
            raise DatabaseError(f"Error buscando sesiones: {str(e)}")


class BrowserUseTaskRepository(BaseRepository[BrowserUseTaskDocument]):
    """Repositorio para tareas de browser-use."""
    
    def __init__(self):
        super().__init__("browser_use_tasks")
    
    def to_document(self, model: BrowserUseTaskDocument) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserUseTaskDocument:
        """Convert MongoDB document to model."""
        return BrowserUseTaskDocument(**document)
    
    def get_document_id(self, model: BrowserUseTaskDocument) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_correlation_id(self, correlation_id: str) -> Optional[BrowserUseTaskDocument]:
        """Obtener tarea por ID de correlación."""
        try:
            collection = await self.collection
            document = await collection.find_one({"correlation_id": correlation_id})
            return self.from_document(document) if document else None
        except Exception as e:
            raise DatabaseError(f"Error obteniendo tarea por correlación: {str(e)}")
    
    async def update_by_id(self, document_id: str, update_data: Dict[str, Any]) -> bool:
        """Update a task document by ID with partial data."""
        try:
            from bson import ObjectId
            from datetime import datetime
            
            collection = await self.collection
            
            # Add updated_at timestamp
            update_data["updated_at"] = datetime.utcnow()
            
            result = await collection.update_one(
                {"_id": ObjectId(document_id)},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
        except Exception as e:
            raise DatabaseError(f"Error updating task by ID: {str(e)}")
    
    async def find_tasks(
        self,
        user_id: Optional[str] = None,
        device_id: Optional[str] = None,
        session_correlation_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        is_stopped: Optional[bool] = None,
        is_paused: Optional[bool] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[BrowserUseTaskDocument]:
        """Buscar tareas con filtros."""
        try:
            collection = await self.collection
            query = {}
            
            if user_id:
                query["user_id"] = user_id
            if device_id:
                query["device_id"] = device_id
            if session_correlation_id:
                query["session_correlation_id"] = session_correlation_id
            if correlation_id:
                query["correlation_id"] = correlation_id
            if is_stopped is not None:
                query["is_stopped"] = is_stopped
            if is_paused is not None:
                query["is_paused"] = is_paused
            
            cursor = collection.find(query).sort("created_at", DESCENDING).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            return [self.from_document(doc) for doc in documents]
        except Exception as e:
            raise DatabaseError(f"Error buscando tareas: {str(e)}")


class BrowserUseStepRepository(BaseRepository[BrowserUseStepDocument]):
    """Repositorio para pasos de browser-use."""
    
    def __init__(self):
        super().__init__("browser_use_steps")
    
    def to_document(self, model: BrowserUseStepDocument) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserUseStepDocument:
        """Convert MongoDB document to model."""
        return BrowserUseStepDocument(**document)
    
    def get_document_id(self, model: BrowserUseStepDocument) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_task_correlation_id(
        self, 
        task_correlation_id: str,
        limit: int = 50,
        skip: int = 0
    ) -> List[BrowserUseStepDocument]:
        """Obtener pasos por ID de correlación de tarea."""
        try:
            collection = await self.collection
            cursor = collection.find(
                {"task_correlation_id": task_correlation_id}
            ).sort("created_at", ASCENDING).skip(skip).limit(limit)
            
            documents = await cursor.to_list(length=limit)
            return [self.from_document(doc) for doc in documents]
        except Exception as e:
            raise DatabaseError(f"Error obteniendo pasos por tarea: {str(e)}")


class BrowserUseFileRepository(BaseRepository[BrowserUseFileDocument]):
    """Repositorio para archivos de browser-use."""
    
    def __init__(self):
        super().__init__("browser_use_files")
    
    def to_document(self, model: BrowserUseFileDocument) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserUseFileDocument:
        """Convert MongoDB document to model."""
        return BrowserUseFileDocument(**document)
    
    def get_document_id(self, model: BrowserUseFileDocument) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def get_by_task_correlation_id(
        self, 
        task_correlation_id: str
    ) -> List[BrowserUseFileDocument]:
        """Obtener archivos por ID de correlación de tarea."""
        try:
            collection = await self.collection
            cursor = collection.find(
                {"task_correlation_id": task_correlation_id}
            ).sort("created_at", ASCENDING)
            
            documents = await cursor.to_list(length=None)
            return [self.from_document(doc) for doc in documents]
        except Exception as e:
            raise DatabaseError(f"Error obteniendo archivos por tarea: {str(e)}")


class BrowserUseEventRepository(BaseRepository[BrowserUseEventDocument]):
    """Repositorio para eventos de browser-use."""
    
    def __init__(self):
        super().__init__("browser_use_events")
    
    def to_document(self, model: BrowserUseEventDocument) -> Dict[str, Any]:
        """Convert model to MongoDB document."""
        return model.model_dump(by_alias=True, exclude_unset=True)
    
    def from_document(self, document: Dict[str, Any]) -> BrowserUseEventDocument:
        """Convert MongoDB document to model."""
        return BrowserUseEventDocument(**document)
    
    def get_document_id(self, model: BrowserUseEventDocument) -> str:
        """Get the document ID from model."""
        return str(model.id) if model.id else ""
    
    async def create_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        user_id: Optional[str] = None,
        device_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        session_correlation_id: Optional[str] = None,
        task_correlation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        source_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> BrowserUseEventDocument:
        """Crear un nuevo evento con correlación de IDs."""
        
        event = BrowserUseEventDocument(
            event_type=event_type,
            data=data,
            user_id=user_id,
            device_id=device_id,
            correlation_id=correlation_id,
            session_correlation_id=session_correlation_id,
            task_correlation_id=task_correlation_id,
            metadata=metadata or {},
            source_ip=source_ip,
            user_agent=user_agent
        )
        
        return await self.create(event)
    
    async def get_events_by_type(
        self,
        event_type: str,
        limit: int = 100,
        skip: int = 0
    ) -> List[BrowserUseEventDocument]:
        """Obtener eventos por tipo."""
        
        return await BrowserUseEventDocument.find(
            BrowserUseEventDocument.event_type == event_type
        ).sort([("timestamp", DESCENDING)]).skip(skip).limit(limit).to_list()
    
    async def get_events_by_user(
        self,
        user_id: str,
        limit: int = 100,
        skip: int = 0
    ) -> List[BrowserUseEventDocument]:
        """Obtener eventos por usuario."""
        
        return await BrowserUseEventDocument.find(
            BrowserUseEventDocument.user_id == user_id
        ).sort([("timestamp", DESCENDING)]).skip(skip).limit(limit).to_list()
    
    async def get_events_by_correlation(
        self,
        correlation_id: str
    ) -> List[BrowserUseEventDocument]:
        """Obtener todos los eventos relacionados por correlation_id."""
        
        return await BrowserUseEventDocument.find(
            {"$or": [
                {"correlation_id": correlation_id},
                {"session_correlation_id": correlation_id},
                {"task_correlation_id": correlation_id}
            ]}
        ).sort([("timestamp", ASCENDING)]).to_list()
    
    async def get_recent_events(
        self,
        minutes: int = 5,
        limit: int = 100
    ) -> List[BrowserUseEventDocument]:
        """Obtener eventos recientes."""
        
        since = datetime.utcnow() - timedelta(minutes=minutes)
        
        return await BrowserUseEventDocument.find(
            BrowserUseEventDocument.timestamp >= since
        ).sort([("timestamp", DESCENDING)]).limit(limit).to_list()
    
    async def get_events_with_filters(
        self,
        event_type: Optional[str] = None,
        user_id: Optional[str] = None,
        device_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search_term: Optional[str] = None,
        limit: int = 100,
        skip: int = 0
    ) -> Tuple[List[BrowserUseEventDocument], int]:
        """Obtener eventos con filtros múltiples y conteo total."""
        
        # Construir query
        query = {}
        
        if event_type and event_type != 'all':
            query["event_type"] = event_type
        
        if user_id and user_id != 'all':
            query["user_id"] = user_id
        
        if device_id:
            query["device_id"] = device_id
        
        if start_date or end_date:
            timestamp_query = {}
            if start_date:
                timestamp_query["$gte"] = start_date
            if end_date:
                timestamp_query["$lte"] = end_date
            query["timestamp"] = timestamp_query
        
        if search_term:
            query["$or"] = [
                {"data": {"$regex": search_term, "$options": "i"}},
                {"event_type": {"$regex": search_term, "$options": "i"}},
                {"user_id": {"$regex": search_term, "$options": "i"}}
            ]
        
        # Obtener eventos
        events = await BrowserUseEventDocument.find(query).sort(
            [("timestamp", DESCENDING)]
        ).skip(skip).limit(limit).to_list()
        
        # Obtener conteo total
        total_count = await BrowserUseEventDocument.find(query).count()
        
        return events, total_count
    
    async def get_stats(self) -> EventStats:
        """Obtener estadísticas de eventos."""
        
        # Conteos básicos
        total_events = await BrowserUseEventDocument.count()
        total_sessions = await BrowserUseSessionDocument.count()
        total_tasks = await BrowserUseTaskDocument.count()
        total_steps = await BrowserUseStepDocument.count()
        total_files = await BrowserUseFileDocument.count()
        
        # Usuarios y dispositivos únicos
        unique_users_pipeline = [
            {"$match": {"user_id": {"$ne": None}}},
            {"$group": {"_id": "$user_id"}},
            {"$count": "unique_users"}
        ]
        unique_users_result = await BrowserUseEventDocument.aggregate(unique_users_pipeline).to_list()
        unique_users = unique_users_result[0]["unique_users"] if unique_users_result else 0
        
        unique_devices_pipeline = [
            {"$match": {"device_id": {"$ne": None}}},
            {"$group": {"_id": "$device_id"}},
            {"$count": "unique_devices"}
        ]
        unique_devices_result = await BrowserUseEventDocument.aggregate(unique_devices_pipeline).to_list()
        unique_devices = unique_devices_result[0]["unique_devices"] if unique_devices_result else 0
        
        # Eventos por tipo
        event_types_pipeline = [
            {"$group": {"_id": "$event_type", "count": {"$sum": 1}}}
        ]
        event_types_result = await BrowserUseEventDocument.aggregate(event_types_pipeline).to_list()
        event_types = {item["_id"]: item["count"] for item in event_types_result}
        
        # Eventos recientes (últimos 5 minutos)
        recent_since = datetime.utcnow() - timedelta(minutes=5)
        recent_events_count = await BrowserUseEventDocument.find(
            BrowserUseEventDocument.timestamp >= recent_since
        ).count()
        
        # Eventos por hora (últimas 24 horas)
        last_24h = datetime.utcnow() - timedelta(hours=24)
        events_last_24h = await BrowserUseEventDocument.find(
            BrowserUseEventDocument.timestamp >= last_24h
        ).count()
        events_per_hour = events_last_24h / 24.0
        
        return EventStats(
            total_events=total_events,
            total_sessions=total_sessions,
            total_tasks=total_tasks,
            total_steps=total_steps,
            total_files=total_files,
            event_types=event_types,
            unique_users=unique_users,
            unique_devices=unique_devices,
            recent_events_count=recent_events_count,
            events_per_hour=events_per_hour
        )
    
    async def get_correlation_info(self, correlation_id: str) -> CorrelationInfo:
        """Obtener información de correlación para un ID dado."""
        
        # Buscar todos los eventos relacionados
        related_events = await self.get_events_by_correlation(correlation_id)
        
        if not related_events:
            raise DocumentNotFoundError("browser_use_events", correlation_id)
        
        # Extraer IDs de correlación únicos
        session_correlation_ids = set()
        task_correlation_ids = set()
        event_ids = []
        event_chain = []
        
        for event in related_events:
            event_ids.append(event.event_id)
            
            if event.session_correlation_id:
                session_correlation_ids.add(event.session_correlation_id)
            
            if event.task_correlation_id:
                task_correlation_ids.add(event.task_correlation_id)
            
            event_chain.append({
                "event_id": event.event_id,
                "event_type": event.event_type,
                "timestamp": event.timestamp.isoformat(),
                "correlation_id": event.correlation_id
            })
        
        return CorrelationInfo(
            correlation_id=correlation_id,
            session_correlation_id=list(session_correlation_ids)[0] if session_correlation_ids else None,
            task_correlation_id=list(task_correlation_ids)[0] if task_correlation_ids else None,
            related_events=event_ids,
            event_chain=event_chain
        )
    
    async def delete_old_events(self, days: int = 30) -> int:
        """Eliminar eventos antiguos."""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        result = await BrowserUseEventDocument.find(
            BrowserUseEventDocument.timestamp < cutoff_date
        ).delete()
        
        return result.deleted_count
    
    async def clear_all_events(self) -> Dict[str, int]:
        """Limpiar todos los eventos (útil para desarrollo)."""
        
        events_deleted = await BrowserUseEventDocument.delete_all()
        sessions_deleted = await BrowserUseSessionDocument.delete_all()
        tasks_deleted = await BrowserUseTaskDocument.delete_all()
        steps_deleted = await BrowserUseStepDocument.delete_all()
        files_deleted = await BrowserUseFileDocument.delete_all()
        
        return {
            "events_deleted": events_deleted.deleted_count,
            "sessions_deleted": sessions_deleted.deleted_count,
            "tasks_deleted": tasks_deleted.deleted_count,
            "steps_deleted": steps_deleted.deleted_count,
            "files_deleted": files_deleted.deleted_count
        }


# Las clases de repositorio ya están definidas arriba con sus implementaciones completas