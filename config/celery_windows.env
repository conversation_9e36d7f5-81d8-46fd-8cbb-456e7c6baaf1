# Windows-specific Celery Configuration
# Add these variables to your .env file or set them in your environment

# Force solo pool for Windows compatibility
CELERY_WORKER_POOL=solo
CELERY_WORKER_CONCURRENCY=1
CELERY_WORKER_PREFETCH_MULTIPLIER=1

# Disable problematic features on Windows
CELERY_WORKER_DISABLE_RATE_LIMITS=True
CELERY_WORKER_SEND_TASK_EVENTS=False
CELERY_TASK_TRACK_STARTED=False

# Increase timeouts for Windows
CELERY_TASK_SOFT_TIME_LIMIT=300
CELERY_TASK_TIME_LIMIT=600
CELERY_BROKER_CONNECTION_TIMEOUT=30
CELERY_RESULT_BACKEND_TIMEOUT=30

# Windows-specific Redis settings
REDIS_SOCKET_CONNECT_TIMEOUT=10
REDIS_SOCKET_TIMEOUT=10
REDIS_SOCKET_KEEPALIVE=True
