<context>
# Overview
QAK (Agents QA) is an AI-powered test automation tool that transforms user stories into executable automation code. It solves the problem of slow, technical, and expensive test automation by allowing anyone, regardless of technical skill, to generate robust tests using natural language. It's for QA engineers, product managers, developers, and business analysts who want to accelerate development cycles and improve software quality. Its value lies in democratizing test automation, providing immediate feedback, and ensuring complete traceability from requirements to execution.

# Core Features
- **Full Test Process**: Converts user stories into executable test code. It's important for end-to-end automation. It works by enhancing the user story, generating manual test cases, creating Gherkin scenarios, and finally producing code for a selected framework.
- **Smoke Tests**: Allows for direct execution of tests from natural language instructions without intermediate steps. This is crucial for quick, exploratory testing. Users input a URL and instructions, and the tool executes the test in a real browser.
- **Multiple Frameworks Support**: Generates code for various automation frameworks like Selenium, Playwright, and Cypress. This provides flexibility and integration with existing team workflows. The system abstracts the test logic and then translates it to the specific syntax of the chosen framework.
- **Real Browser Execution**: Tests are run in a real browser environment powered by <PERSON><PERSON>. This ensures that tests are validated under realistic conditions, increasing confidence in the results. It uses browser-use for local synchronization of browser events.
- **Project Management**: Organizes tests into projects and suites. This is important for managing and scaling testing efforts. The system uses a JSON-based structure to persist project data.
- **Test History**: Provides detailed logs and history of all test executions, including screenshots. This is vital for debugging, auditing, and tracking quality over time.

# User Experience
- **User Personas**:
    - **QA Engineers**: Use QAK to accelerate the creation of automated test suites.
    - **Product Managers**: Use QAK to directly validate that user stories are implemented correctly.
    - **Developers**: Integrate QAK into their development workflow for quick feedback.
    - **Business Analysts**: Verify business requirements through automated tests.
- **Key User Flows**:
    - **Full Test Flow**: User inputs a story -> enhances it with AI -> generates manual cases -> generates Gherkin scenarios -> executes tests -> generates automation code.
    - **Smoke Test Flow**: User provides a URL and natural language instructions -> executes the test directly -> reviews results and screenshots.
- **UI/UX Considerations**: The interface is a modern Next.js web application designed to be intuitive and require no technical training. It provides immediate visual feedback with screenshots and metrics.
</context>
<PRD>
# Technical Architecture
- **System Components**:
    - **Backend**: FastAPI (Python) application that houses the AI agents and core logic.
    - **Frontend**: Next.js (TypeScript) web application for the user interface.
    - **AI Core**: Integrates with multiple AI providers (Gemini, OpenAI, etc.) to power the generation agents.
    - **Browser Automation**: Uses `browser-use` library with Playwright for real browser execution.
- **Data Models**: Project and test data are stored in JSON files for simplicity and portability.
- **APIs and Integrations**: A robust REST API with OpenAPI documentation. Integrates with LLM providers.
- **Infrastructure Requirements**: Python 3.11+, Node.js for the frontend. Can be run locally or containerized with Docker.

# Development Roadmap
- **Phase 1: Core System (Completed)**: MVP with backend, browser integration, and CLI.
- **Phase 2: User Interface (Completed)**: Full-featured Next.js web interface.
- **Phase 3: Production Ready (Current)**: Focus on stability, documentation, and performance optimization.
- **Phase 4: Enhancement & Scale (Future)**: Multi-user support, CI/CD integrations, advanced testing features, and potential database migration.

# Logical Dependency Chain
1.  **Foundation**: Build the core FastAPI backend with AI agent integrations.
2.  **Browser Execution**: Integrate `browser-use` to enable real-time test execution, providing a visible, working front-end component quickly.
3.  **CLI**: Develop a command-line interface for developer-centric workflows.
4.  **Web UI**: Create the Next.js frontend, starting with the Smoke Test feature for immediate usability.
5.  **Full Test Flow**: Implement the complete user story to code pipeline, building upon the foundational components.
6.  **Project Management**: Add features for organizing and saving tests.

# Risks and Mitigations
- **Technical Challenges**: Dependency on external AI APIs (rate limits, costs). **Mitigation**: Support for multiple providers and local models where possible. `browser-use` can be complex to configure. **Mitigation**: Clear documentation and containerized environments.
- **Figuring out the MVP**: Balancing features with speed of delivery. **Mitigation**: The phased approach, starting with a functional Smoke Test, proved effective in delivering value early.
- **Resource Constraints**: Cost of AI APIs. **Mitigation**: Efficient prompt engineering and allowing users to bring their own keys.

# Appendix
- **Research Findings**: The project brief and README confirm the need for democratized testing tools that bridge the gap between technical and non-technical users.
- **Technical Specifications**: Python 3.11, FastAPI, Next.js, Playwright, Docker.
</PRD>