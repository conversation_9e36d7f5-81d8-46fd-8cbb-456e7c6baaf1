#!/usr/bin/env python3
"""
Celery Worker Entry Point for QAK Background Jobs

Run this file to start a Celery worker for processing background analysis tasks.

Usage:
    python celery_worker.py

Environment Variables:
    REDIS_URL: Redis connection URL (default: redis://localhost:6379/0)
    CELERY_LOG_LEVEL: Log level for Celery worker (default: INFO)
    OPENROUTER_MAX_MONTHLY_SPEND: Maximum monthly spend for OpenRouter (default: 2.0 for background jobs)
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set background jobs specific environment variables
os.environ.setdefault("OPENROUTER_MAX_MONTHLY_SPEND", "2.0")  # $2 cost limit for background jobs
os.environ.setdefault("AI_ANALYSIS_COST_OPTIMIZATION", "high")  # High cost optimization

# Ensure the project root is in Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import Celery app
from src.core.background_jobs.celery_app import celery_app

if __name__ == "__main__":
    # Set default log level
    log_level = os.getenv("CELERY_LOG_LEVEL", "INFO")
      # Start worker with Windows-compatible configuration
    celery_app.worker_main([
        "worker",
        f"--loglevel={log_level}",
        "--queues=analysis,default",
        "--pool=solo",  # Use solo pool for Windows compatibility
        "--concurrency=1",  # Single worker for Windows stability
        "--hostname=qak-worker@%h",
        "--without-gossip",
        "--without-mingle",
        "--without-heartbeat",  # Disable heartbeat for Windows stability
    ])