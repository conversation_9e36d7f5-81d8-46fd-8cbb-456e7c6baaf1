# Diagrama de Arquitectura: Workers para Análisis y Browser Use

## Arquitectura Actual vs Propuesta

### Arquitectura Actual (Solo Worker de Análisis)

```mermaid
graph TB
    subgraph "Cliente"
        UI["🖥️ Frontend Web"]
        CLI["💻 CLI"]
    end
    
    subgraph "API Principal"
        API["🚀 FastAPI App<br/>(app.py)"]
        Routes["📡 API Routes"]
    end
    
    subgraph "Ejecución Directa"
        BrowserUse["🌐 Browser Use Agent<br/>(Ejecución Síncrona)"]
        BrowserPool["🏊 Browser Pool"]
        Playwright["🎭 Playwright Browsers"]
    end
    
    subgraph "Sistema de Background Jobs"
        Redis["📦 Redis<br/>(Broker & Backend)"]
        AnalysisWorker["⚙️ Celery Worker<br/>(Análisis IA)"]
        Flower["🌸 Flower<br/>(Monitoring)"]
    end
    
    subgraph "Tareas de Análisis"
        AnalysisTasks["🧠 Analysis Tasks<br/>- analyze_test_background<br/>- AI-powered analysis"]
    end
    
    UI --> API
    CLI --> API
    API --> Routes
    Routes --> BrowserUse
    Routes --> <PERSON>is
    BrowserUse --> BrowserPool
    BrowserPool --> Playwright
    Redis --> AnalysisWorker
    AnalysisWorker --> AnalysisTasks
    Redis --> Flower
    
    classDef current fill:#e1f5fe
    classDef worker fill:#f3e5f5
    classDef browser fill:#e8f5e8
    
    class API,Routes,BrowserUse,BrowserPool current
    class AnalysisWorker,AnalysisTasks worker
    class Playwright browser
```

### Arquitectura Propuesta (Workers para Análisis + Browser Use)

```mermaid
graph TB
    subgraph "Cliente"
        UI["🖥️ Frontend Web"]
        CLI["💻 CLI"]
    end
    
    subgraph "API Principal"
        API["🚀 FastAPI App<br/>(app.py)"]
        Routes["📡 API Routes"]
        JobManager["📋 Job Manager<br/>(Orchestrator)"]
    end
    
    subgraph "Sistema de Background Jobs"
        Redis["📦 Redis<br/>(Broker & Backend)"]
        
        subgraph "Workers Pool"
            AnalysisWorker["⚙️ Analysis Worker<br/>(Celery)"]
            BrowserWorker1["🌐 Browser Worker 1<br/>(Celery)"]
            BrowserWorker2["🌐 Browser Worker 2<br/>(Celery)"]
            BrowserWorkerN["🌐 Browser Worker N<br/>(Celery)"]
        end
        
        Flower["🌸 Flower<br/>(Monitoring)"]
    end
    
    subgraph "Colas de Tareas"
        AnalysisQueue["📥 analysis_queue<br/>- AI Analysis<br/>- Test Analysis"]
        BrowserQueue["📥 browser_execution_queue<br/>- Browser Automation<br/>- Test Execution<br/>- Scenario Running"]
        PriorityQueue["📥 priority_queue<br/>- Urgent Tasks<br/>- Real-time Requests"]
    end
    
    subgraph "Recursos Compartidos"
        BrowserPool["🏊 Shared Browser Pool"]
        FileSystem["📁 Shared File System"]
        Database["🗄️ MongoDB<br/>(Results & State)"]
    end
    
    subgraph "Ejecución de Tareas"
        AnalysisTasks["🧠 Analysis Tasks<br/>- analyze_test_background<br/>- AI-powered analysis"]
        BrowserTasks["🌐 Browser Tasks<br/>- execute_browser_scenario<br/>- run_test_suite<br/>- generate_screenshots<br/>- extract_data"]
    end
    
    UI --> API
    CLI --> API
    API --> Routes
    Routes --> JobManager
    
    JobManager --> AnalysisQueue
    JobManager --> BrowserQueue
    JobManager --> PriorityQueue
    
    Redis --> AnalysisQueue
    Redis --> BrowserQueue
    Redis --> PriorityQueue
    
    AnalysisQueue --> AnalysisWorker
    BrowserQueue --> BrowserWorker1
    BrowserQueue --> BrowserWorker2
    BrowserQueue --> BrowserWorkerN
    
    AnalysisWorker --> AnalysisTasks
    BrowserWorker1 --> BrowserTasks
    BrowserWorker2 --> BrowserTasks
    BrowserWorkerN --> BrowserTasks
    
    BrowserTasks --> BrowserPool
    BrowserTasks --> FileSystem
    BrowserTasks --> Database
    AnalysisTasks --> Database
    
    Redis --> Flower
    
    classDef api fill:#e1f5fe
    classDef worker fill:#f3e5f5
    classDef queue fill:#fff3e0
    classDef resource fill:#e8f5e8
    classDef task fill:#fce4ec
    classDef new fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class API,Routes,JobManager api
    class AnalysisWorker,BrowserWorker1,BrowserWorker2,BrowserWorkerN worker
    class AnalysisQueue,BrowserQueue,PriorityQueue queue
    class BrowserPool,FileSystem,Database resource
    class AnalysisTasks,BrowserTasks task
    class JobManager,BrowserWorker1,BrowserWorker2,BrowserWorkerN,BrowserQueue,BrowserTasks new
```

## Configuración Docker Compose Propuesta

```yaml
# docker-compose.workers.yml
version: '3.8'

services:
  # Redis para Celery broker y backend
  redis:
    image: redis:7-alpine
    container_name: qak-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # Worker para Análisis IA
  analysis-worker:
    build: .
    container_name: qak-analysis-worker
    command: celery -A src.core.background_jobs.celery_app worker -Q analysis_queue -n analysis_worker@%h
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_LOG_LEVEL=INFO
      - WORKER_TYPE=analysis
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LLM_MODEL=${LLM_MODEL:-gemini-2.5-flash-exp}
    volumes:
      - .:/app
      - /app/.venv
    restart: unless-stopped

  # Workers para Browser Use (Escalables)
  browser-worker-1:
    build: .
    container_name: qak-browser-worker-1
    command: celery -A src.core.background_jobs.celery_app worker -Q browser_execution_queue,priority_queue -n browser_worker_1@%h
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_LOG_LEVEL=INFO
      - WORKER_TYPE=browser
      - WORKER_ID=1
      - DISPLAY=:99
    volumes:
      - .:/app
      - /app/.venv
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  browser-worker-2:
    build: .
    container_name: qak-browser-worker-2
    command: celery -A src.core.background_jobs.celery_app worker -Q browser_execution_queue,priority_queue -n browser_worker_2@%h
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0
      - CELERY_LOG_LEVEL=INFO
      - WORKER_TYPE=browser
      - WORKER_ID=2
      - DISPLAY=:99
    volumes:
      - .:/app
      - /app/.venv
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Flower para monitoreo
  flower:
    build: .
    container_name: qak-flower
    command: celery -A src.core.background_jobs.celery_app flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - analysis-worker
      - browser-worker-1
      - browser-worker-2
    environment:
      - REDIS_URL=redis://redis:6379/0
    profiles:
      - monitoring

volumes:
  redis_data:
```

## Beneficios de la Arquitectura Propuesta

### 🚀 **Escalabilidad**
- **Workers Independientes**: Análisis y Browser Use en workers separados
- **Escalado Horizontal**: Fácil agregar más workers de browser según demanda
- **Balanceador de Carga**: Redis distribuye tareas automáticamente

### ⚡ **Performance**
- **Ejecución Paralela**: Múltiples tests ejecutándose simultáneamente
- **No Bloqueo**: API responde inmediatamente, ejecución en background
- **Optimización de Recursos**: Workers especializados por tipo de tarea

### 🔧 **Mantenibilidad**
- **Separación de Responsabilidades**: Análisis vs Ejecución
- **Monitoreo Granular**: Métricas por tipo de worker
- **Configuración Flexible**: Diferentes configuraciones por worker type

### 🛡️ **Confiabilidad**
- **Tolerancia a Fallos**: Si un worker falla, otros continúan
- **Reintentos Automáticos**: Celery maneja reintentos de tareas fallidas
- **Persistencia**: Redis mantiene estado de tareas

## Implementación Sugerida

### Fase 1: Crear Browser Workers
1. Crear `browser_tasks.py` con tareas de ejecución
2. Modificar `celery_app.py` para soportar múltiples colas
3. Actualizar `docker-compose.workers.yml`

### Fase 2: Job Manager
1. Implementar `JobManager` para orchestrar tareas
2. Crear API endpoints para jobs asíncronos
3. Implementar sistema de callbacks/webhooks

### Fase 3: Monitoreo y Métricas
1. Dashboard en tiempo real
2. Métricas de performance por worker
3. Alertas automáticas

### Fase 4: Optimizaciones
1. Auto-scaling basado en carga
2. Priorización inteligente de tareas
3. Optimización de recursos compartidos