#!/bin/bash

# =============================================================================
# QAK Docker Deployment Script
# =============================================================================
# Script para iniciar la infraestructura completa de QAK sin Next.js
# Incluye: Redis, API FastAPI y Celery Worker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[QAK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[QAK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[QAK]${NC} $1"
}

print_error() {
    echo -e "${RED}[QAK]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker no está ejecutándose. Por favor inicia Docker Desktop."
        exit 1
    fi
    print_success "Docker está ejecutándose correctamente."
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning "Archivo .env no encontrado."
        if [ -f ".env.docker" ]; then
            print_status "Copiando .env.docker a .env..."
            cp .env.docker .env
            print_warning "¡IMPORTANTE! Edita el archivo .env y configura tus API keys antes de continuar."
            print_warning "Especialmente: OPENROUTER_API_KEY, GOOGLE_API_KEY, MONGODB_URI, etc."
            read -p "Presiona Enter cuando hayas configurado el archivo .env..."
        else
            print_error "No se encontró .env.docker. Ejecuta este script desde el directorio raíz del proyecto."
            exit 1
        fi
    else
        print_success "Archivo .env encontrado."
    fi
}

# Function to create data directories
create_directories() {
    print_status "Creando directorios de datos..."
    mkdir -p data/{conversations,codegen_sessions,projects,semantic_memories,monitoring_data,logs,screenshots,exports}
    print_success "Directorios creados correctamente."
}

# Function to build and start services
start_services() {
    local mode=${1:-"production"}
    
    print_status "Construyendo imágenes Docker..."
    docker-compose -f docker-compose.production.yml build --no-cache
    
    print_status "Iniciando servicios QAK..."
    if [ "$mode" = "monitoring" ]; then
        print_status "Iniciando con monitoreo Flower habilitado..."
        docker-compose -f docker-compose.production.yml --profile monitoring up -d
    else
        docker-compose -f docker-compose.production.yml up -d
    fi
    
    print_success "Servicios iniciados correctamente."
}

# Function to show service status
show_status() {
    print_status "Estado de los servicios:"
    docker-compose -f docker-compose.production.yml ps
    
    echo ""
    print_status "Logs recientes:"
    docker-compose -f docker-compose.production.yml logs --tail=10
}

# Function to show service URLs
show_urls() {
    echo ""
    print_success "=== QAK SERVICIOS DISPONIBLES ==="
    echo -e "${GREEN}🚀 API Principal:${NC}      http://localhost:8000"
    echo -e "${GREEN}📚 Documentación API:${NC}  http://localhost:8000/docs"
    echo -e "${GREEN}🔍 Health Check:${NC}       http://localhost:8000/health"
    echo -e "${GREEN}🌐 Browser Use UI:${NC}     http://localhost:8000/browser-use-ui"
    echo -e "${GREEN}📊 Redis:${NC}              localhost:6379"
    
    if docker-compose -f docker-compose.production.yml ps | grep -q "qak-flower"; then
        echo -e "${GREEN}🌸 Flower Monitor:${NC}     http://localhost:5555"
    fi
    
    echo ""
    print_success "=== COMANDOS ÚTILES ==="
    echo "Ver logs:           docker-compose -f docker-compose.production.yml logs -f"
    echo "Parar servicios:    docker-compose -f docker-compose.production.yml down"
    echo "Reiniciar:          docker-compose -f docker-compose.production.yml restart"
    echo "Limpiar todo:       docker-compose -f docker-compose.production.yml down -v --rmi all"
}

# Function to wait for services to be healthy
wait_for_services() {
    print_status "Esperando que los servicios estén listos..."
    
    # Wait for Redis
    print_status "Verificando Redis..."
    timeout 60 bash -c 'until docker-compose -f docker-compose.production.yml exec redis redis-cli ping; do sleep 2; done'
    
    # Wait for API
    print_status "Verificando API..."
    timeout 120 bash -c 'until curl -f http://localhost:8000/health > /dev/null 2>&1; do sleep 5; done'
    
    print_success "Todos los servicios están listos."
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "=============================================================================="
    echo "                    QAK DOCKER DEPLOYMENT SCRIPT"
    echo "=============================================================================="
    echo -e "${NC}"
    
    # Parse command line arguments
    local command=${1:-"start"}
    local mode=${2:-"production"}
    
    case $command in
        "start")
            check_docker
            check_env_file
            create_directories
            start_services $mode
            wait_for_services
            show_status
            show_urls
            ;;
        "stop")
            print_status "Deteniendo servicios QAK..."
            docker-compose -f docker-compose.production.yml down
            print_success "Servicios detenidos."
            ;;
        "restart")
            print_status "Reiniciando servicios QAK..."
            docker-compose -f docker-compose.production.yml restart
            wait_for_services
            show_status
            ;;
        "status")
            show_status
            show_urls
            ;;
        "logs")
            docker-compose -f docker-compose.production.yml logs -f
            ;;
        "clean")
            print_warning "¡ADVERTENCIA! Esto eliminará todos los contenedores, volúmenes e imágenes de QAK."
            read -p "¿Estás seguro? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                print_status "Limpiando todo..."
                docker-compose -f docker-compose.production.yml down -v --rmi all
                print_success "Limpieza completada."
            else
                print_status "Operación cancelada."
            fi
            ;;
        "help")
            echo "Uso: $0 [comando] [modo]"
            echo ""
            echo "Comandos:"
            echo "  start [production|monitoring]  - Iniciar servicios (default: production)"
            echo "  stop                          - Detener servicios"
            echo "  restart                       - Reiniciar servicios"
            echo "  status                        - Mostrar estado de servicios"
            echo "  logs                          - Mostrar logs en tiempo real"
            echo "  clean                         - Limpiar todo (contenedores, volúmenes, imágenes)"
            echo "  help                          - Mostrar esta ayuda"
            echo ""
            echo "Modos:"
            echo "  production                    - Solo servicios esenciales (Redis, API, Worker)"
            echo "  monitoring                    - Incluye Flower para monitoreo de Celery"
            ;;
        *)
            print_error "Comando desconocido: $command"
            print_status "Usa '$0 help' para ver los comandos disponibles."
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"