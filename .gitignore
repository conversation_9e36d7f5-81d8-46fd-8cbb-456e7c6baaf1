# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
artifacts/
# IDE
.idea/
.vscode/
*.swp
*.swo


/tests/
# Project specific
agent_history.json
.qodo/

# Logs
*.log

# Local configuration
.env
.DS_Store

# Added by Task Master AI
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
