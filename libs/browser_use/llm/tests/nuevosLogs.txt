INFO:     127.0.0.1:59053 - "GET /api/v2/tests/execution/8d4c211f-3c9e-4f69-9d0c-5ffbf593b246 HTTP/1.1" 200 OK
19:22:48.957 GET /api/v2/tests/execution/8d4c211f-3c9e-4f69-9d0c-5ffbf593b246
19:22:48.958   FastAPI arguments
19:22:48.959     Found execution 8d4c211f-3c9e-4f69-9d0c-5ffbf593b246 in simple tracking with status ExecutionStatus.SUCCESS
19:22:48.959     🔍 Execution 8d4c211f-3c9e-4f69-9d0c-5ffbf593b246 is complete, checking MongoDB for latest data including AI analysis
19:22:49.938     ✅ Found updated execution data in MongoDB for 8d4c211f-3c9e-4f69-9d0c-5ffbf593b246
19:22:49.938     📋 MongoDB metadata present: True
19:22:49.938     🧠 AI Analysis in MongoDB metadata: True
19:22:49.938     📊 AI Analysis Status in MongoDB metadata: completed
INFO:     127.0.0.1:59053 - "GET /api/v2/tests/execution/8d4c211f-3c9e-4f69-9d0c-5ffbf593b246 HTTP/1.1" 200 OK
19:26:03.034 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
19:26:03.041 BrowserPool initialized (min: 2, max: 10)
19:26:03.045 PerformanceMonitor initialized (interval: 30s)
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
✅ Browser-use logging configured from /Users/<USER>/Proyectos/qak/libs
INFO     [browser_use.telemetry.service] Anonymized telemetry enabled. See https://docs.browser-use.com/development/telemetry for more information.
19:26:04.202 ✅ Using explicit REDIS_URL: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
19:26:04.393 Iniciando API web de QA Agent en http://0.0.0.0:8000...
19:26:04.394 Documentacion API disponible en:
19:26:04.394   - Swagger UI: http://localhost:8000/docs
19:26:04.394   - ReDoc: http://localhost:8000/redoc
19:26:04.394 
Presiona Ctrl+C para detener el servidor.
INFO:     Started server process [74486]
INFO:     Waiting for application startup.
19:26:04.683 🔧 Database configuration initialized for development environment
19:26:04.683 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
19:26:04.683 🔧 Database manager initialized
19:26:04.683 🔌 Connecting to MongoDB
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
19:26:06.541 ✅ Connected to MongoDB database: qak_dev
19:26:06.541 📊 MongoDB Server Version: 8.0.11
19:26:06.541 🚀 Database initialization successful
19:26:06.541 📊 Database indexes will be managed by Beanie ODM models
19:26:06.542 🔧 ODM manager initialized
19:26:06.542 📝 Registered ODM model: Project
19:26:06.542 📝 Registered ODM model: Execution
19:26:06.542 📝 Registered ODM model: CodegenSession
19:26:06.542 📝 Registered ODM model: Artifact
19:26:06.542 📝 Registered ODM model: BrowserConfiguration
19:26:06.542 📝 Registered ODM model: BrowserSessionPool
19:26:06.543 📝 Registered ODM model: BrowserUseEventDocument
19:26:06.543 📝 Registered ODM model: BrowserUseSessionDocument
19:26:06.543 📝 Registered ODM model: BrowserUseTaskDocument
19:26:06.543 📝 Registered ODM model: BrowserUseStepDocument
19:26:06.543 📝 Registered ODM model: BrowserUseFileDocument
19:26:06.707 🚀 Initializing Beanie ODM with 11 models...
19:26:06.707 🧹 Cleaning up potentially conflicting indexes...
19:26:07.055 🗑️ Dropped conflicting index: projects.project_id_1
19:26:07.222 🗑️ Dropped conflicting index: projects.created_at_-1
19:26:07.554 🗑️ Dropped conflicting index: executions.execution_id_1
19:26:07.900 🗑️ Dropped conflicting index: codegen_sessions.session_id_1
19:26:08.234 🗑️ Dropped conflicting index: artifacts.artifact_id_1
19:26:08.234 ✅ Index cleanup completed
19:26:14.230 ✅ Beanie ODM initialization successful
19:26:14.231   📝 Project -> projects
19:26:14.231   📝 Execution -> executions
19:26:14.231   📝 CodegenSession -> codegen_sessions
19:26:14.231   📝 Artifact -> artifacts
19:26:14.231   📝 BrowserConfiguration -> browser_configurations
19:26:14.231   📝 BrowserSessionPool -> browser_session_pools
19:26:14.231   📝 BrowserUseEventDocument -> browser_use_events
19:26:14.231   📝 BrowserUseSessionDocument -> browser_use_sessions
19:26:14.232   📝 BrowserUseTaskDocument -> browser_use_tasks
19:26:14.232   📝 BrowserUseStepDocument -> browser_use_steps
19:26:14.232   📝 BrowserUseFileDocument -> browser_use_files
19:26:14.564 Predefined configuration already exists: test_case
19:26:14.731 Predefined configuration already exists: smoke
19:26:14.893 Predefined configuration already exists: exploration
19:26:15.055 Predefined configuration already exists: exploration_deep
19:26:15.221 Predefined configuration already exists: test_suite
19:26:15.737 ✅ ProjectManagerService: MongoDB mode enabled
19:26:15.737 Service operation: list_projects
19:26:15.738 BrowserPool initialized (min: 2, max: 10)
19:26:15.738 PerformanceMonitor initialized (interval: 30s)
19:26:15.738 ExecutionOrchestrator initialized
19:26:15.739 🔄 Creating shared global EnhancedArtifactCollector instance
19:26:15.739 ✅ Initialized r2 storage backend for artifacts
19:26:15.739 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
19:26:15.739 EnhancedArtifactCollector initialized with CloudflareR2StorageBackend cloud storage backend (R2-ONLY MODE)
19:26:15.739 Loaded existing artifacts from storage
19:26:15.739 ArtifactCollector initialized successfully
19:26:15.739 🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud
19:26:15.739 ✅ Shared global EnhancedArtifactCollector instance initialized
19:26:15.740 Execution orchestrator initialized with ExecutionRepository.
INFO:     Application startup complete.
19:26:15.740 Loading active/stale sessions from database...
19:26:15.741 Cleaning up old, unfinished sessions from database...
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
19:26:16.784 Processed 0 stale sessions from database.
19:26:16.956 No old, unfinished sessions to clean up.
19:26:48.482 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:48.491 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:48.492 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:48.495 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:48.497 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
INFO:     127.0.0.1:60902 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60904 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60905 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
INFO:     127.0.0.1:60907 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60908 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:26:48.512 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:48.514 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:48.528   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:48.529   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
               FastAPI arguments
19:26:48.530     ✅ ProjectManagerService: MongoDB mode enabled
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
19:26:48.530     ✅ ProjectManagerService: MongoDB mode enabled
19:26:49.029     Service operation: list_projects
19:26:49.032     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:49.032     Service operation: list_projects
19:26:49.706     ✅ Project found: Web Agent Page
19:26:49.706     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:49.706     📝 Has environments attr: True
19:26:49.707     📝 Environments count: 2
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:26:49.709 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:49.710   FastAPI arguments
19:26:49.711     ✅ ProjectManagerService: MongoDB mode enabled
19:26:49.711     Service operation: list_projects
19:26:50.754     ✅ Project found: Web Agent Page
19:26:50.754     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:50.754     📝 Has environments attr: True
19:26:50.754     📝 Environments count: 2
INFO:     127.0.0.1:60905 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:26:50.756 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:50.757   FastAPI arguments
19:26:50.757     ✅ ProjectManagerService: MongoDB mode enabled
19:26:50.757     Service operation: list_projects
19:26:50.765     ✅ Project found: Web Agent Page
19:26:50.765     ✅ Test suite found: Funcionalidad core
19:26:50.765     ✅ Test case found: Login
19:26:50.765     ✅ Test case found: Login
INFO:     127.0.0.1:60904 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
19:26:50.766 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:50.767   FastAPI arguments
19:26:50.767     ✅ ProjectManagerService: MongoDB mode enabled
19:26:50.768     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:50.768     Service operation: list_projects
19:26:50.768 OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
INFO:     127.0.0.1:60904 - "OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
19:26:50.770 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
19:26:50.771   FastAPI arguments
19:26:50.772     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:50.772     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
19:26:50.772     ✅ Orchestrator and execution repository initialized
19:26:51.100     ✅ Project found: Web Agent Page
19:26:51.101     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:51.101     📝 Has environments attr: True
19:26:51.101     📝 Environments count: 2
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:26:52.488     ✅ Project found: Web Agent Page
19:26:52.488     ✅ Test suite found: Funcionalidad core
19:26:52.488     ✅ Test case found: Login
19:26:52.488     ✅ Test case found: Login
INFO:     127.0.0.1:60905 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
19:26:52.491 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:52.491   FastAPI arguments
19:26:52.492     ✅ ProjectManagerService: MongoDB mode enabled
19:26:52.492     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:52.493     Service operation: list_projects
19:26:52.830     ✅ Project found: Web Agent Page
19:26:52.830     ✅ Test suite found: Funcionalidad core
19:26:52.830     ✅ Test case found: Login
19:26:52.830     ✅ Test case found: Login
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
19:26:54.084     📊 Found 40 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:54.085     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:60904 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
19:26:54.089 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
19:26:54.089   FastAPI arguments
19:26:54.090     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:54.090     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
19:26:54.091     ✅ Orchestrator and execution repository initialized
19:26:54.166 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:54.167   FastAPI arguments
19:26:54.167     ✅ ProjectManagerService: MongoDB mode enabled
19:26:54.168     Service operation: list_projects
19:26:54.850     ✅ Project found: Web Agent Page
19:26:54.850     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:54.850     📝 Has environments attr: True
19:26:54.850     📝 Environments count: 2
INFO:     127.0.0.1:60904 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
19:26:57.314     📊 Found 40 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:57.315     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:60902 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
19:26:58.785 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:58.785   FastAPI arguments
19:26:58.786 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:58.786   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
19:26:58.787     ✅ ProjectManagerService: MongoDB mode enabled
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
               FastAPI arguments
19:26:58.787     ✅ ProjectManagerService: MongoDB mode enabled
19:26:58.788     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:58.788     Service operation: list_projects
19:26:58.789     Service operation: list_projects
19:26:59.126     ✅ Project found: Web Agent Page
19:26:59.127     ✅ Test suite found: Funcionalidad core
19:26:59.127     ✅ Test case found: Login
19:26:59.127     ✅ Test case found: Login
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
19:26:59.129 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:59.130   FastAPI arguments
19:26:59.130     ✅ ProjectManagerService: MongoDB mode enabled
19:26:59.131     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:59.131     Service operation: list_projects
19:26:59.457     ✅ Project found: Web Agent Page
19:26:59.458     ✅ Test suite found: Funcionalidad core
19:26:59.458     ✅ Test case found: Login
19:26:59.458     ✅ Test case found: Login
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
19:26:59.462 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
19:26:59.463   FastAPI arguments
19:26:59.464     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:26:59.464     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
19:26:59.464     ✅ Orchestrator and execution repository initialized
19:26:59.615     ✅ Project found: Web Agent Page
19:26:59.615     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:59.616     📝 Has environments attr: True
19:26:59.616     📝 Environments count: 2
INFO:     127.0.0.1:60904 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:26:59.627 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:26:59.627   FastAPI arguments
19:26:59.628     ✅ ProjectManagerService: MongoDB mode enabled
19:26:59.628     Service operation: list_projects
19:26:59.963     ✅ Project found: Web Agent Page
19:26:59.963     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:26:59.964     📝 Has environments attr: True
19:26:59.964     📝 Environments count: 2
INFO:     127.0.0.1:60904 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
19:27:03.071     📊 Found 40 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:27:03.071     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:60902 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
19:27:03.155 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
19:27:03.155   FastAPI arguments
19:27:03.156     ✅ ProjectManagerService: MongoDB mode enabled
19:27:03.156     Service operation: list_projects
19:27:03.499     ✅ Project found: Web Agent Page
19:27:03.499     📝 Project type: <class 'src.utilities.project_manager.Project'>
19:27:03.499     📝 Has environments attr: True
19:27:03.499     📝 Environments count: 2
INFO:     127.0.0.1:60902 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
19:27:34.210 POST /api/v2/tests/execute
19:27:34.211   FastAPI arguments
19:27:34.213     🔧 BROWSER CONFIG: Resolving configuration for execution type case
19:27:34.213     🔧 BROWSER CONFIG: MongoDB query filters: {'execution_types': {'$in': ['case']}, 'is_active': True}
19:27:35.284     Found 1 configurations for execution type: case
19:27:35.284       Config 1: Test Case (Default) - execution_types: ['case', 'full'] - model_name: None
19:27:35.284     Ordered configurations by priority - OpenRouter configs first
19:27:35.285       1. Test Case (Default) (provider: openrouter, usage: 0)
19:27:35.285     🔧 BROWSER CONFIG: Found DB config 'Test Case (Default)' for type case
19:27:35.285     🔧 BROWSER CONFIG: MongoDB settings: model_provider=openrouter, model_name=None, headless=True
19:27:35.285     🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: openai/gpt-4.1-mini
19:27:35.285     🔧 BROWSER CONFIG: DB config failed (BrowserConfig.__init__() got an unexpected keyword argument 'capture_beyond_viewport'), using hardcoded fallback
19:27:35.285     🔧 BROWSER CONFIG: Applied hardcoded fallback for type case with headless=True
19:27:35.286     ✅ ProjectManagerService: MongoDB mode enabled
19:27:35.286     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
19:27:35.286     Service operation: list_projects
19:27:35.946     ✅ Project found: Web Agent Page
19:27:35.946     ✅ Test suite found: Funcionalidad core
19:27:35.946     ✅ Test case found: Login
19:27:36.278     🔍 EXECUTION DEBUG: Environment name resolved: QA
19:27:36.279     Created execution context e595c7e5-0c4e-4697-8b96-f779e1c880b5 for TestType.CASE
19:27:36.279     🔍 CONTEXT DEBUG: No application_version provided or empty
19:27:36.279     Registering execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in orchestrator.active_executions
19:27:36.279     Orchestrator instance ID: 5132135504
19:27:36.279     Active executions before: []
19:27:36.279     Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 status: ExecutionStatus.RUNNING
19:27:36.280     Active executions after: ['e595c7e5-0c4e-4697-8b96-f779e1c880b5']
19:27:36.280     Successfully registered e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:36.280     Simple tracking has: ['e595c7e5-0c4e-4697-8b96-f779e1c880b5']
19:27:36.280     Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 started and returned immediately
INFO:     127.0.0.1:60970 - "POST /api/v2/tests/execute HTTP/1.1" 200 OK
19:27:36.281 Background execution started for e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:36.281 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 status: ExecutionStatus.RUNNING
19:27:36.608 Using specified environment: QA
19:27:36.609 🔍 ORCHESTRATOR DEBUG: Resolved environment: Environment(name='QA', base_url='https://web-agent-playground.lovable.app', is_default=True)
19:27:36.609 🔍 ORCHESTRATOR DEBUG: Environment details - ID: 079f669c-ea2c-43f4-bf76-0a98897b1353, Name: QA
19:27:36.609 Using absolute URL: https://web-agent-playground.lovable.app
19:27:36.609 🔍 ORCHESTRATOR DEBUG: Constructed URL: https://web-agent-playground.lovable.app
19:27:36.609 Environment info set for execution e595c7e5-0c4e-4697-8b96-f779e1c880b5: QA -> https://web-agent-playground.lovable.app
19:27:36.609 🔍 ORCHESTRATOR DEBUG: Environment info set in context
19:27:36.609 BrowserPool initialized successfully
19:27:36.610 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
19:27:36.610   - headless: True
19:27:36.610   - model_provider: openrouter
19:27:36.610   - model_name: openai/gpt-4.1-mini
19:27:36.610   - highlight_elements: False
19:27:36.610   - temperature: 0.1
19:27:36.610 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
19:27:36.610 🔍 BROWSER POOL: Added disable_security=False from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added enable_memory=False from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added generate_gif=True from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added headless=True from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added max_failures=3 from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
19:27:36.611 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added model_name=openai/gpt-4.1-mini from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added model_provider=openrouter from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added overrides={} from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added retry_delay=5 from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added stealth=False from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
19:27:36.612 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
19:27:36.613 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
19:27:36.613 🔍 BROWSER POOL: Ensured critical field headless=True
19:27:36.613 🔍 BROWSER POOL: Ensured critical field disable_security=False
19:27:36.613 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
19:27:36.613 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'model_name', 'model_provider', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
19:27:36.613 🔍 BROWSER POOL: headless value being passed: True
19:27:36.613 🔍 BROWSER POOL: model_provider=openrouter, model_name=openai/gpt-4.1-mini
19:27:36.614 🔍 BROWSER POOL: BrowserProfile created - headless=True
19:27:36.614 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=openrouter
19:27:36.666 Created browser 4e5fee8c-38ae-4d6a-b2a3-40662e0a77d3 with config hash 20b966c0
19:27:36.666 Created new browser 4e5fee8c-38ae-4d6a-b2a3-40662e0a77d3 for e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:36.666 Executing with strategy: TestCaseStrategy
19:27:36.666 Executing test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 using TestCaseStrategy.
19:27:36.667 Created initial actions to navigate to: https://web-agent-playground.lovable.app
19:27:36.667 🔧 STRATEGY CONFIG: Using resolved config from context: headless=True, max_steps=50
19:27:36.667 🔧 STRATEGY CONFIG: Created agent config with max_steps=50 from DB config
19:27:36.667 Creating OpenRouter LLM with model: openai/gpt-4.1-mini
19:27:36.667 Using provided Gherkin scenario for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO     [browser_use.agent.service] 💾 File system path: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browser_use_agent_47266ae6
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6ae6 🅟 24] 🧠 Starting a browser-use agent 0.5.4 with base_model=openai/gpt-4.1-mini +vision extraction_model=openai/gpt-4.1-mini +file_system
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 24] 🚀 Starting task: Caracterstica: Autenticacin de Usuario

Escenario: Inicio de sesin exitoso con credenciales vlidas
  Dado que el usuario est en la pgina de inicio de sesin
  Cuando el usuario ingresa "<EMAIL>" como nombre de usuario y "admin123" como contrasea
  Y el usuario intenta iniciar sesin
  Entonces el usuario debera iniciar sesin exitosamente
19:27:37.059 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61146 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
19:27:37.070 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61148 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 6c71:None #84] 🎭 Launching new local browser playwright:chromium keep_alive=False user_data_dir= ~/.config/browseruse/profiles/default
WARNING  [browser_use.BrowserSession🆂 6c71:None #84] ⚠️ SingletonLock conflict detected. Profile at ~/.config/browseruse/profiles/default is locked. Using temporary profile instead: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-8zr39and
INFO     [browser_use.BrowserSession🆂 6c71:None #84]  ↳ Spawning Chrome subprocess listening on CDP http://127.0.0.1:61153/ with user_data_dir= /private/var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-8zr39and
19:27:39.363 OPTIONS /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
INFO:     127.0.0.1:60970 - "OPTIONS /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:27:39.365 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:39.366   FastAPI arguments
19:27:39.367     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🌎 Connecting to newly spawned browser via CDP http://127.0.0.1:61153/ -> browser_pid=74648 (local)
INFO     [browser_use.controller.service] 🔗  Opened new tab #1 with url https://web-agent-playground.lovable.app
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ☑️ Executed action 1/1: go_to_url()
19:27:42.363 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:42.363   FastAPI arguments
19:27:42.364     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Current 1512x982, Document 1512x982, Target 1920x2000
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Expanded viewport to 1920x2000 (was 1512x982)
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 2: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
19:27:45.365 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:45.366   FastAPI arguments
19:27:45.367     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:27:48.363 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:48.363   FastAPI arguments
19:27:48.364     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:27:51.365 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:51.366   FastAPI arguments
19:27:51.366     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 💡 Thinking:
The user request is to test the user authentication feature with a successful login scenario using valid credentials. The current page is the login page with input fields for email and password and a button to submit the login form. The placeholders match the credentials provided in the scenario. The next step is to input the email '<EMAIL>' into the email field and the password 'admin123' into the password field, then click the 'Iniciar Sesión' button to attempt login. After that, I will need to verify if the login was successful.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 👍 Eval: The login page with input fields and login button is loaded successfully, ready for input. Verdict: Success
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🧠 Memory: On the login page with email and password fields and the login button visible. Credentials to use: <EMAIL> / admin123.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🎯 Next goal: Input the email '<EMAIL>' into the email field and the password 'admin123' into the password field.

19:27:52.799 🧠 openai/gpt-4.1-mini | 🆕 2.7k + 💾 4.6k | 📤 240
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 2: Ran 1 actions in 10.84s: ✅ 1
19:27:53.159 POST /api/browser-use/v1/api/v1/events
19:27:53.160 POST /api/browser-use/v1/api/v1/oauth/device/authorize
INFO:     127.0.0.1:61248 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:61249 - "POST /api/browser-use/v1/api/v1/oauth/device/authorize HTTP/1.1" 404 Not Found
WARNING  [browser_use.sync.auth] Cloud sync authentication endpoint not found (404). Check your BROWSER_USE_CLOUD_API_URL setting.
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Current 756x469, Document 756x562, Target 1920x2000
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Expanded viewport to 1920x2000 (was 756x469)
19:27:54.364 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:54.364   FastAPI arguments
19:27:54.365     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 3: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
19:27:57.363 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:27:57.363   FastAPI arguments
19:27:57.364     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 💡 Thinking:
The email field has been successfully filled with '<EMAIL>'. The next step is to input the password 'admin123' into the password field (index 4) and then click the 'Iniciar Sesión' button (index 6) to attempt login, as per the user request scenario for successful login.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 👍 Eval: Successfully input the email '<EMAIL>' into the email field. Verdict: Success
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🧠 Memory: Email field filled with '<EMAIL>'. Ready to input password and submit login form.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🎯 Next goal: Input the password 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt login.

19:27:59.635 🧠 openai/gpt-4.1-mini | 🆕 1.1k + 💾 4.6k | 📤 154
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 3: Ran 1 actions in 6.79s: ✅ 1
19:27:59.940 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61284 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
19:28:00.363 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:00.364   FastAPI arguments
19:28:00.364     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Current 756x469, Document 756x562, Target 1920x2000
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Expanded viewport to 1920x2000 (was 756x469)
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 4: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
19:28:04.132 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:04.133   FastAPI arguments
19:28:04.133     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 💡 Thinking:
The email and password fields have been successfully filled with the valid credentials '<EMAIL>' and 'admin123'. The next step is to click the 'Iniciar Sesión' button to attempt login and verify if the user can log in successfully as per the scenario described.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 👍 Eval: Successfully input the password 'admin123' into the password field. Verdict: Success
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🧠 Memory: Email and password fields are filled with valid credentials for admin user. Ready to submit the login form by clicking the 'Iniciar Sesión' button.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🎯 Next goal: Click the 'Iniciar Sesión' button to attempt login and verify successful user authentication.

19:28:04.903 🧠 openai/gpt-4.1-mini | 🆕 1.0k + 💾 4.7k | 📤 162
INFO     [browser_use.controller.service] 🖱️ Clicked button with index 6: Iniciar Sesión
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ☑️ Executed action 1/1: click_element_by_index()
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 4: Ran 1 actions in 5.14s: ✅ 1
19:28:05.077 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61314 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.00s
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Current 756x469, Document 756x1653, Target 1920x2000
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🔥 VIEWPORT DEBUG: Expanded viewport to 1920x2000 (was 756x469)
19:28:06.615 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
19:28:06.615   - headless: True
19:28:06.615   - model_provider: None
19:28:06.615   - model_name: None
19:28:06.615   - highlight_elements: False
19:28:06.615   - temperature: 0.1
19:28:06.615 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added headless=True from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
19:28:06.616 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added overrides={} from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added stealth=False from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
19:28:06.617 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
19:28:06.618 🔍 BROWSER POOL: Ensured critical field headless=True
19:28:06.618 🔍 BROWSER POOL: Ensured critical field disable_security=True
19:28:06.618 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
19:28:06.618 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
19:28:06.619 🔍 BROWSER POOL: headless value being passed: True
19:28:06.619 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
19:28:06.619 🔍 BROWSER POOL: BrowserProfile created - headless=True
19:28:06.620 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
19:28:06.670 Created browser 03f05f73-df3e-4b82-a258-0633da4e1436 with config hash 20b966c0
19:28:06.671 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
19:28:06.671   - headless: True
19:28:06.671   - model_provider: None
19:28:06.671   - model_name: None
19:28:06.671   - highlight_elements: False
19:28:06.671   - temperature: 0.1
19:28:06.672 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added disable_security=True from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added enable_memory=True from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added generate_gif=False from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added headless=True from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
19:28:06.672 🔍 BROWSER POOL: Added max_failures=2 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added overrides={} from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added retry_delay=10 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added stealth=False from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
19:28:06.673 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
19:28:06.674 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
19:28:06.674 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
19:28:06.674 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
19:28:06.674 🔍 BROWSER POOL: Ensured critical field headless=True
19:28:06.674 🔍 BROWSER POOL: Ensured critical field disable_security=True
19:28:06.674 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
19:28:06.674 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
19:28:06.674 🔍 BROWSER POOL: headless value being passed: True
19:28:06.674 🔍 BROWSER POOL: model_provider=NOT_SET, model_name=NOT_SET
19:28:06.675 🔍 BROWSER POOL: BrowserProfile created - headless=True
19:28:06.675 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=None
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 5: Evaluating page with 19 interactive elements on: https://web-agent-playground.lovable.app/
19:28:06.734 Created browser 12832485-1534-45db-b12e-00c9c3d50625 with config hash 20b966c0
19:28:07.130 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:07.130   FastAPI arguments
19:28:07.131     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:28:10.131 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:10.131   FastAPI arguments
19:28:10.132     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 💡 Thinking:
The user request describes a feature and scenario for user authentication with a successful login using valid credentials. The previous steps have already completed the login process successfully, and the current page shows a dashboard with a message "Login exitoso" and "Bienvenido al sistema de testing," confirming the successful login. This matches the expected outcome in the scenario's "Entonces" step. Therefore, the test scenario is fully validated by the current page state. The next step is to finalize the task by reporting success and summarizing the results to the user.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 👍 Eval: The login was successful, and the dashboard page with a welcome message is displayed, confirming the user logged in successfully. Verdict: Success
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🧠 Memory: The login process was completed with <NAME_EMAIL> and admin123. The system responded with a successful login message and dashboard view, confirming the scenario's expected outcome.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 🎯 Next goal: Complete the task by reporting the successful login scenario execution and provide a summary to the user.

19:28:11.938 🧠 openai/gpt-4.1-mini | 📥 7.3k | 📤 281
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ☑️ Executed action 1/1: done()
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📄 Result: The user authentication feature and scenario for successful login with valid credentials have been fully verified. The user was able to log in with the provided credentials (<EMAIL> / admin123), and the system displayed a welcome message and dashboard confirming the successful login. The scenario steps have been executed and validated as expected. Task completed successfully.
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] 📍 Step 5: Ran 1 actions in 6.98s: ✅ 1
INFO     [browser_use.Agent🅰 6ae6 on 🆂 6c71 🅟 48] ✅ Task completed successfully
19:28:12.045 📊 Per-Model Usage Breakdown:
19:28:12.045   🤖 openai/gpt-4.1-mini: 26.9k tokens | ⬅️ 26.1k | ➡️ 837 | 📞 4 calls | 📈 6.7k/call
19:28:12.054 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61348 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
19:28:12.067 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:61350 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84] 🛑 Closing cdp_url=http://127.0.0.1:61153/ browser context  <Browser type=<BrowserType name=chromium executable_path=/Users/<USER>/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium> version=138.0.7204.23>
INFO     [browser_use.BrowserSession🆂 6c71:61153 #84]  ↳ Killing browser_pid=74648 ~/Library/Caches/ms-playwright/chromium-1179/chrome-mac/Chromium.app/Contents/MacOS/Chromium (terminate() called)
19:28:12.437 Processed 4 screenshots from history (artifacts already created by collector)
19:28:12.438 🗂️  Retrieved 0 artifacts for execution e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:12.438 Processing 4 history items from history.history
19:28:12.438 [STEP EXTRACT] Processing step 1: type=<class 'browser_use.agent.views.AgentHistory'>
19:28:12.438 [STEP EXTRACT] Successfully processed step 1: action_type=unknown, success=True
19:28:12.439 [STEP EXTRACT] Traceback: NoneType: None

19:28:12.439 [STEP EXTRACT] Processing step 2: type=<class 'browser_use.agent.views.AgentHistory'>
19:28:12.439 [STEP EXTRACT] Successfully processed step 2: action_type=unknown, success=True
19:28:12.439 [STEP EXTRACT] Traceback: NoneType: None

19:28:12.439 [STEP EXTRACT] Processing step 3: type=<class 'browser_use.agent.views.AgentHistory'>
19:28:12.439 [STEP EXTRACT] Successfully processed step 3: action_type=unknown, success=True
19:28:12.439 [STEP EXTRACT] Traceback: NoneType: None

19:28:12.440 [STEP EXTRACT] Processing step 4: type=<class 'browser_use.agent.views.AgentHistory'>
19:28:12.440 [STEP EXTRACT] Successfully processed step 4: action_type=unknown, success=True
19:28:12.440 [STEP EXTRACT] Traceback: NoneType: None

19:28:12.440 Checking 4 model actions for done action
19:28:12.440 Final step count: 4 steps processed
19:28:12.440 🔍 Searching for artifacts with execution_id: e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:12.608 🔍 Found 0 total artifacts for execution
19:28:12.608 🔍 Searching with query: {'execution_id': 'e595c7e5-0c4e-4697-8b96-f779e1c880b5', 'type': 'screenshot'}
19:28:12.943 🔍 Found 0 screenshot artifacts
19:28:12.944 ❌ No artifact found for step 1 with step_name: step_1
19:28:12.944 🔍 Searching for artifacts with execution_id: e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:13.114 🔍 Found 0 total artifacts for execution
19:28:13.115 🔍 Searching with query: {'execution_id': 'e595c7e5-0c4e-4697-8b96-f779e1c880b5', 'type': 'screenshot'}
19:28:13.130 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:13.131   FastAPI arguments
19:28:13.132     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:28:13.447 🔍 Found 0 screenshot artifacts
19:28:13.447 ❌ No artifact found for step 2 with step_name: step_2
19:28:13.448 🔍 Searching for artifacts with execution_id: e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:13.613 🔍 Found 0 total artifacts for execution
19:28:13.613 🔍 Searching with query: {'execution_id': 'e595c7e5-0c4e-4697-8b96-f779e1c880b5', 'type': 'screenshot'}
19:28:13.944 🔍 Found 0 screenshot artifacts
19:28:13.944 ❌ No artifact found for step 3 with step_name: step_3
19:28:13.945 🔍 Searching for artifacts with execution_id: e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:14.110 🔍 Found 0 total artifacts for execution
19:28:14.110 🔍 Searching with query: {'execution_id': 'e595c7e5-0c4e-4697-8b96-f779e1c880b5', 'type': 'screenshot'}
19:28:14.442 🔍 Found 0 screenshot artifacts
19:28:14.442 ❌ No artifact found for step 4 with step_name: step_4
19:28:14.442 Agent completed successfully based on final_result: The user authentication feature and scenario for successful login with valid credentials have been fully verified. The user was able to log in with the provided credentials (<EMAIL> / admin123), and the system displayed a welcome message and dashboard confirming the successful login. The scenario steps have been executed and validated as expected. Task completed successfully.
19:28:14.443 Captured 4 raw result items for frontend processing
19:28:14.443 Processing 4 steps to find done action
19:28:14.443 Last step (#4) success: True, action_type: unknown
19:28:14.443 Found success indicator in formatted raw result: Status: ✅ Success
Completion: ✅ Complete
Memory: Task completed: True - The user authentication feat...
19:28:14.443 SUCCESS INDICATOR: Last step successful with completion message - marking as successful
19:28:14.443 SUCCESS DETERMINATION: Using done action success: True
19:28:14.443 SUCCESS DETERMINATION: Done action successful - overriding any intermediate step failures
19:28:14.443 FINAL SUCCESS DETERMINATION: success=True, done_action_success=True, last_step_success=True, agent_completed_successfully=True
19:28:14.444 🤖 Starting AI-powered test analysis...
19:28:14.444 🔍 PRIORITY EXTRACTION: Attempting to extract screenshots directly from history
19:28:14.444 🔍 HISTORY: Attempting to extract screenshots from history.history
19:28:14.444 ✅ Keeping original screenshot 1 quality - small file (97,935 bytes)
19:28:14.444 ✅ HISTORY: Extracted screenshot 1 from state (130580 chars)
19:28:14.445 ✅ Keeping original screenshot 2 quality - small file (48,865 bytes)
19:28:14.445 ✅ HISTORY: Extracted screenshot 2 from state (65156 chars)
19:28:14.445 ✅ Keeping original screenshot 3 quality - small file (47,541 bytes)
19:28:14.445 ✅ HISTORY: Extracted screenshot 3 from state (63388 chars)
19:28:14.446 ✅ Keeping original screenshot 4 quality - small file (119,983 bytes)
19:28:14.446 ✅ HISTORY: Extracted screenshot 4 from state (159980 chars)
19:28:14.446 🖼️ HISTORY: Successfully extracted 4 screenshots for AI analysis
19:28:14.446 🔍 ARTIFACTS DEBUG: result.artifacts = Artifacts(screenshots=[], videos=[], logs=[], generated_code=None, history_file=None, gherkin_scenarios=[])
19:28:14.446 🔍 ARTIFACTS DEBUG: artifacts.screenshots = []
19:28:14.446 🔍 ARTIFACTS DEBUG: artifacts.__dict__ = {'screenshots': [], 'videos': [], 'logs': [], 'generated_code': None, 'history_file': None, 'gherkin_scenarios': []}
19:28:14.447 📸 AI Analysis: Prepared 4 screenshots for analysis
19:28:14.447 🔍 Background jobs check: BACKGROUND_JOBS_AVAILABLE=True, USE_BACKGROUND_JOBS=true, use_background_jobs=True
19:28:14.447 🔄 Background jobs available - will process AI analysis asynchronously
19:28:14.447 ✅ JobManager using local Redis: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
19:28:14.449 Created job ai_analysis_207ffbab for execution e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:14.513 🚀 AI analysis job created: ai_analysis_207ffbab
19:28:14.513 Processed browser_history result: e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:14.514 🔍 ORCHESTRATOR DEBUG: Final status: ExecutionStatus.SUCCESS (type: <enum 'ExecutionStatus'>)
19:28:16.131 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:16.131   FastAPI arguments
19:28:16.132     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:28:16.359 ✅ Created document in executions: 6872b74ed5eaec5e0060b397
19:28:16.360 💾 Saved execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 to the database.
19:28:17.212 ✅ Updated document in projects: 6861fe7bb4804a69aa767eca
19:28:17.212 Added execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 to test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 history
19:28:17.212 📊 Updated history for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 with execution e595c7e5-0c4e-4697-8b96-f779e1c880b5.
19:28:17.212 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 status: ExecutionStatus.SUCCESS
19:28:17.213 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 completed successfully
19:28:17.213 Browser 4e5fee8c-38ae-4d6a-b2a3-40662e0a77d3 contaminated: 
19:28:17.213 Disposed browser 4e5fee8c-38ae-4d6a-b2a3-40662e0a77d3
19:28:17.213 Background execution completed for e595c7e5-0c4e-4697-8b96-f779e1c880b5 with status ExecutionStatus.SUCCESS
19:28:19.131 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:19.132   FastAPI arguments
19:28:19.133     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.SUCCESS
19:28:19.133     🔍 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 is complete, checking MongoDB for latest data including AI analysis
19:28:19.964     ✅ Found updated execution data in MongoDB for e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:19.965     📋 MongoDB metadata present: True
19:28:19.965     🧠 AI Analysis in MongoDB metadata: False
19:28:19.965     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:28:20.200 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:20.201   FastAPI arguments
19:28:20.202     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.SUCCESS
19:28:20.202     🔍 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 is complete, checking MongoDB for latest data including AI analysis
19:28:20.537     ✅ Found updated execution data in MongoDB for e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:20.537     📋 MongoDB metadata present: True
19:28:20.537     🧠 AI Analysis in MongoDB metadata: False
19:28:20.537     📊 AI Analysis Status in MongoDB metadata: processing
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:28:20.592 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:20.593   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:20.596 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:20.597   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:23.131 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:23.131   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:25.131 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:25.131   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:27.130 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:27.130   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:29.131 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:29.132   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:31.130 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:31.131   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:33.130 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:33.131   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:35.131 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:35.132   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:37.129 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:37.130   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:39.130 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:39.131   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:41.129 GET /api/v2/background-jobs/ai_analysis_207ffbab/status
19:28:41.130   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/status HTTP/1.1" 200 OK
19:28:41.133 GET /api/v2/background-jobs/ai_analysis_207ffbab/result
19:28:41.134   FastAPI arguments
INFO:     127.0.0.1:60970 - "GET /api/v2/background-jobs/ai_analysis_207ffbab/result HTTP/1.1" 200 OK
19:28:43.131 GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:43.132   FastAPI arguments
19:28:43.132     Found execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 in simple tracking with status ExecutionStatus.SUCCESS
19:28:43.132     🔍 Execution e595c7e5-0c4e-4697-8b96-f779e1c880b5 is complete, checking MongoDB for latest data including AI analysis
19:28:43.959     ✅ Found updated execution data in MongoDB for e595c7e5-0c4e-4697-8b96-f779e1c880b5
19:28:43.959     📋 MongoDB metadata present: True
19:28:43.960     🧠 AI Analysis in MongoDB metadata: True
19:28:43.960     📊 AI Analysis Status in MongoDB metadata: completed
INFO:     127.0.0.1:60970 - "GET /api/v2/tests/execution/e595c7e5-0c4e-4697-8b96-f779e1c880b5 HTTP/1.1" 200 OK
19:29:53.131 GET /api/v1/events
19:29:53.132   FastAPI arguments
INFO     [browser_use] {"timestamp":"2025-07-12T19:29:54.292295Z","level":"INFO","message":"Consultando 100 eventos (total: 128)","event_type":"info","correlation_id":"0511964e-625e-4feb-8356-4da1c3c393ab","metadata":{"filters":{},"limit":100,"skip":0,"results_count":100,"total_count":128}}
INFO:     127.0.0.1:61872 - "GET /api/v1/events HTTP/1.1" 200 OK
19:30:00.114 GET /api/v1/events
19:30:00.115   FastAPI arguments
INFO     [browser_use] {"timestamp":"2025-07-12T19:30:01.275059Z","level":"INFO","message":"Consultando 100 eventos (total: 128)","event_type":"info","correlation_id":"f2695a0d-c62e-4e26-b697-63f2a6bcfdcf","metadata":{"filters":{},"limit":100,"skip":0,"results_count":100,"total_count":128}}
INFO:     127.0.0.1:61904 - "GET /api/v1/events HTTP/1.1" 200 OK
