             │   File "[BASE64_DATA_FILTERED].py", line 241, in find
             │     raise DatabaseError(f"Failed to find documents in {self.collection_name}: {str(e)}")
             │ src.database.exceptions.DatabaseError: Failed to find documents in projects: 

INFO:     Shutting down
INFO:     Waiting for application shutdown.
18:50:08.952 🔌 Disconnecting from MongoDB
18:50:09.112 ✅ Disconnected from MongoDB
INFO:     Application shutdown complete.
INFO:     Finished server process [67994]
18:50:31.029 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
18:50:31.035 BrowserPool initialized (min: 2, max: 10)
18:50:31.039 PerformanceMonitor initialized (interval: 30s)
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
✅ Browser-use logging configured from /Users/<USER>/Proyectos/qak/libs
INFO     [browser_use.telemetry.service] Anonymized telemetry enabled. See https://docs.browser-use.com/development/telemetry for more information.
18:50:32.180 ✅ Using explicit REDIS_URL: redis://localhost:6379/0# Requiere: redis-server redis-local.conf
/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'schema_extra' has been renamed to 'json_schema_extra'
  warnings.warn(message, UserWarning)
18:50:32.373 Iniciando API web de QA Agent en http://0.0.0.0:8000...
18:50:32.373 Documentacion API disponible en:
18:50:32.373   - Swagger UI: http://localhost:8000/docs
18:50:32.373   - ReDoc: http://localhost:8000/redoc
18:50:32.373 
Presiona Ctrl+C para detener el servidor.
INFO:     Started server process [69033]
INFO:     Waiting for application startup.
18:50:32.662 🔧 Database configuration initialized for development environment
18:50:32.662 🔗 Connection target: aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak
18:50:32.663 🔧 Database manager initialized
18:50:32.663 🔌 Connecting to MongoDB
Logfire project URL: https://logfire-us.pydantic.dev/nahuelcio/qak
18:50:34.541 ✅ Connected to MongoDB database: qak_dev
18:50:34.541 📊 MongoDB Server Version: 8.0.11
18:50:34.541 🚀 Database initialization successful
18:50:34.541 📊 Database indexes will be managed by Beanie ODM models
18:50:34.542 🔧 ODM manager initialized
18:50:34.542 📝 Registered ODM model: Project
18:50:34.542 📝 Registered ODM model: Execution
18:50:34.542 📝 Registered ODM model: CodegenSession
18:50:34.542 📝 Registered ODM model: Artifact
18:50:34.542 📝 Registered ODM model: BrowserConfiguration
18:50:34.542 📝 Registered ODM model: BrowserSessionPool
18:50:34.543 📝 Registered ODM model: BrowserUseEventDocument
18:50:34.543 📝 Registered ODM model: BrowserUseSessionDocument
18:50:34.543 📝 Registered ODM model: BrowserUseTaskDocument
18:50:34.543 📝 Registered ODM model: BrowserUseStepDocument
18:50:34.543 📝 Registered ODM model: BrowserUseFileDocument
18:50:34.709 🚀 Initializing Beanie ODM with 11 models...
18:50:34.709 🧹 Cleaning up potentially conflicting indexes...
18:50:35.046 🗑️ Dropped conflicting index: projects.project_id_1
18:50:35.216 🗑️ Dropped conflicting index: projects.created_at_-1
18:50:35.548 🗑️ Dropped conflicting index: executions.execution_id_1
18:50:35.876 🗑️ Dropped conflicting index: codegen_sessions.session_id_1
18:50:36.207 🗑️ Dropped conflicting index: artifacts.artifact_id_1
18:50:36.207 ✅ Index cleanup completed
18:50:42.163 ✅ Beanie ODM initialization successful
18:50:42.164   📝 Project -> projects
18:50:42.164   📝 Execution -> executions
18:50:42.164   📝 CodegenSession -> codegen_sessions
18:50:42.164   📝 Artifact -> artifacts
18:50:42.164   📝 BrowserConfiguration -> browser_configurations
18:50:42.164   📝 BrowserSessionPool -> browser_session_pools
18:50:42.164   📝 BrowserUseEventDocument -> browser_use_events
18:50:42.165   📝 BrowserUseSessionDocument -> browser_use_sessions
18:50:42.165   📝 BrowserUseTaskDocument -> browser_use_tasks
18:50:42.165   📝 BrowserUseStepDocument -> browser_use_steps
18:50:42.165   📝 BrowserUseFileDocument -> browser_use_files
18:50:42.495 Predefined configuration already exists: test_case
18:50:42.658 Predefined configuration already exists: smoke
18:50:42.821 Predefined configuration already exists: exploration
18:50:42.988 Predefined configuration already exists: exploration_deep
18:50:43.160 Predefined configuration already exists: test_suite
18:50:43.649 ✅ ProjectManagerService: MongoDB mode enabled
18:50:43.649 Service operation: list_projects
18:50:43.650 BrowserPool initialized (min: 2, max: 10)
18:50:43.650 PerformanceMonitor initialized (interval: 30s)
18:50:43.650 ExecutionOrchestrator initialized
18:50:43.650 🔄 Creating shared global EnhancedArtifactCollector instance
18:50:43.651 ✅ Initialized r2 storage backend for artifacts
18:50:43.651 ArtifactCollector initialized (storage: artifacts, max: 10.0GB)
18:50:43.651 EnhancedArtifactCollector initialized with CloudflareR2StorageBackend cloud storage backend (R2-ONLY MODE)
18:50:43.651 Loaded existing artifacts from storage
18:50:43.651 ArtifactCollector initialized successfully
18:50:43.651 🔄 Migration task disabled in R2-only mode - artifacts created directly in cloud
18:50:43.651 ✅ Shared global EnhancedArtifactCollector instance initialized
18:50:43.651 Execution orchestrator initialized with ExecutionRepository.
INFO:     Application startup complete.
18:50:43.652 Loading active/stale sessions from database...
18:50:43.653 Cleaning up old, unfinished sessions from database...
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
18:50:44.690 Processed 0 stale sessions from database.
18:50:44.857 No old, unfinished sessions to clean up.
18:51:35.101 OPTIONS /api/projects/
INFO:     127.0.0.1:65378 - "OPTIONS /api/projects/ HTTP/1.1" 200 OK
18:51:35.139 GET /api/projects/
18:51:35.188   FastAPI arguments
18:51:35.191     ✅ ProjectManagerService: MongoDB mode enabled
18:51:35.955     Service operation: list_projects
18:51:36.039 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:36.044 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:36.052 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:51:36.053 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:36.054 OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
INFO:     127.0.0.1:65390 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65392 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65393 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
INFO:     127.0.0.1:65395 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
INFO:     127.0.0.1:65396 - "OPTIONS /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:51:36.060 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:36.062   FastAPI arguments
18:51:36.064 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:51:36.066   FastAPI arguments
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
               FastAPI arguments
18:51:36.068     ✅ ProjectManagerService: MongoDB mode enabled
             GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
               FastAPI arguments
18:51:36.069     ✅ ProjectManagerService: MongoDB mode enabled
18:51:36.071     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:36.072     Service operation: list_projects
18:51:36.074     Service operation: list_projects
INFO:     127.0.0.1:65381 - "GET /api/projects/ HTTP/1.1" 200 OK
18:51:36.615 OPTIONS /api/analytics-v2 ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-13'
INFO:     127.0.0.1:65381 - "OPTIONS /api/analytics-v2?start_date=2025-06-13&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
18:51:36.618 GET /api/analytics-v2 ? detailed='true' & end_date='2025-07-12' & start_date='2025-06-13'
18:51:36.618   FastAPI arguments
18:51:37.624     ✅ Project found: Web Agent Page
18:51:37.625     ✅ Test suite found: Funcionalidad core
18:51:37.625     ✅ Test case found: Login
18:51:37.625     ✅ Test case found: Login
INFO:     127.0.0.1:65390 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:51:37.627 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:37.628   FastAPI arguments
18:51:37.628     ✅ ProjectManagerService: MongoDB mode enabled
18:51:37.629     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:37.629     Service operation: list_projects
18:51:37.633     ✅ Project found: Web Agent Page
18:51:37.633     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:51:37.633     📝 Has environments attr: True
18:51:37.633     📝 Environments count: 2
INFO:     127.0.0.1:65398 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:51:37.636 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:51:37.637   FastAPI arguments
18:51:37.637     ✅ ProjectManagerService: MongoDB mode enabled
18:51:37.638     Service operation: list_projects
18:51:39.013     ✅ Project found: Web Agent Page
18:51:39.013     ✅ Test suite found: Funcionalidad core
18:51:39.013     ✅ Test case found: Login
18:51:39.014     ✅ Test case found: Login
INFO:     127.0.0.1:65411 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:51:39.015 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:39.016   FastAPI arguments
18:51:39.016     ✅ ProjectManagerService: MongoDB mode enabled
18:51:39.018     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:39.018     Service operation: list_projects
18:51:39.019 OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
INFO:     127.0.0.1:65411 - "OPTIONS /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:51:39.021 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:51:39.021   FastAPI arguments
18:51:39.022     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:39.023     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:51:39.023     ✅ Orchestrator and execution repository initialized
18:51:39.157     ✅ Project found: Web Agent Page
18:51:39.157     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:51:39.157     📝 Has environments attr: True
18:51:39.157     📝 Environments count: 2
INFO:     127.0.0.1:65398 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
18:51:39.161 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:51:39.161   FastAPI arguments
18:51:39.161     ✅ ProjectManagerService: MongoDB mode enabled
18:51:39.162     Service operation: list_projects
18:51:39.351     ✅ Project found: Web Agent Page
18:51:39.351     ✅ Test suite found: Funcionalidad core
18:51:39.352     ✅ Test case found: Login
18:51:39.352     ✅ Test case found: Login
INFO:     127.0.0.1:65390 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/suites/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/tests/790f99de-2eb6-4f92-8f87-8cc07a2b87d7 HTTP/1.1" 200 OK
18:51:39.507     ✅ Project found: Web Agent Page
18:51:39.508     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:51:39.508     📝 Has environments attr: True
18:51:39.508     📝 Environments count: 2
INFO:     127.0.0.1:65424 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
INFO:     127.0.0.1:65402 - "GET /api/analytics-v2?start_date=2025-06-13&end_date=2025-07-12&detailed=true HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:51:59.040     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:59.041     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:65411 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:51:59.045 GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
18:51:59.046   FastAPI arguments
18:51:59.047     🔍 Getting executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:51:59.047     🔍 Project ID: 29dff68c-370c-4d83-b9eb-0cd98d13258a, Suite ID: 52eb8e73-9b8b-4c36-aa79-9a71460b2f51
18:51:59.047     ✅ Orchestrator and execution repository initialized
18:51:59.137 GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments
18:51:59.138   FastAPI arguments
18:51:59.138     ✅ ProjectManagerService: MongoDB mode enabled
18:51:59.139     Service operation: list_projects
18:51:59.468     ✅ Project found: Web Agent Page
18:51:59.468     📝 Project type: <class 'src.utilities.project_manager.Project'>
18:51:59.468     📝 Has environments attr: True
18:51:59.468     📝 Environments count: 2
INFO:     127.0.0.1:65411 - "GET /api/projects/29dff68c-370c-4d83-b9eb-0cd98d13258a/environments HTTP/1.1" 200 OK
             GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions ? limit='20' & summary_only='true' & include_ai_analysis='false'
               GET /api/v2/tests/{project_id}/{suite_id}/{test_id}/executions (get_test_executions)
18:52:07.441     📊 Found 39 total executions, returning 20 (limit: 20) for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:52:07.442     ✅ Successfully processed 20 executions for test_id: 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO:     127.0.0.1:65402 - "GET /api/v2/tests/29dff68c-370c-4d83-b9eb-0cd98d13258a/52eb8e73-9b8b-4c36-aa79-9a71460b2f51/790f99de-2eb6-4f92-8f87-8cc07a2b87d7/executions?limit=20&include_ai_analysis=false&summary_only=true HTTP/1.1" 200 OK
18:54:08.877 OPTIONS /api/v2/tests/execute
INFO:     127.0.0.1:49712 - "OPTIONS /api/v2/tests/execute HTTP/1.1" 200 OK
18:54:08.882 POST /api/v2/tests/execute
18:54:08.884   FastAPI arguments
18:54:08.886     🔧 BROWSER CONFIG: Resolving configuration for execution type case
18:54:08.887     🔧 BROWSER CONFIG: MongoDB query filters: {'execution_types': {'$in': ['case']}, 'is_active': True}
18:54:09.053     Found 1 configurations for execution type: case
18:54:09.053       Config 1: Test Case (Default) - execution_types: ['case', 'full'] - model_name: None
18:54:09.053     Ordered configurations by priority - OpenRouter configs first
18:54:09.054       1. Test Case (Default) (provider: openrouter, usage: 0)
18:54:09.054     🔧 BROWSER CONFIG: Found DB config 'Test Case (Default)' for type case
18:54:09.054     🔧 BROWSER CONFIG: MongoDB settings: model_provider=openrouter, model_name=None, headless=True
18:54:09.054     🔧 BROWSER CONFIG FIX: Assigned default OpenRouter model for provider=openrouter: openai/gpt-4.1-mini
18:54:09.054     🔧 BROWSER CONFIG: DB config failed (BrowserConfig.__init__() got an unexpected keyword argument 'capture_beyond_viewport'), using hardcoded fallback
18:54:09.054     🔧 BROWSER CONFIG: Applied hardcoded fallback for type case with headless=True
18:54:09.055     ✅ ProjectManagerService: MongoDB mode enabled
18:54:09.055     🔍 ProjectManager.get_test_case: project_id=29dff68c-370c-4d83-b9eb-0cd98d13258a, suite_id=52eb8e73-9b8b-4c36-aa79-9a71460b2f51, test_id=790f99de-2eb6-4f92-8f87-8cc07a2b87d7
18:54:09.055     Service operation: list_projects
18:54:09.753     ✅ Project found: Web Agent Page
18:54:09.753     ✅ Test suite found: Funcionalidad core
18:54:09.753     ✅ Test case found: Login
18:54:10.089     🔍 EXECUTION DEBUG: Environment name resolved: QA
18:54:10.089     Created execution context 772fa60c-10ac-4dac-bcee-833aebc7c8fe for TestType.CASE
18:54:10.090     🔍 CONTEXT DEBUG: No application_version provided or empty
18:54:10.090     Registering execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in orchestrator.active_executions
18:54:10.090     Orchestrator instance ID: 5117278864
18:54:10.090     Active executions before: []
18:54:10.090     Execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe status: ExecutionStatus.RUNNING
18:54:10.090     Active executions after: ['772fa60c-10ac-4dac-bcee-833aebc7c8fe']
18:54:10.091     Successfully registered 772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:10.091     Simple tracking has: ['772fa60c-10ac-4dac-bcee-833aebc7c8fe']
18:54:10.091     Execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe started and returned immediately
INFO:     127.0.0.1:49712 - "POST /api/v2/tests/execute HTTP/1.1" 200 OK
18:54:10.092 Background execution started for 772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:10.092 Execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe status: ExecutionStatus.RUNNING
18:54:10.434 Using specified environment: QA
18:54:10.434 🔍 ORCHESTRATOR DEBUG: Resolved environment: Environment(name='QA', base_url='https://web-agent-playground.lovable.app', is_default=True)
18:54:10.434 🔍 ORCHESTRATOR DEBUG: Environment details - ID: 079f669c-ea2c-43f4-bf76-0a98897b1353, Name: QA
18:54:10.435 Using absolute URL: https://web-agent-playground.lovable.app
18:54:10.435 🔍 ORCHESTRATOR DEBUG: Constructed URL: https://web-agent-playground.lovable.app
18:54:10.435 Environment info set for execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe: QA -> https://web-agent-playground.lovable.app
18:54:10.435 🔍 ORCHESTRATOR DEBUG: Environment info set in context
18:54:10.435 BrowserPool initialized successfully
18:54:10.435 🔍 BROWSER POOL: Creating browser with COMPLETE MongoDB config:
18:54:10.436   - headless: True
18:54:10.436   - model_provider: openrouter
18:54:10.436   - model_name: openai/gpt-4.1-mini
18:54:10.436   - highlight_elements: False
18:54:10.436   - temperature: 0.1
18:54:10.436 🔍 BROWSER POOL: Added deterministic_rendering=False from MongoDB config
18:54:10.436 🔍 BROWSER POOL: Added disable_security=False from MongoDB config
18:54:10.436 🔍 BROWSER POOL: Added enable_memory=False from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added generate_gif=True from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added headless=True from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added highlight_elements=False from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added keep_alive=False from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added max_failures=3 from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added max_steps=50 from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added maximum_wait_page_load_time=15.0 from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added minimum_wait_page_load_time=0.5 from MongoDB config
18:54:10.437 🔍 BROWSER POOL: Added model_name=openai/gpt-4.1-mini from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added model_provider=openrouter from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added overrides={} from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added retry_delay=5 from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added stealth=False from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added temperature=0.1 from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added use_vision=True from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added viewport_expansion=1200 from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added wait_between_actions=1.0 from MongoDB config
18:54:10.438 🔍 BROWSER POOL: Added wait_for_network_idle_page_load_time=1.0 from MongoDB config
18:54:10.439 🔍 BROWSER POOL: Ensured critical field headless=True
18:54:10.439 🔍 BROWSER POOL: Ensured critical field disable_security=False
18:54:10.439 🔍 BROWSER POOL: Ensured critical field highlight_elements=False
18:54:10.439 🔍 BROWSER POOL: Final profile_args keys: ['deterministic_rendering', 'disable_security', 'enable_memory', 'generate_gif', 'headless', 'highlight_elements', 'keep_alive', 'max_failures', 'max_steps', 'maximum_wait_page_load_time', 'minimum_wait_page_load_time', 'model_name', 'model_provider', 'overrides', 'retry_delay', 'stealth', 'temperature', 'use_vision', 'viewport_expansion', 'wait_between_actions', 'wait_for_network_idle_page_load_time']
18:54:10.439 🔍 BROWSER POOL: headless value being passed: True
18:54:10.439 🔍 BROWSER POOL: model_provider=openrouter, model_name=openai/gpt-4.1-mini
18:54:10.440 🔍 BROWSER POOL: BrowserProfile created - headless=True
18:54:10.440 ✅ BROWSER POOL: Created browser with MongoDB config - headless=True, model_provider=openrouter
18:54:10.490 Created browser b2ad7d4d-2f89-456e-88dc-a93bd1eb9de3 with config hash 20b966c0
18:54:10.490 Created new browser b2ad7d4d-2f89-456e-88dc-a93bd1eb9de3 for 772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:10.490 Executing with strategy: TestCaseStrategy
18:54:10.490 Executing test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7 using TestCaseStrategy.
18:54:10.490 Created initial actions to navigate to: https://web-agent-playground.lovable.app
18:54:10.490 🔧 STRATEGY CONFIG: Using resolved config from context: headless=True, max_steps=50
18:54:10.490 🔧 STRATEGY CONFIG: Created agent config with max_steps=50 from DB config
18:54:10.491 Creating OpenRouter LLM with model: openai/gpt-4.1-mini
18:54:10.491 Using provided Gherkin scenario for test case 790f99de-2eb6-4f92-8f87-8cc07a2b87d7
INFO     [browser_use.agent.service] 💾 File system path: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browser_use_agent_5d198a69
INFO     [browser_use.Agent🅰 8a69 on 🆂 8a69 🅟 00] 🧠 Starting a browser-use agent 0.5.4 with base_model=openai/gpt-4.1-mini +vision extraction_model=openai/gpt-4.1-mini +file_system
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 00] 🚀 Starting task: Caracterstica: Autenticacin de Usuario

Escenario: Inicio de sesin exitoso con credenciales vlidas
  Dado que el usuario est en la pgina de inicio de sesin
  Cuando el usuario ingresa "<EMAIL>" como nombre de usuario y "admin123" como contrasea
  Y el usuario intenta iniciar sesin
  Entonces el usuario debera iniciar sesin exitosamente
18:54:10.888 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:49724 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
18:54:10.899 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:49726 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 664c:None #60] 🎭 Launching new local browser playwright:chromium keep_alive=False user_data_dir= ~/.config/browseruse/profiles/default
WARNING  [browser_use.BrowserSession🆂 664c:None #60] ⚠️ SingletonLock conflict detected. Profile at ~/.config/browseruse/profiles/default is locked. Using temporary profile instead: /var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-evmi8sv9
INFO     [browser_use.BrowserSession🆂 664c:None #60]  ↳ Spawning Chrome subprocess listening on CDP http://127.0.0.1:49731/ with user_data_dir= /private/var/folders/p3/2xz8j0j562v_6b0yc13vzt100000gn/T/browseruse-tmp-singleton-evmi8sv9
18:54:13.173 OPTIONS /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
INFO:     127.0.0.1:49712 - "OPTIONS /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
18:54:13.175 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:13.176   FastAPI arguments
18:54:13.177     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🌎 Connecting to newly spawned browser via CDP http://127.0.0.1:49731/ -> browser_pid=69426 (local)
18:54:16.173 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:16.173   FastAPI arguments
18:54:16.174     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.controller.service] 🔗  Opened new tab #1 with url https://web-agent-playground.lovable.app
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] ☑️ Executed action 1/1: go_to_url()
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:18.440 take_screenshot failed (attempt 1/2): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters. Waiting 1.0s before retry...
18:54:19.173 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:19.173   FastAPI arguments
18:54:19.174     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:19.450 take_screenshot failed after 2 attempts over 1.0s. Semaphore wait: 0.0s. Final error: Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
WARNING  [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed for web-agent-playground.l…: Error CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 📍 Step 2: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:54:22.173 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:22.174   FastAPI arguments
18:54:22.174     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 💡 Thinking:
The user request describes a test scenario for successful login with valid credentials on a login page. The page currently loaded is a login form with fields for email and password, and a button labeled 'Iniciar Sesión' (Log In). The placeholders in the input fields match the credentials given in the scenario. The next step is to input the email '<EMAIL>' into the email field (index 2) and the password 'admin123' into the password field (index 4), then click the 'Iniciar Sesión' button (index 6) to attempt login. This will test if the login is successful as per the scenario.
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 👍 Eval: The page with the login form loaded successfully, showing the required input fields and login button. Verdict: Success
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 🧠 Memory: At the login page with email and password input fields and a login button. Ready to input credentials and attempt login.
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 🎯 Next goal: Input the email '<EMAIL>' into the email field to start the login process.

18:54:24.279 🧠 openai/gpt-4.1-mini | 📥 4.8k | 📤 242
INFO     [browser_use.controller.service] ⌨️  Input <EMAIL> into index 2
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 📍 Step 2: Ran 1 actions in 7.35s: ✅ 1
18:54:24.645 POST /api/browser-use/v1/api/v1/events
18:54:24.646 POST /api/browser-use/v1/api/v1/oauth/device/authorize
INFO:     127.0.0.1:49810 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49811 - "POST /api/browser-use/v1/api/v1/oauth/device/authorize HTTP/1.1" 404 Not Found
WARNING  [browser_use.sync.auth] Cloud sync authentication endpoint not found (404). Check your BROWSER_USE_CLOUD_API_URL setting.
18:54:25.172 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:25.173   FastAPI arguments
18:54:25.174     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:25.679 take_screenshot failed (attempt 1/2): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters. Waiting 1.0s before retry...
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:26.690 take_screenshot failed after 2 attempts over 1.0s. Semaphore wait: 0.0s. Final error: Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
WARNING  [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed for web-agent-playground.l…: Error CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 📍 Step 3: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:54:28.172 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:28.173   FastAPI arguments
18:54:28.174     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 💡 Thinking:
The user request is to test a successful login scenario with valid credentials on the login page. I have already input the email '<EMAIL>' into the email field (index 2). The next step is to input the password 'admin123' into the password field (index 4) and then click the 'Iniciar Sesión' button (index 6) to attempt login. This will complete the login attempt and allow me to verify if the login is successful as per the scenario.
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 👍 Eval: Successfully input the email '<EMAIL>' into the email field. Verdict: Success
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 🧠 Memory: Email '<EMAIL>' has been entered into the email input field on the login page. Ready to input password and submit the login form.
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 🎯 Next goal: Input the password 'admin123' into the password field and then click the 'Iniciar Sesión' button to attempt login.

18:54:29.692 🧠 openai/gpt-4.1-mini | 🆕 598 + 💾 4.4k | 📤 201
INFO     [browser_use.controller.service] ⌨️  Input admin123 into index 4
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] ☑️ Executed action 1/1: input_text()
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 📍 Step 3: Ran 1 actions in 5.36s: ✅ 1
18:54:30.004 POST /api/browser-use/v1/api/v1/events
INFO:     127.0.0.1:49833 - "POST /api/browser-use/v1/api/v1/events HTTP/1.1" 404 Not Found
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] ➡️ Page navigation [1]web-agent-playground.lovable.app/ took 1.01s
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:31.040 take_screenshot failed (attempt 1/2): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters. Waiting 1.0s before retry...
18:54:31.172 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:31.173   FastAPI arguments
18:54:31.173     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Document size 1512x982
INFO     [browser_use.BrowserSession🆂 664c:49731 #60] 🔥 HEADLESS SCREENSHOT: Using captureBeyondViewport with document-sized clip
ERROR    [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed on page web-agent-playground.l… (possibly crashed): Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
18:54:32.051 take_screenshot failed after 2 attempts over 1.0s. Semaphore wait: 0.0s. Final error: Error: CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
WARNING  [browser_use.BrowserSession🆂 664c:49731 #60] ❌ Screenshot failed for web-agent-playground.l…: Error CDPSession.send: Protocol error (Page.captureScreenshot): Invalid parameters
INFO     [browser_use.Agent🅰 8a69 on 🆂 664c 🅟 20] 📍 Step 4: Evaluating page with 10 interactive elements on: https://web-agent-playground.lovable.app/
18:54:34.173 GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe
18:54:34.174   FastAPI arguments
18:54:34.175     Found execution 772fa60c-10ac-4dac-bcee-833aebc7c8fe in simple tracking with status ExecutionStatus.RUNNING
INFO:     127.0.0.1:49712 - "GET /api/v2/tests/execution/772fa60c-10ac-4dac-bcee-833aebc7c8fe HTTP/1.1" 200 OK
