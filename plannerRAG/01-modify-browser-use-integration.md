# 1. Modificar Browser-Use Integration

## 🎯 Objetivo
Modificar la integración con `libs/browser_use` para añadir hooks de supervisión del planner.

## 📂 Archivos a Modificar

### 1.1 `src/core/browser_pool.py`

**Cambios necesarios:**
- Añadir callback hooks para el planner
- Interceptar pasos del agente antes de ejecución
- Reportar estado después de cada paso

```python
# Añadir imports
from src.services.planner_service import PlannerService

# En _create_browser_object(), después de crear browser_session:
if hasattr(config, 'enable_planner') and config.enable_planner:
    planner = PlannerService(
        original_task=config.original_task,
        max_steps_before_check=config.planner_check_interval or 3
    )
    browser_session.planner = planner
```

### 1.2 `src/Utilities/browser_helper.py`

**Cambios necesarios:**
- Modificar `create_and_run_agent()` para incluir planner
- Añadir configuración de planner en `BrowserHelperConfig`

```python
# En BrowserHelperConfig, añadir:
enable_planner: bool = True
planner_check_interval: int = 3
original_task: str = None
task_completion_threshold: float = 0.8

# En create_and_run_agent(), modificar configuración del agente:
if config.enable_planner:
    agent_kwargs["step_callback"] = planner.evaluate_step
    agent_kwargs["should_continue_callback"] = planner.should_continue
```

### 1.3 `libs/browser_use/agent/service.py` (Wrapper)

**Crear wrapper para interceptar pasos:**

```python
# Nuevo archivo: src/wrappers/enhanced_browser_agent.py
class EnhancedBrowserAgent:
    def __init__(self, original_agent, planner=None):
        self.agent = original_agent
        self.planner = planner
        
    async def step(self):
        # Pre-step evaluation
        if self.planner:
            should_continue = await self.planner.evaluate_before_step(
                current_step=self.agent.current_step,
                context=self.agent.get_context()
            )
            if not should_continue:
                return False
        
        # Execute original step
        result = await self.agent.step()
        
        # Post-step evaluation
        if self.planner:
            await self.planner.evaluate_after_step(
                step_result=result,
                current_step=self.agent.current_step
            )
        
        return result
```

## 🔧 Implementación Detallada

### Paso 1.1: Modificar BrowserHelperConfig
```python
# En src/Utilities/browser_helper.py
class BrowserHelperConfig:
    # ...existing code...
    
    # Planner configuration
    enable_planner: bool = True
    planner_check_interval: int = 3  # Evaluar cada 3 pasos
    original_task: Optional[str] = None
    task_completion_threshold: float = 0.8  # 80% de confianza para completar
    max_planning_steps: int = 50  # Máximo de pasos antes de forzar parada
    planning_model: str = "openai/gpt-4o-mini"  # Modelo para planner
```

### Paso 1.2: Añadir Hooks en Browser Pool
```python
# En src/core/browser_pool.py, método _create_browser_object()

# Después de crear browser_session, antes del return:
if getattr(config, 'enable_planner', False):
    from src.services.planner_service import PlannerService
    
    planner = PlannerService(
        original_task=getattr(config, 'original_task', ''),
        check_interval=getattr(config, 'planner_check_interval', 3),
        completion_threshold=getattr(config, 'task_completion_threshold', 0.8),
        max_steps=getattr(config, 'max_planning_steps', 50)
    )
    
    # Wrap the browser session with planner
    from src.wrappers.enhanced_browser_agent import EnhancedBrowserAgent
    browser_session = EnhancedBrowserAgent(browser_session, planner)
    
    logger.info(f"🧠 PLANNER: Enabled for task: {getattr(config, 'original_task', 'Unknown')}")
```

### Paso 1.3: Modificar Strategy Classes
```python
# En src/execution/strategies/test_case_strategy.py

# Modificar execute() para pasar original_task al config:
async def execute(self, context: ExecutionContext) -> StandardResult:
    # ...existing code...
    
    # Antes de crear el agent, añadir:
    if hasattr(config, 'enable_planner'):
        config.original_task = context.test_case.scenario  # Gherkin scenario
        config.planner_check_interval = 3
        config.task_completion_threshold = 0.9  # Más estricto para test cases
        
        logger.info(f"🧠 PLANNER: Set original task: {config.original_task}")
```

## ✅ Validación

### Tests a crear:
1. **Test de integración**: Verificar que planner se inicializa correctamente
2. **Test de hooks**: Confirmar que callbacks se ejecutan
3. **Test de wrapper**: Validar que EnhancedBrowserAgent funciona

### Logs esperados:
```
🧠 PLANNER: Enabled for task: <NAME_EMAIL>
🧠 PLANNER: Evaluating step 3/50 - Task completion: 45%
🧠 PLANNER: Task completed successfully at step 7/50 - Stopping execution
```

## 📋 Checklist de Implementación

- [ ] Modificar `BrowserHelperConfig` con opciones de planner
- [ ] Crear `EnhancedBrowserAgent` wrapper
- [ ] Añadir hooks en `browser_pool.py`
- [ ] Modificar strategies para pasar `original_task`
- [ ] Crear tests unitarios
- [ ] Validar integración end-to-end

## 🔗 Siguiente Paso
Una vez completado, proceder con `02-create-planner-agent.md`
