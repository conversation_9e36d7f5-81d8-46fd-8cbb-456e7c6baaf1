# 2. <PERSON><PERSON><PERSON> Planner Agent

## 🎯 Objetivo
Implementar el agente de planificación que supervisará y controlará la ejecución de tareas.

## 📂 Archivo Principal: `src/services/planner_service.py`

## 🧠 Arquitectura del Planner

```
PlannerService
├── TaskAnalyzer (analiza tarea original)
├── ProgressEvaluator (evalúa progreso actual) 
├── DecisionEngine (decide si continuar/parar)
├── GoalTracker (rastrea objetivos cumplidos)
└── ContextManager (maneja contexto histórico)
```

## 🔧 Implementación Detallada

### Estructura Principal

```python
# src/services/planner_service.py

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging

logger = logging.getLogger(__name__)

class PlannerDecision(str, Enum):
    CONTINUE = "continue"
    STOP_SUCCESS = "stop_success" 
    STOP_FAILURE = "stop_failure"
    REDIRECT = "redirect"
    OPTIMIZE = "optimize"

@dataclass
class TaskProgress:
    step_number: int
    completion_percentage: float
    objectives_completed: List[str]
    current_action: str
    confidence_score: float
    issues_detected: List[str]

@dataclass
class PlannerContext:
    original_task: str
    parsed_objectives: List[str]
    current_progress: TaskProgress
    step_history: List[Dict[str, Any]]
    execution_context: Dict[str, Any]

class PlannerService:
    def __init__(
        self,
        original_task: str,
        check_interval: int = 3,
        completion_threshold: float = 0.8,
        max_steps: int = 50,
        model_provider: str = "openrouter"
    ):
        self.original_task = original_task
        self.check_interval = check_interval
        self.completion_threshold = completion_threshold
        self.max_steps = max_steps
        self.model_provider = model_provider
        
        # Internal state
        self.step_count = 0
        self.last_evaluation_step = 0
        self.parsed_objectives = self._parse_task_objectives()
        self.step_history = []
        self.completed_objectives = []
        
        # Components
        self.task_analyzer = TaskAnalyzer(original_task)
        self.progress_evaluator = ProgressEvaluator()
        self.decision_engine = DecisionEngine(completion_threshold)
        self.goal_tracker = GoalTracker(self.parsed_objectives)
        
        logger.info(f"🧠 PLANNER: Initialized for task: {original_task}")
        logger.info(f"🎯 PLANNER: Parsed {len(self.parsed_objectives)} objectives")
```

### TaskAnalyzer - Análisis de Tarea Original

```python
class TaskAnalyzer:
    def __init__(self, original_task: str):
        self.original_task = original_task
        self.objectives = []
        self.task_type = self._detect_task_type()
        
    def _detect_task_type(self) -> str:
        """Detecta el tipo de tarea (login, form_fill, navigation, etc.)"""
        task_lower = self.original_task.lower()
        
        if any(word in task_lower for word in ['login', 'signin', 'autenticación']):
            return "login"
        elif any(word in task_lower for word in ['form', 'formulario', 'submit']):
            return "form_completion" 
        elif any(word in task_lower for word in ['extract', 'scrape', 'data']):
            return "data_extraction"
        elif any(word in task_lower for word in ['navigate', 'go to', 'visit']):
            return "navigation"
        else:
            return "general"
    
    def parse_gherkin_objectives(self) -> List[str]:
        """Extrae objetivos de un escenario Gherkin"""
        objectives = []
        lines = self.original_task.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('Dado que'):
                objectives.append(f"PRECONDITION: {line[9:]}")
            elif line.startswith('Cuando'):
                objectives.append(f"ACTION: {line[7:]}")
            elif line.startswith('Entonces'):
                objectives.append(f"EXPECTED: {line[9:]}")
            elif line.startswith('Y '):
                objectives.append(f"AND: {line[2:]}")
                
        return objectives
```

### ProgressEvaluator - Evaluación de Progreso

```python
class ProgressEvaluator:
    def __init__(self):
        self.llm_service = self._get_llm_service()
    
    async def evaluate_progress(
        self, 
        context: PlannerContext,
        current_state: Dict[str, Any]
    ) -> TaskProgress:
        """Evalúa el progreso actual usando LLM"""
        
        prompt = self._build_evaluation_prompt(context, current_state)
        
        try:
            response = await self.llm_service.make_request({
                "messages": [
                    {"role": "system", "content": "You are a task progress evaluator..."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 500
            })
            
            # Parse structured response
            evaluation = self._parse_llm_response(response.content)
            
            return TaskProgress(
                step_number=context.current_progress.step_number,
                completion_percentage=evaluation["completion_percentage"],
                objectives_completed=evaluation["objectives_completed"],
                current_action=evaluation["current_action"],
                confidence_score=evaluation["confidence_score"],
                issues_detected=evaluation["issues_detected"]
            )
            
        except Exception as e:
            logger.error(f"🧠 PLANNER ERROR: Failed to evaluate progress: {e}")
            return self._fallback_evaluation(context)
    
    def _build_evaluation_prompt(self, context: PlannerContext, current_state: Dict[str, Any]) -> str:
        return f"""
        ORIGINAL TASK: {context.original_task}
        
        PARSED OBJECTIVES:
        {chr(10).join(f"- {obj}" for obj in context.parsed_objectives)}
        
        CURRENT STEP: {context.current_progress.step_number}
        LAST ACTION: {current_state.get('last_action', 'Unknown')}
        CURRENT PAGE: {current_state.get('current_url', 'Unknown')}
        PAGE ELEMENTS: {len(current_state.get('interactive_elements', []))} interactive elements
        
        STEP HISTORY (last 3):
        {self._format_recent_history(context.step_history[-3:])}
        
        EVALUATE:
        1. What percentage of the original task is completed? (0-100)
        2. Which specific objectives have been completed?
        3. What is the current action being performed?
        4. Confidence score that task will be completed successfully? (0-1)
        5. Any issues or concerns detected?
        
        Respond in JSON format:
        {{
            "completion_percentage": <number>,
            "objectives_completed": [<list of completed objectives>],
            "current_action": "<description>",
            "confidence_score": <number>,
            "issues_detected": [<list of issues>]
        }}
        """
```

### DecisionEngine - Motor de Decisiones

```python
class DecisionEngine:
    def __init__(self, completion_threshold: float = 0.8):
        self.completion_threshold = completion_threshold
        
    async def make_decision(
        self, 
        progress: TaskProgress,
        context: PlannerContext
    ) -> Tuple[PlannerDecision, str]:
        """Decide la próxima acción basada en el progreso"""
        
        # Check for completion
        if progress.completion_percentage >= (self.completion_threshold * 100):
            return PlannerDecision.STOP_SUCCESS, f"Task completed with {progress.completion_percentage}% completion"
        
        # Check for failure conditions
        if progress.confidence_score < 0.3:
            return PlannerDecision.STOP_FAILURE, f"Low confidence ({progress.confidence_score:.2f}) - likely failure"
        
        # Check for excessive steps
        if progress.step_number >= context.execution_context.get('max_steps', 50):
            return PlannerDecision.STOP_FAILURE, "Maximum steps reached"
        
        # Check for issues
        if progress.issues_detected:
            critical_issues = [issue for issue in progress.issues_detected if 'critical' in issue.lower()]
            if critical_issues:
                return PlannerDecision.STOP_FAILURE, f"Critical issues detected: {critical_issues}"
        
        # Check for redirection needs
        if self._needs_redirection(progress, context):
            return PlannerDecision.REDIRECT, "Task seems to be going off-track"
        
        # Default: continue
        return PlannerDecision.CONTINUE, "Task progressing normally"
    
    def _needs_redirection(self, progress: TaskProgress, context: PlannerContext) -> bool:
        """Detecta si la tarea se está desviando del objetivo"""
        
        # Si está extrayendo datos pero la tarea original era solo login
        if (context.original_task.lower().find('login') != -1 and 
            progress.current_action.lower().find('extract') != -1):
            return True
            
        # Si hay demasiados pasos para una tarea simple
        if (progress.step_number > 10 and 
            any(word in context.original_task.lower() for word in ['login', 'click', 'navigate'])):
            return True
            
        return False
```

### Métodos Principales de PlannerService

```python
class PlannerService:
    # ...existing code...
    
    async def evaluate_before_step(self, current_step: int, context: Dict[str, Any]) -> bool:
        """Evalúa antes de ejecutar un paso"""
        self.step_count = current_step
        
        # Solo evaluar cada check_interval pasos
        if (current_step - self.last_evaluation_step) < self.check_interval:
            return True
            
        self.last_evaluation_step = current_step
        
        # Construir contexto
        planner_context = PlannerContext(
            original_task=self.original_task,
            parsed_objectives=self.parsed_objectives,
            current_progress=TaskProgress(
                step_number=current_step,
                completion_percentage=0,  # Se calculará en evaluate_progress
                objectives_completed=self.completed_objectives,
                current_action=context.get('last_action', ''),
                confidence_score=1.0,
                issues_detected=[]
            ),
            step_history=self.step_history,
            execution_context={'max_steps': self.max_steps}
        )
        
        # Evaluar progreso
        progress = await self.progress_evaluator.evaluate_progress(planner_context, context)
        
        # Tomar decisión
        decision, reason = await self.decision_engine.make_decision(progress, planner_context)
        
        logger.info(f"🧠 PLANNER Step {current_step}: {decision.value} - {reason}")
        logger.info(f"📊 PLANNER Progress: {progress.completion_percentage}% complete")
        
        # Actuar según decisión
        if decision == PlannerDecision.STOP_SUCCESS:
            logger.info(f"✅ PLANNER: Task completed successfully at step {current_step}")
            return False  # Stop execution
        elif decision == PlannerDecision.STOP_FAILURE:
            logger.warning(f"❌ PLANNER: Task failed at step {current_step} - {reason}")
            return False  # Stop execution
        elif decision == PlannerDecision.REDIRECT:
            logger.warning(f"🔄 PLANNER: Task redirection needed at step {current_step}")
            # TODO: Implement redirection logic
            return True  # Continue for now
        
        return True  # Continue execution
    
    async def evaluate_after_step(self, step_result: Any, current_step: int):
        """Evalúa después de ejecutar un paso"""
        
        # Guardar historial
        self.step_history.append({
            'step': current_step,
            'result': str(step_result)[:200],  # Truncate for storage
            'timestamp': asyncio.get_event_loop().time()
        })
        
        # Mantener solo últimos 10 pasos
        if len(self.step_history) > 10:
            self.step_history.pop(0)
```

## 🔧 Utilidades y Helpers

```python
# src/services/planner_helpers.py

class GoalTracker:
    def __init__(self, objectives: List[str]):
        self.objectives = objectives
        self.completed = []
        self.in_progress = []
        
    def mark_completed(self, objective: str):
        if objective in self.objectives and objective not in self.completed:
            self.completed.append(objective)
            if objective in self.in_progress:
                self.in_progress.remove(objective)
    
    def get_completion_ratio(self) -> float:
        if not self.objectives:
            return 1.0
        return len(self.completed) / len(self.objectives)

class ContextManager:
    def __init__(self):
        self.execution_context = {}
        self.browser_state = {}
        self.task_metadata = {}
    
    def update_browser_state(self, state: Dict[str, Any]):
        self.browser_state.update(state)
    
    def get_relevant_context(self) -> Dict[str, Any]:
        return {
            'execution': self.execution_context,
            'browser': self.browser_state,
            'metadata': self.task_metadata
        }
```

## ✅ Tests a Crear

```python
# tests/test_planner_service.py

import pytest
from src.services.planner_service import PlannerService, PlannerDecision

@pytest.mark.asyncio
async def test_planner_initialization():
    planner = PlannerService("<NAME_EMAIL>")
    assert planner.original_task == "<NAME_EMAIL>"
    assert len(planner.parsed_objectives) > 0

@pytest.mark.asyncio  
async def test_simple_login_completion():
    planner = PlannerService("<NAME_EMAIL>")
    
    # Simulate successful login
    context = {'last_action': 'login_success', 'current_url': '/dashboard'}
    should_continue = await planner.evaluate_before_step(5, context)
    
    # Should detect completion and stop
    assert should_continue == False

@pytest.mark.asyncio
async def test_task_redirection_detection():
    planner = PlannerService("<NAME_EMAIL>") 
    
    # Simulate going off-track (extracting data)
    context = {'last_action': 'extract_sales_data', 'current_url': '/reports'}
    should_continue = await planner.evaluate_before_step(10, context)
    
    # Should detect redirection need
    # Implementation depends on decision logic
```

## 📋 Checklist de Implementación

- [ ] Crear `PlannerService` clase principal
- [ ] Implementar `TaskAnalyzer` 
- [ ] Implementar `ProgressEvaluator` con LLM
- [ ] Implementar `DecisionEngine`
- [ ] Crear `GoalTracker` y `ContextManager`
- [ ] Añadir integración con LLM service
- [ ] Crear tests unitarios
- [ ] Validar con casos reales

## 🔗 Siguiente Paso
Proceder con `03-implement-rag-system.md` para añadir contexto histórico.
