# 4. Implementar Sistema RAG de Contexto del Proyecto Bajo Prueba

## 🎯 Objetivo
Crear un sistema RAG especializado que proporcione contexto rico del **proyecto/aplicación que se está testeando** junto con las tareas específicas, permitiendo que el agente comprenda mejor el propósito, funcionalidades y estructura del sistema objetivo para tomar decisiones más inteligentes durante las pruebas.

## 🧠 Concepto del Sistema RAG de Contexto del Proyecto Bajo Prueba

```
RAG Contexto del Proyecto Bajo Prueba
├── TargetProjectContext (contexto del proyecto objetivo)
├── ApplicationKnowledgeBase (base de conocimiento de la aplicación)
├── DomainBusinessLogic (lógica de negocio del dominio)
├── UIComponentMapping (mapeo de componentes de interfaz)
├── WorkflowPatterns (patrones de flujo de la aplicación)
└── TestContextEnhancer (enriquecedor de contexto de pruebas)
```

## 📂 Archivo Principal: `src/services/target_project_context_rag_service.py`

## 🔧 Implementación Detallada

### Estructura Principal

```python
# src/services/target_project_context_rag_service.py

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import json
import logging
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TargetProjectContext:
    project_name: str
    project_description: str
    domain: str  # e-commerce, banking, social, etc.
    key_features: List[str]
    user_personas: List[Dict[str, str]]
    business_flows: List[Dict[str, str]]
    ui_components: Dict[str, List[str]]
    authentication_type: str
    common_workflows: List[Dict[str, str]]
    known_issues: List[str]
    test_data_patterns: Dict[str, Any]

@dataclass
class TaskContext:
    original_task: str
    task_type: str
    relevant_features: List[str]
    affected_components: List[str]
    business_flow: str
    user_persona: str
    expected_workflow: str
    success_indicators: List[str]
    potential_challenges: List[str]

@dataclass
class EnhancedTaskContext:
    enhanced_task_description: str
    target_project_context: TargetProjectContext
    task_context: TaskContext
    contextual_hints: List[str]
    domain_expertise: List[str]
    workflow_guidance: List[str]
    test_data_suggestions: Dict[str, Any]

class TargetProjectContextRAGService:
    """
    Sistema RAG especializado en proporcionar contexto del proyecto/aplicación
    que se está testeando para mejorar la comprensión del agente.
    """
    
    def __init__(self, project_context_path: str = "projects/"):
        self.project_context_path = Path(project_context_path)
        self.current_project_context = None
        self.domain_knowledge = self._load_domain_knowledge()
        
        logger.info("TargetProjectContextRAGService initialized")

    def load_project_context(self, project_id: str) -> TargetProjectContext:
        """Cargar contexto del proyecto específico que se está testeando."""
        try:
            # Buscar archivos de contexto del proyecto
            project_file = self.project_context_path / f"{project_id}.json"
            
            if project_file.exists():
                context_data = json.loads(project_file.read_text())
                return TargetProjectContext(**context_data)
            else:
                # Intentar inferir contexto desde la URL o nombre del proyecto
                return self._infer_project_context(project_id)
                
        except Exception as e:
            logger.error(f"Error loading project context for {project_id}: {e}")
            return self._get_default_project_context(project_id)

    def _infer_project_context(self, project_identifier: str) -> TargetProjectContext:
        """Inferir contexto del proyecto basado en identificador/URL."""
        project_lower = project_identifier.lower()
        
        # Inferir dominio basado en patrones comunes
        if any(term in project_lower for term in ["shop", "store", "ecommerce", "cart", "product"]):
            return self._create_ecommerce_context(project_identifier)
        elif any(term in project_lower for term in ["bank", "finance", "payment", "transaction"]):
            return self._create_banking_context(project_identifier)
        elif any(term in project_lower for term in ["social", "chat", "message", "post", "feed"]):
            return self._create_social_context(project_identifier)
        elif any(term in project_lower for term in ["admin", "dashboard", "management", "cms"]):
            return self._create_admin_context(project_identifier)
        else:
            return self._create_generic_web_context(project_identifier)

    def _create_ecommerce_context(self, project_id: str) -> TargetProjectContext:
        """Crear contexto para aplicaciones de e-commerce."""
        return TargetProjectContext(
            project_name=project_id,
            project_description="Aplicación de comercio electrónico",
            domain="e-commerce",
            key_features=[
                "Catálogo de productos",
                "Carrito de compras", 
                "Proceso de checkout",
                "Gestión de usuarios",
                "Sistema de pagos",
                "Búsqueda de productos",
                "Reseñas y calificaciones"
            ],
            user_personas=[
                {"role": "customer", "description": "Cliente que realiza compras"},
                {"role": "admin", "description": "Administrador que gestiona productos"},
                {"role": "guest", "description": "Visitante sin cuenta registrada"}
            ],
            business_flows=[
                {"name": "purchase_flow", "steps": "Browse → Add to Cart → Checkout → Payment → Confirmation"},
                {"name": "registration_flow", "steps": "Sign up → Email verification → Profile setup"},
                {"name": "search_flow", "steps": "Search → Filter → View results → Product details"}
            ],
            ui_components={
                "navigation": ["header", "menu", "breadcrumbs", "footer"],
                "product": ["product_card", "product_details", "image_gallery", "reviews"],
                "cart": ["cart_icon", "cart_sidebar", "cart_page", "checkout_form"],
                "user": ["login_form", "register_form", "profile_page", "order_history"]
            },
            authentication_type="email_password",
            common_workflows=[
                {"name": "add_to_cart", "pattern": "product_page → add_button → cart_confirmation"},
                {"name": "checkout", "pattern": "cart → checkout → payment → confirmation"},
                {"name": "search", "pattern": "search_box → results → filters → selection"}
            ],
            known_issues=[
                "cart_synchronization_delays",
                "payment_gateway_timeouts", 
                "image_loading_issues",
                "mobile_responsiveness"
            ],
            test_data_patterns={
                "products": {"id_pattern": "PROD-{number}", "price_range": [10, 1000]},
                "users": {"email_pattern": "test{number}@example.com", "password": "Test123!"},
                "orders": {"id_pattern": "ORD-{timestamp}", "status": ["pending", "confirmed", "shipped"]}
            }
        )

    def _create_banking_context(self, project_id: str) -> TargetProjectContext:
        """Crear contexto para aplicaciones bancarias/financieras."""
        return TargetProjectContext(
            project_name=project_id,
            project_description="Aplicación bancaria/financiera",
            domain="banking",
            key_features=[
                "Consulta de saldos",
                "Transferencias",
                "Historial de transacciones",
                "Pagos de servicios",
                "Inversiones",
                "Autenticación segura",
                "Alertas y notificaciones"
            ],
            user_personas=[
                {"role": "account_holder", "description": "Cliente con cuenta bancaria"},
                {"role": "business_user", "description": "Usuario empresarial"},
                {"role": "admin", "description": "Administrador bancario"}
            ],
            business_flows=[
                {"name": "transfer_flow", "steps": "Login → Select accounts → Enter amount → Confirm → Execute"},
                {"name": "payment_flow", "steps": "Login → Pay bills → Select service → Amount → Confirm"},
                {"name": "balance_inquiry", "steps": "Login → Dashboard → Select account → View details"}
            ],
            ui_components={
                "security": ["login_form", "2fa_input", "security_questions", "logout"],
                "dashboard": ["account_summary", "balance_cards", "recent_transactions"],
                "transfers": ["transfer_form", "beneficiary_list", "confirmation_dialog"],
                "transactions": ["transaction_list", "filters", "export_options"]
            },
            authentication_type="multi_factor",
            common_workflows=[
                {"name": "secure_login", "pattern": "credentials → 2FA → security_questions → dashboard"},
                {"name": "money_transfer", "pattern": "dashboard → transfers → form → confirmation → receipt"},
                {"name": "balance_check", "pattern": "login → dashboard → account_details → balance"}
            ],
            known_issues=[
                "session_timeout_aggressive",
                "2fa_sms_delays",
                "transaction_processing_delays",
                "browser_compatibility_strict"
            ],
            test_data_patterns={
                "accounts": {"number_pattern": "****-****-****-{last4}", "types": ["checking", "savings"]},
                "users": {"id_pattern": "USR{8digits}", "roles": ["customer", "business", "admin"]},
                "amounts": {"min": 1, "max": 10000, "currency": "USD"}
            }
        )

    def _create_social_context(self, project_id: str) -> TargetProjectContext:
        """Crear contexto para aplicaciones sociales."""
        return TargetProjectContext(
            project_name=project_id,
            project_description="Aplicación de red social",
            domain="social",
            key_features=[
                "Feed de noticias",
                "Publicaciones y posts",
                "Sistema de likes/reacciones",
                "Comentarios",
                "Mensajería privada",
                "Perfiles de usuario",
                "Seguimiento de usuarios"
            ],
            user_personas=[
                {"role": "regular_user", "description": "Usuario regular que publica y consume contenido"},
                {"role": "influencer", "description": "Usuario con muchos seguidores"},
                {"role": "moderator", "description": "Moderador de contenido"}
            ],
            business_flows=[
                {"name": "post_creation", "steps": "Create → Add content → Set privacy → Publish"},
                {"name": "social_interaction", "steps": "Browse feed → Like/Comment → Share → Engage"},
                {"name": "messaging", "steps": "Select user → Compose → Send → Conversation"}
            ],
            ui_components={
                "feed": ["post_list", "infinite_scroll", "reaction_buttons", "share_options"],
                "profile": ["profile_header", "bio_section", "posts_grid", "followers_list"],
                "messaging": ["chat_list", "conversation_view", "message_input", "media_attachment"],
                "creation": ["post_composer", "media_upload", "privacy_settings", "publish_button"]
            },
            authentication_type="social_login",
            common_workflows=[
                {"name": "create_post", "pattern": "compose → media → text → privacy → publish"},
                {"name": "engage_content", "pattern": "browse → like → comment → share"},
                {"name": "follow_user", "pattern": "discover → profile → follow → notification"}
            ],
            known_issues=[
                "infinite_scroll_performance",
                "media_upload_size_limits",
                "real_time_updates_delays",
                "mobile_gesture_conflicts"
            ],
            test_data_patterns={
                "posts": {"id_pattern": "POST_{timestamp}", "types": ["text", "image", "video"]},
                "users": {"username_pattern": "user{number}", "handle_pattern": "@user{number}"},
                "interactions": {"types": ["like", "love", "comment", "share"], "counts": [0, 10000]}
            }
        )

    def _create_admin_context(self, project_id: str) -> TargetProjectContext:
        """Crear contexto para paneles administrativos."""
        return TargetProjectContext(
            project_name=project_id,
            project_description="Panel de administración/CMS",
            domain="admin",
            key_features=[
                "Dashboard con métricas",
                "Gestión de usuarios",
                "CRUD de contenido",
                "Configuración del sistema",
                "Reportes y analytics",
                "Control de permisos",
                "Auditoría de acciones"
            ],
            user_personas=[
                {"role": "super_admin", "description": "Administrador con acceso completo"},
                {"role": "content_manager", "description": "Gestor de contenido"},
                {"role": "analyst", "description": "Analista con acceso a reportes"}
            ],
            business_flows=[
                {"name": "content_management", "steps": "Login → Content section → Create/Edit → Save → Publish"},
                {"name": "user_management", "steps": "Users section → Add/Edit user → Assign roles → Save"},
                {"name": "analytics_review", "steps": "Dashboard → Reports → Filter → Export → Analysis"}
            ],
            ui_components={
                "navigation": ["sidebar", "breadcrumbs", "top_nav", "user_menu"],
                "dashboard": ["metrics_cards", "charts", "recent_activity", "quick_actions"],
                "forms": ["input_fields", "dropdowns", "date_pickers", "file_uploads"],
                "tables": ["data_tables", "pagination", "sorting", "filters", "bulk_actions"]
            },
            authentication_type="role_based",
            common_workflows=[
                {"name": "crud_operations", "pattern": "list → create/edit → validate → save → confirm"},
                {"name": "bulk_operations", "pattern": "select_multiple → choose_action → confirm → execute"},
                {"name": "report_generation", "pattern": "reports → filters → generate → export → download"}
            ],
            known_issues=[
                "large_dataset_performance",
                "complex_form_validation",
                "file_upload_timeouts",
                "permission_edge_cases"
            ],
            test_data_patterns={
                "entities": {"id_pattern": "ID_{increment}", "status": ["active", "inactive", "pending"]},
                "admins": {"email_pattern": "admin{number}@company.com", "roles": ["admin", "editor", "viewer"]},
                "content": {"slug_pattern": "content-{slug}", "types": ["page", "post", "media"]}
            }
        )

    def _create_generic_web_context(self, project_id: str) -> TargetProjectContext:
        """Crear contexto genérico para aplicaciones web."""
        return TargetProjectContext(
            project_name=project_id,
            project_description="Aplicación web general",
            domain="general",
            key_features=[
                "Navegación principal",
                "Formularios de contacto",
                "Contenido dinámico",
                "Búsqueda básica",
                "Responsive design"
            ],
            user_personas=[
                {"role": "visitor", "description": "Visitante general del sitio"},
                {"role": "registered_user", "description": "Usuario registrado"}
            ],
            business_flows=[
                {"name": "navigation", "steps": "Landing → Browse → Interact → Convert"},
                {"name": "contact", "steps": "Contact page → Fill form → Submit → Confirmation"}
            ],
            ui_components={
                "layout": ["header", "navigation", "main_content", "footer"],
                "forms": ["contact_form", "newsletter_signup", "search_box"],
                "content": ["text_blocks", "images", "videos", "links"]
            },
            authentication_type="optional",
            common_workflows=[
                {"name": "browse_content", "pattern": "landing → navigation → content → interaction"},
                {"name": "form_submission", "pattern": "form → validation → submission → confirmation"}
            ],
            known_issues=[
                "cross_browser_compatibility",
                "mobile_responsiveness",
                "form_validation_issues"
            ],
            test_data_patterns={
                "forms": {"email_pattern": "test{number}@domain.com", "phone": "+**********"},
                "content": {"title_max_length": 100, "description_max_length": 500}
            }
        )

    def _load_project_context(self) -> ProjectContext:
        """Cargar contexto del proyecto desde Memory Bank."""
        try:
            # Leer archivos de Memory Bank
            project_brief = self._read_memory_file("project-brief.md")
            product_context = self._read_memory_file("productContext.md")
            tech_context = self._read_memory_file("techContext.md")
            system_patterns = self._read_memory_file("systemPatterns.md")
            
            return ProjectContext(
                project_name="AgentQA",
                project_purpose=self._extract_purpose(project_brief, product_context),
                key_features=self._extract_features(product_context, system_patterns),
                domain_knowledge=self._extract_domain_knowledge(product_context),
                business_context=self._extract_business_context(project_brief),
                technical_stack=self._extract_tech_stack(tech_context),
                common_workflows=self._extract_workflows(system_patterns),
                success_criteria=self._extract_success_criteria(project_brief)
            )
            
        except Exception as e:
            logger.error(f"Error loading project context: {e}")
            return self._get_default_project_context()

    def _read_memory_file(self, filename: str) -> str:
        """Leer archivo del Memory Bank."""
        file_path = self.memory_bank_path / filename
        if file_path.exists():
            return file_path.read_text(encoding='utf-8')
        return ""

    def _extract_purpose(self, project_brief: str, product_context: str) -> str:
        """Extraer propósito del proyecto."""
        # Buscar secciones de propósito en los archivos
        purpose_keywords = ["objetivo", "purpose", "goal", "misión", "democratizar"]
        
        for line in project_brief.split('\n'):
            if any(keyword in line.lower() for keyword in purpose_keywords):
                return line.strip()
        
        return "Democratizar la automatización de pruebas mediante IA, reduciendo el tiempo de desarrollo de pruebas."

    def _extract_features(self, product_context: str, system_patterns: str) -> List[str]:
        """Extraer características clave del proyecto."""
        return [
            "Generación automática de código de pruebas desde historias de usuario",
            "Soporte multi-framework (Selenium, Playwright, Cypress, Robot Framework, Cucumber)",
            "Ejecución en navegadores reales con browser-use",
            "Interfaz web moderna (Next.js) y CLI para desarrolladores",
            "Múltiples proveedores de IA (Gemini, OpenAI, Claude, Groq)",
            "Sistema de gestión de proyectos con persistencia JSON",
            "Captura automática de screenshots y métricas",
            "Soporte multilenguaje (ES/EN) con traducción inteligente"
        ]

    def _extract_domain_knowledge(self, product_context: str) -> Dict[str, str]:
        """Extraer conocimiento del dominio."""
        return {
            "qa_automation": "Automatización de pruebas de calidad con IA",
            "browser_testing": "Pruebas automatizadas en navegadores reales",
            "gherkin": "Lenguaje de especificación de comportamiento",
            "user_stories": "Historias de usuario para definir requisitos",
            "test_frameworks": "Frameworks de testing como Selenium, Playwright",
            "ci_cd": "Integración y despliegue continuo",
            "web_scraping": "Extracción automatizada de datos web",
            "llm_integration": "Integración con modelos de lenguaje grande"
        }

    def _extract_business_context(self, project_brief: str) -> str:
        """Extraer contexto de negocio."""
        return """
        AgentQA resuelve el problema de la automatización de pruebas costosa y técnicamente compleja.
        Permite que usuarios no técnicos generen pruebas automatizadas usando lenguaje natural.
        Reduce el tiempo de desarrollo de pruebas de días/semanas a minutos.
        Proporciona trazabilidad completa desde requisitos hasta ejecución.
        """

    def _extract_tech_stack(self, tech_context: str) -> List[str]:
        """Extraer stack tecnológico."""
        return [
            "FastAPI (Backend)",
            "Next.js (Frontend)",
            "browser-use v0.5.0 (Automatización)",
            "LangChain (IA/LLM)",
            "MongoDB (Configuración)",
            "Redis (Cache)",
            "Celery (Jobs en background)",
            "Python 3.11+",
            "TypeScript/React"
        ]

    def _extract_workflows(self, system_patterns: str) -> List[Dict[str, str]]:
        """Extraer flujos de trabajo comunes."""
        return [
            {
                "name": "Pipeline Completo",
                "flow": "User Story → Enhanced Story → Manual Tests → Gherkin → Code → Execution"
            },
            {
                "name": "Smoke Test Pipeline",
                "flow": "Descripción directa → Ejecución inmediata"
            },
            {
                "name": "Browser Automation",
                "flow": "Configuración → Sesión → Agente → Ejecución → Resultados"
            }
        ]

    def _extract_success_criteria(self, project_brief: str) -> List[str]:
        """Extraer criterios de éxito."""
        return [
            "Reducción del 80%+ en tiempo de creación de pruebas",
            "Capacidad para usuarios no técnicos de generar pruebas",
            "Trazabilidad completa desde requisito hasta resultado",
            "Escalabilidad para múltiples proyectos y equipos",
            "Ejecución confiable en navegadores reales"
        ]

    def _get_default_project_context(self) -> ProjectContext:
        """Contexto por defecto si falla la carga."""
        return ProjectContext(
            project_name="AgentQA",
            project_purpose="Automatización de pruebas con IA",
            key_features=["Generación automática de pruebas", "Ejecución en navegadores"],
            domain_knowledge={"testing": "Automatización de pruebas"},
            business_context="Democratizar testing automatizado",
            technical_stack=["FastAPI", "browser-use", "LangChain"],
            common_workflows=[],
            success_criteria=["Reducir tiempo de pruebas"]
        )

    async def enhance_task_with_context(
        self, 
        original_task: str,
        project_id: str,
        task_type: str = "general"
    ) -> EnhancedTaskContext:
        """
        Enriquecer una tarea con contexto del proyecto bajo prueba.
        
        Args:
            original_task: Tarea original del usuario
            project_id: Identificador del proyecto que se está testeando
            task_type: Tipo de tarea (automation, testing, navigation, etc.)
            
        Returns:
            EnhancedTaskContext con información contextual rica del proyecto objetivo
        """
        try:
            # Cargar contexto del proyecto específico
            if not self.current_project_context or self.current_project_context.project_name != project_id:
                self.current_project_context = self.load_project_context(project_id)
            
            # Analizar la tarea para identificar características relevantes
            task_context = self._analyze_task(original_task, task_type)
            
            # Generar hints contextuales específicos del proyecto
            contextual_hints = self._generate_contextual_hints(task_context, self.current_project_context)
            
            # Proporcionar expertise del dominio específico
            domain_expertise = self._provide_domain_expertise(task_context, self.current_project_context)
            
            # Guía de workflow específica del proyecto
            workflow_guidance = self._suggest_workflow(task_context, self.current_project_context)
            
            # Sugerencias de datos de prueba
            test_data_suggestions = self._suggest_test_data(task_context, self.current_project_context)
            
            # Crear descripción enriquecida de la tarea
            enhanced_description = self._create_enhanced_description(
                original_task, 
                task_context,
                self.current_project_context,
                contextual_hints
            )
            
            return EnhancedTaskContext(
                enhanced_task_description=enhanced_description,
                target_project_context=self.current_project_context,
                task_context=task_context,
                contextual_hints=contextual_hints,
                domain_expertise=domain_expertise,
                workflow_guidance=workflow_guidance,
                test_data_suggestions=test_data_suggestions
            )
            
        except Exception as e:
            logger.error(f"Error enhancing task context: {e}")
            return self._create_minimal_context(original_task, task_type, project_id)

    def _analyze_task(self, task: str, task_type: str) -> TaskContext:
        """Analizar tarea para extraer contexto relevante."""
        task_lower = task.lower()
        
        # Identificar características relevantes
        relevant_features = []
        if "login" in task_lower or "autenticar" in task_lower:
            relevant_features.extend(["authentication", "browser_automation", "form_handling"])
        if "navigate" in task_lower or "navegar" in task_lower:
            relevant_features.extend(["navigation", "url_handling", "page_loading"])
        if "test" in task_lower or "prueba" in task_lower:
            relevant_features.extend(["test_generation", "test_execution", "validation"])
        if "extract" in task_lower or "extraer" in task_lower:
            relevant_features.extend(["data_extraction", "web_scraping", "element_selection"])
        
        # Hints del dominio
        domain_hints = []
        if any(feature in task_lower for feature in ["selenium", "playwright", "cypress"]):
            domain_hints.append("framework_specific_testing")
        if "gherkin" in task_lower or "bdd" in task_lower:
            domain_hints.append("behavior_driven_development")
        if "api" in task_lower:
            domain_hints.append("api_testing")
        
        # Workflow esperado
        expected_workflow = self._determine_expected_workflow(task_lower, relevant_features)
        
        # Indicadores de éxito
        success_indicators = self._determine_success_indicators(task_lower, relevant_features)
        
        # Desafíos potenciales
        potential_challenges = self._identify_potential_challenges(task_lower, relevant_features)
        
        return TaskContext(
            original_task=task,
            task_type=task_type,
            relevant_features=relevant_features,
            domain_hints=domain_hints,
            expected_workflow=expected_workflow,
            success_indicators=success_indicators,
            potential_challenges=potential_challenges
        )

    def _generate_contextual_hints(self, task_context: TaskContext) -> List[str]:
        """Generar hints contextuales para la tarea."""
        hints = []
        
        # Hints basados en características
        if "authentication" in task_context.relevant_features:
            hints.append("En AgentQA, las tareas de autenticación suelen requerir configuración de dominios permitidos")
            hints.append("Considera usar headless=false para debugging de login si hay problemas")
        
        if "navigation" in task_context.relevant_features:
            hints.append("AgentQA optimiza navegación con initial_actions para URLs conocidas")
            hints.append("El sistema preserva URLs exactas del escenario sin modificaciones")
        
        if "test_generation" in task_context.relevant_features:
            hints.append("AgentQA genera código en múltiples frameworks: Selenium, Playwright, Cypress")
            hints.append("El pipeline completo va de User Story → Gherkin → Code → Execution")
        
        if "data_extraction" in task_context.relevant_features:
            hints.append("AgentQA usa browser-use con vision para identificar elementos precisos")
            hints.append("Considera viewport_expansion para elementos fuera del viewport inicial")
        
        return hints

    def _provide_domain_expertise(self, task_context: TaskContext) -> List[str]:
        """Proporcionar expertise del dominio."""
        expertise = []
        
        # Expertise basada en hints del dominio
        if "framework_specific_testing" in task_context.domain_hints:
            expertise.append("Cada framework tiene patrones específicos de localizadores y esperas")
            expertise.append("Selenium: WebDriverWait, Playwright: auto-wait, Cypress: built-in retry")
        
        if "behavior_driven_development" in task_context.domain_hints:
            expertise.append("Gherkin sigue patrón Given-When-Then para claridad")
            expertise.append("AgentQA traduce Gherkin a código ejecutable automáticamente")
        
        if "api_testing" in task_context.domain_hints:
            expertise.append("API testing complementa UI testing para cobertura completa")
            expertise.append("Considera validar tanto respuesta API como cambios en UI")
        
        # Expertise general de AgentQA
        expertise.append("AgentQA prioriza pruebas confiables sobre velocidad pura")
        expertise.append("El sistema mantiene trazabilidad desde requisito hasta ejecución")
        
        return expertise

    def _suggest_workflow(self, task_context: TaskContext) -> List[str]:
        """Sugerir workflow apropiado."""
        workflow = []
        
        if task_context.task_type == "automation":
            workflow.extend([
                "1. Configurar browser profile con settings apropiados",
                "2. Establecer initial_actions si hay URL conocida",
                "3. Ejecutar con vision=true para mejor precisión",
                "4. Capturar screenshots para debugging",
                "5. Validar resultados con criterios específicos"
            ])
        elif task_context.task_type == "testing":
            workflow.extend([
                "1. Definir historia de usuario clara",
                "2. Generar casos de prueba manuales",
                "3. Convertir a Gherkin BDD",
                "4. Generar código automatizado",
                "5. Ejecutar y validar resultados"
            ])
        else:
            workflow.extend([
                "1. Analizar requisitos de la tarea",
                "2. Configurar entorno apropiado",
                "3. Ejecutar con monitoreo",
                "4. Validar resultados",
                "5. Documentar outcomes"
            ])
        
        return workflow

    def _determine_expected_workflow(self, task_lower: str, features: List[str]) -> str:
        """Determinar workflow esperado."""
        if "test" in task_lower:
            return "test_generation_pipeline"
        elif "automation" in task_lower or "browser" in task_lower:
            return "browser_automation_pipeline"
        elif "smoke" in task_lower:
            return "smoke_test_pipeline"
        else:
            return "general_pipeline"

    def _determine_success_indicators(self, task_lower: str, features: List[str]) -> List[str]:
        """Determinar indicadores de éxito."""
        indicators = ["task_completion", "no_critical_errors"]
        
        if "authentication" in features:
            indicators.append("successful_login")
        if "navigation" in features:
            indicators.append("correct_page_reached")
        if "data_extraction" in features:
            indicators.append("data_successfully_extracted")
        if "test_generation" in features:
            indicators.append("executable_test_code_generated")
        
        return indicators

    def _identify_potential_challenges(self, task_lower: str, features: List[str]) -> List[str]:
        """Identificar desafíos potenciales."""
        challenges = []
        
        if "authentication" in features:
            challenges.extend(["captcha_handling", "2fa_requirements", "session_timeout"])
        if "navigation" in features:
            challenges.extend(["slow_page_loading", "dynamic_content", "redirects"])
        if "data_extraction" in features:
            challenges.extend(["dynamic_elements", "lazy_loading", "element_visibility"])
        
        # Desafíos comunes de AgentQA
        challenges.extend(["browser_profile_conflicts", "llm_rate_limits", "network_latency"])
        
        return list(set(challenges))  # Remove duplicates

    def _create_enhanced_description(
        self, 
        original_task: str, 
        task_context: TaskContext,
        hints: List[str]
    ) -> str:
        """Crear descripción enriquecida de la tarea."""
        enhanced = f"""
**TAREA ORIGINAL:** {original_task}

**CONTEXTO DEL PROYECTO:** AgentQA - {self.project_context.project_purpose}

**CARACTERÍSTICAS RELEVANTES:** {', '.join(task_context.relevant_features)}

**WORKFLOW ESPERADO:** {task_context.expected_workflow}

**HINTS CONTEXTUALES:**
{chr(10).join(f'• {hint}' for hint in hints[:3])}

**CRITERIOS DE ÉXITO:** {', '.join(task_context.success_indicators)}

**CONSIDERACIONES ESPECIALES:**
• Este proyecto democratiza la automatización de pruebas con IA
• Prioriza la trazabilidad completa desde requisitos hasta ejecución
• Usa browser-use v0.5.0 con capacidades avanzadas de vision
• Soporta múltiples frameworks de testing y proveedores de LLM
        """
        
        return enhanced.strip()

    def _create_minimal_context(self, task: str, task_type: str) -> EnhancedTaskContext:
        """Crear contexto mínimo en caso de error."""
        return EnhancedTaskContext(
            enhanced_task_description=task,
            project_context=self.project_context,
            task_context=TaskContext(
                original_task=task,
                task_type=task_type,
                relevant_features=[],
                domain_hints=[],
                expected_workflow="general",
                success_indicators=["completion"],
                potential_challenges=[]
            ),
            contextual_hints=[],
            domain_expertise=[],
            workflow_guidance=[]
        )

    def get_project_summary(self) -> Dict[str, Any]:
        """Obtener resumen del proyecto para contexto rápido."""
        return {
            "name": self.project_context.project_name,
            "purpose": self.project_context.project_purpose,
            "key_features": self.project_context.key_features[:5],  # Top 5
            "tech_stack": self.project_context.technical_stack[:5],  # Top 5
            "domain": "QA Automation with AI",
            "success_metrics": self.project_context.success_criteria[:3]  # Top 3
        }
```

## 🔗 Integración con Planner Service

### Modificaciones en `PlannerService`

```python
# En src/services/planner_service.py

from .project_context_rag_service import ProjectContextRAGService

class PlannerService:
    def __init__(
        self,
        original_task: str,
        max_steps_before_check: int = 3,
        completion_threshold: float = 0.8,
        failure_threshold: int = 3,
        rag_service: Optional[Any] = None,
        project_context_rag: bool = True  # NUEVO
    ):
        # ...existing code...
        
        # Añadir servicio de contexto del proyecto
        if project_context_rag:
            self.project_context_rag = ProjectContextRAGService()
            # Enriquecer la tarea original con contexto
            asyncio.create_task(self._enrich_original_task())
        else:
            self.project_context_rag = None
    
    async def _enrich_original_task(self):
        """Enriquecer tarea original con contexto del proyecto."""
        if self.project_context_rag:
            enhanced_context = await self.project_context_rag.enhance_task_with_context(
                self.original_task,
                task_type="automation"
            )
            # Usar contexto enriquecido para mejores decisiones
            self.enhanced_task_context = enhanced_context
```

## 📊 Casos de Uso

### 1. **Tarea Simple con Contexto Rico**
```python
# Input: "Login to the website"
# Output: Enhanced task with AgentQA context about authentication patterns,
#         browser configuration recommendations, and domain-specific guidance
```

### 2. **Generación de Pruebas con Conocimiento del Dominio**
```python
# Input: "Create test for user registration"
# Output: Context about AgentQA's test generation pipeline,
#         multi-framework support, and Gherkin conversion
```

### 3. **Automatización con Awareness del Proyecto**
```python
# Input: "Extract data from product page"
# Output: Context about browser-use capabilities, vision features,
#         and AgentQA's approach to web scraping
```

## 🎯 Beneficios del Sistema

1. **Contexto Rico**: El agente entiende el propósito y capacidades de AgentQA
2. **Decisiones Informadas**: Mejores decisiones basadas en conocimiento del dominio
3. **Workflow Guidance**: Sugerencias de mejores prácticas específicas del proyecto
4. **Expertise del Dominio**: Conocimiento especializado en QA automation
5. **Awareness Tecnológico**: Comprensión del stack tecnológico y limitaciones

## 🔄 Flujo de Integración

```
Usuario envía tarea
        ↓
ProjectContextRAGService enriquece con contexto
        ↓
PlannerService recibe tarea enriquecida + contexto del proyecto
        ↓
Agente ejecuta con awareness completo del proyecto
        ↓
Decisiones más inteligentes basadas en conocimiento del dominio
```

## 📝 Archivos de Configuración

### `config/project_context.json`
```json
{
  "domain_expertise": {
    "qa_automation": "Advanced knowledge of test automation patterns",
    "browser_testing": "Expert in cross-browser testing strategies",
    "ai_integration": "Specialized in LLM-powered testing workflows"
  },
  "workflow_templates": {
    "smoke_test": "Quick validation workflow",
    "full_test": "Comprehensive testing pipeline",
    "automation": "Browser automation workflow"
  },
  "success_patterns": [
    "reliable_element_detection",
    "robust_wait_strategies", 
    "comprehensive_error_handling"
  ]
}
```

## 🚀 Resultado Esperado

- ✅ Agente con comprensión profunda del proyecto AgentQA
- ✅ Contexto rico para cada tarea específica
- ✅ Mejores decisiones basadas en conocimiento del dominio
- ✅ Guidance de workflows específicos del proyecto
- ✅ Awareness de capacidades y limitaciones técnicas
- ✅ Integración seamless con el sistema de planning existente
