# 3. Implementar Sistema RAG

## 🎯 Objetivo
Crear un sistema RAG (Retrieval-Augmented Generation) que proporcione contexto histórico y conocimiento sobre ejecuciones previas para mejorar las decisiones del planner.

## 🧠 Arquitectura RAG

```
RAG System
├── VectorStore (embeddings de ejecuciones)
├── MemoryManager (gestión de memoria a corto/largo plazo)
├── PatternRecognizer (reconoce patrones de éxito/fallo)
├── ContextRetriever (recupera contexto relevante)
└── KnowledgeBase (base de conocimiento de tareas)
```

## 📂 Archivo Principal: `src/services/rag_service.py`

## 🔧 Implementación Detallada

### Estructura Principal

```python
# src/services/rag_service.py

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
import json
from datetime import datetime, timedelta
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ExecutionMemory:
    execution_id: str
    original_task: str
    task_type: str
    steps_taken: List[Dict[str, Any]]
    final_result: str  # success/failure/timeout
    completion_percentage: float
    total_steps: int
    duration_seconds: float
    issues_encountered: List[str]
    successful_patterns: List[str]
    embedding: Optional[List[float]] = None
    timestamp: datetime = None

@dataclass
class TaskPattern:
    pattern_id: str
    task_type: str
    common_steps: List[str]
    success_indicators: List[str]
    failure_indicators: List[str]
    average_steps: int
    success_rate: float
    examples: List[str]

class RAGService:
    def __init__(
        self,
        storage_path: str = "data/rag_memory",
        embedding_model: str = "all-MiniLM-L6-v2",
        max_memories: int = 1000
    ):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.embedding_model = embedding_model
        self.max_memories = max_memories
        
        # Components
        self.vector_store = VectorStore(self.storage_path / "vectors")
        self.memory_manager = MemoryManager(self.storage_path / "memories")
        self.pattern_recognizer = PatternRecognizer()
        self.context_retriever = ContextRetriever(self.vector_store)
        self.knowledge_base = KnowledgeBase(self.storage_path / "knowledge")
        
        # Load existing data
        self._load_existing_data()
        
        logger.info(f"🧠 RAG: Initialized with {len(self.memories)} memories")
```

### VectorStore - Almacenamiento de Embeddings

```python
class VectorStore:
    def __init__(self, storage_path: Path):
        self.storage_path = storage_path
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.embeddings_file = self.storage_path / "embeddings.npy"
        self.metadata_file = self.storage_path / "metadata.json"
        
        # Try to use sentence-transformers for embeddings
        try:
            from sentence_transformers import SentenceTransformer
            self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
            self.embedding_available = True
        except ImportError:
            logger.warning("🧠 RAG: sentence-transformers not available, using simple text similarity")
            self.encoder = None
            self.embedding_available = False
        
        self.embeddings = []
        self.metadata = []
        self._load_vectors()
    
    def _load_vectors(self):
        """Carga vectores existentes"""
        if self.embeddings_file.exists() and self.metadata_file.exists():
            try:
                self.embeddings = np.load(self.embeddings_file).tolist()
                with open(self.metadata_file, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"🧠 RAG: Loaded {len(self.embeddings)} vectors")
            except Exception as e:
                logger.error(f"🧠 RAG: Failed to load vectors: {e}")
                self.embeddings = []
                self.metadata = []
    
    def add_embedding(self, text: str, metadata: Dict[str, Any]) -> str:
        """Añade un nuevo embedding"""
        if self.embedding_available:
            embedding = self.encoder.encode([text])[0].tolist()
        else:
            # Fallback: simple text hash as pseudo-embedding
            embedding = [hash(text[i:i+10]) % 1000 for i in range(0, min(len(text), 100), 10)]
        
        embedding_id = f"emb_{len(self.embeddings)}"
        self.embeddings.append(embedding)
        self.metadata.append({
            'id': embedding_id,
            'text': text,
            'metadata': metadata,
            'timestamp': datetime.now().isoformat()
        })
        
        self._save_vectors()
        return embedding_id
    
    def search_similar(self, query_text: str, top_k: int = 5) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Busca embeddings similares"""
        if not self.embeddings:
            return []
        
        if self.embedding_available:
            query_embedding = self.encoder.encode([query_text])[0]
            
            # Calcular similitud coseno
            similarities = []
            for i, stored_embedding in enumerate(self.embeddings):
                similarity = np.dot(query_embedding, stored_embedding) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(stored_embedding)
                )
                similarities.append((i, similarity))
        else:
            # Fallback: simple text similarity
            similarities = []
            query_words = set(query_text.lower().split())
            for i, metadata in enumerate(self.metadata):
                stored_words = set(metadata['text'].lower().split())
                similarity = len(query_words & stored_words) / len(query_words | stored_words)
                similarities.append((i, similarity))
        
        # Ordenar por similitud
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        results = []
        for i, similarity in similarities[:top_k]:
            if similarity > 0.1:  # Threshold mínimo
                results.append((
                    self.metadata[i]['id'],
                    similarity,
                    self.metadata[i]
                ))
        
        return results
    
    def _save_vectors(self):
        """Guarda vectores al disco"""
        try:
            if self.embeddings:
                np.save(self.embeddings_file, np.array(self.embeddings))
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"🧠 RAG: Failed to save vectors: {e}")
```

### MemoryManager - Gestión de Memoria

```python
class MemoryManager:
    def __init__(self, storage_path: Path):
        self.storage_path = storage_path
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.memories_file = self.storage_path / "execution_memories.json"
        self.memories: List[ExecutionMemory] = []
        self._load_memories()
    
    def _load_memories(self):
        """Carga memorias existentes"""
        if self.memories_file.exists():
            try:
                with open(self.memories_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        memory = ExecutionMemory(**item)
                        if memory.timestamp:
                            memory.timestamp = datetime.fromisoformat(memory.timestamp)
                        self.memories.append(memory)
                logger.info(f"🧠 RAG: Loaded {len(self.memories)} memories")
            except Exception as e:
                logger.error(f"🧠 RAG: Failed to load memories: {e}")
    
    def add_execution_memory(self, memory: ExecutionMemory):
        """Añade una nueva memoria de ejecución"""
        memory.timestamp = datetime.now()
        self.memories.append(memory)
        
        # Mantener límite máximo
        if len(self.memories) > 1000:  # Max memories
            # Eliminar las más antiguas, pero mantener algunas exitosas
            successful_memories = [m for m in self.memories if m.final_result == 'success']
            failed_memories = [m for m in self.memories if m.final_result != 'success']
            
            # Mantener últimas 800 + 200 exitosas más relevantes
            self.memories = (
                sorted(failed_memories, key=lambda x: x.timestamp)[-800:] +
                sorted(successful_memories, key=lambda x: x.completion_percentage, reverse=True)[:200]
            )
        
        self._save_memories()
        logger.info(f"🧠 RAG: Added memory for {memory.execution_id}")
    
    def get_similar_executions(
        self, 
        task: str, 
        task_type: str, 
        limit: int = 5
    ) -> List[ExecutionMemory]:
        """Obtiene ejecuciones similares"""
        
        # Filtrar por tipo de tarea
        candidate_memories = [m for m in self.memories if m.task_type == task_type]
        
        # Si no hay suficientes del mismo tipo, incluir otros
        if len(candidate_memories) < limit:
            other_memories = [m for m in self.memories if m.task_type != task_type]
            candidate_memories.extend(other_memories[:limit - len(candidate_memories)])
        
        # Ordenar por similitud de tarea (simple word matching por ahora)
        def task_similarity(memory):
            task_words = set(task.lower().split())
            memory_words = set(memory.original_task.lower().split())
            if not task_words or not memory_words:
                return 0
            return len(task_words & memory_words) / len(task_words | memory_words)
        
        candidate_memories.sort(key=task_similarity, reverse=True)
        
        return candidate_memories[:limit]
    
    def get_success_patterns(self, task_type: str) -> List[str]:
        """Obtiene patrones de éxito para un tipo de tarea"""
        successful_memories = [
            m for m in self.memories 
            if m.task_type == task_type and m.final_result == 'success'
        ]
        
        patterns = []
        for memory in successful_memories:
            patterns.extend(memory.successful_patterns)
        
        # Contar frecuencia de patrones
        pattern_counts = {}
        for pattern in patterns:
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
        
        # Retornar patrones más comunes
        return sorted(pattern_counts.keys(), key=lambda x: pattern_counts[x], reverse=True)[:10]
    
    def _save_memories(self):
        """Guarda memorias al disco"""
        try:
            data = []
            for memory in self.memories:
                memory_dict = memory.__dict__.copy()
                if memory_dict['timestamp']:
                    memory_dict['timestamp'] = memory_dict['timestamp'].isoformat()
                data.append(memory_dict)
            
            with open(self.memories_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"🧠 RAG: Failed to save memories: {e}")
```

### PatternRecognizer - Reconocimiento de Patrones

```python
class PatternRecognizer:
    def __init__(self):
        self.known_patterns = {
            'login': {
                'success_indicators': [
                    'redirect to dashboard',
                    'login successful',
                    'welcome message',
                    'user menu visible',
                    'logout button present'
                ],
                'failure_indicators': [
                    'invalid credentials',
                    'login failed',
                    'error message',
                    'still on login page',
                    'captcha required'
                ],
                'typical_steps': ['navigate', 'fill_form', 'click_submit', 'verify_success']
            },
            'form_completion': {
                'success_indicators': [
                    'form submitted',
                    'confirmation message',
                    'redirect after submit',
                    'success notification'
                ],
                'failure_indicators': [
                    'validation errors',
                    'required fields empty',
                    'form not submitted',
                    'error notification'
                ],
                'typical_steps': ['navigate', 'fill_fields', 'validate', 'submit', 'confirm']
            },
            'data_extraction': {
                'success_indicators': [
                    'data extracted',
                    'file created',
                    'content saved',
                    'extraction complete'
                ],
                'failure_indicators': [
                    'no data found',
                    'extraction failed',
                    'empty results',
                    'access denied'
                ],
                'typical_steps': ['navigate', 'locate_data', 'extract', 'format', 'save']
            }
        }
    
    def analyze_execution_patterns(self, memory: ExecutionMemory) -> Tuple[List[str], List[str]]:
        """Analiza patrones en una ejecución"""
        successful_patterns = []
        failure_patterns = []
        
        task_type = memory.task_type
        if task_type not in self.known_patterns:
            return successful_patterns, failure_patterns
        
        patterns = self.known_patterns[task_type]
        
        # Analizar pasos para detectar patrones
        step_descriptions = [step.get('description', '') for step in memory.steps_taken]
        all_text = ' '.join(step_descriptions).lower()
        
        # Detectar indicadores de éxito
        for indicator in patterns['success_indicators']:
            if indicator.lower() in all_text:
                successful_patterns.append(f"success_indicator:{indicator}")
        
        # Detectar indicadores de fallo
        for indicator in patterns['failure_indicators']:
            if indicator.lower() in all_text:
                failure_patterns.append(f"failure_indicator:{indicator}")
        
        # Analizar secuencia de pasos
        step_sequence = self._analyze_step_sequence(memory.steps_taken, patterns['typical_steps'])
        if step_sequence:
            if memory.final_result == 'success':
                successful_patterns.append(f"step_sequence:{step_sequence}")
            else:
                failure_patterns.append(f"broken_sequence:{step_sequence}")
        
        return successful_patterns, failure_patterns
    
    def _analyze_step_sequence(self, steps: List[Dict[str, Any]], typical_steps: List[str]) -> str:
        """Analiza la secuencia de pasos"""
        if len(steps) < 2:
            return ""
        
        # Clasificar pasos en categorías
        step_categories = []
        for step in steps:
            action = step.get('action', '').lower()
            if 'navigate' in action or 'go_to' in action:
                step_categories.append('navigate')
            elif 'fill' in action or 'type' in action:
                step_categories.append('fill')
            elif 'click' in action:
                step_categories.append('click')
            elif 'extract' in action:
                step_categories.append('extract')
            else:
                step_categories.append('other')
        
        return '->'.join(step_categories[:5])  # Primeros 5 pasos
    
    def predict_success_probability(
        self, 
        current_steps: List[Dict[str, Any]], 
        task_type: str,
        memories: List[ExecutionMemory]
    ) -> float:
        """Predice probabilidad de éxito basada en patrones"""
        
        if not memories:
            return 0.5  # Neutral si no hay datos
        
        # Analizar pasos actuales
        current_sequence = self._analyze_step_sequence(current_steps, 
                                                      self.known_patterns.get(task_type, {}).get('typical_steps', []))
        
        # Comparar con memorias exitosas
        successful_memories = [m for m in memories if m.final_result == 'success']
        total_memories = len(memories)
        
        if not successful_memories:
            return 0.2  # Baja probabilidad si no hay éxitos previos
        
        # Base success rate
        base_success_rate = len(successful_memories) / total_memories
        
        # Ajustar basado en similitud de patrones
        pattern_matches = 0
        for memory in successful_memories:
            memory_sequence = self._analyze_step_sequence(memory.steps_taken,
                                                         self.known_patterns.get(task_type, {}).get('typical_steps', []))
            if current_sequence and memory_sequence and current_sequence in memory_sequence:
                pattern_matches += 1
        
        if successful_memories:
            pattern_bonus = (pattern_matches / len(successful_memories)) * 0.3
        else:
            pattern_bonus = 0
        
        return min(1.0, base_success_rate + pattern_bonus)
```

### ContextRetriever - Recuperación de Contexto

```python
class ContextRetriever:
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
    
    async def get_relevant_context(
        self, 
        current_task: str,
        current_steps: List[Dict[str, Any]],
        task_type: str,
        memories: List[ExecutionMemory]
    ) -> Dict[str, Any]:
        """Recupera contexto relevante para la tarea actual"""
        
        context = {
            'similar_executions': [],
            'success_patterns': [],
            'common_issues': [],
            'recommendations': [],
            'success_probability': 0.5
        }
        
        # Buscar ejecuciones similares
        similar_results = self.vector_store.search_similar(current_task, top_k=5)
        
        # Convertir a memorias si hay metadatos
        for result_id, similarity, metadata in similar_results:
            # Buscar memoria correspondiente
            matching_memories = [m for m in memories if m.execution_id == metadata.get('metadata', {}).get('execution_id')]
            if matching_memories:
                context['similar_executions'].append({
                    'memory': matching_memories[0],
                    'similarity': similarity
                })
        
        # Obtener patrones de éxito
        pattern_recognizer = PatternRecognizer()
        context['success_patterns'] = pattern_recognizer.known_patterns.get(task_type, {}).get('success_indicators', [])
        
        # Analizar problemas comunes
        failed_memories = [m for m in memories if m.final_result != 'success' and m.task_type == task_type]
        common_issues = []
        for memory in failed_memories:
            common_issues.extend(memory.issues_encountered)
        
        # Contar frecuencia de problemas
        issue_counts = {}
        for issue in common_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        context['common_issues'] = sorted(issue_counts.keys(), key=lambda x: issue_counts[x], reverse=True)[:5]
        
        # Generar recomendaciones
        context['recommendations'] = self._generate_recommendations(
            current_task, current_steps, task_type, memories
        )
        
        # Calcular probabilidad de éxito
        context['success_probability'] = pattern_recognizer.predict_success_probability(
            current_steps, task_type, memories
        )
        
        return context
    
    def _generate_recommendations(
        self,
        current_task: str,
        current_steps: List[Dict[str, Any]],
        task_type: str,
        memories: List[ExecutionMemory]
    ) -> List[str]:
        """Genera recomendaciones basadas en el contexto"""
        
        recommendations = []
        
        # Recomendaciones basadas en tipo de tarea
        if task_type == 'login':
            if len(current_steps) > 5:
                recommendations.append("Login taking too many steps - consider stopping if successful")
            if any('extract' in step.get('action', '').lower() for step in current_steps):
                recommendations.append("Avoid data extraction during login task")
        
        elif task_type == 'data_extraction':
            if len(current_steps) < 3:
                recommendations.append("Data extraction may need more navigation steps")
        
        # Recomendaciones basadas en memorias similares
        successful_memories = [m for m in memories if m.final_result == 'success' and m.task_type == task_type]
        if successful_memories:
            avg_steps = sum(m.total_steps for m in successful_memories) / len(successful_memories)
            if len(current_steps) > avg_steps * 1.5:
                recommendations.append(f"Current execution longer than typical successful ones (avg: {avg_steps:.1f} steps)")
        
        return recommendations
```

### Integración con PlannerService

```python
# Modificación en src/services/planner_service.py

class PlannerService:
    def __init__(self, ...):
        # ...existing code...
        
        # Añadir RAG service
        self.rag_service = RAGService()
    
    async def evaluate_before_step(self, current_step: int, context: Dict[str, Any]) -> bool:
        # ...existing evaluation code...
        
        # Obtener contexto RAG
        rag_context = await self.rag_service.get_relevant_context(
            current_task=self.original_task,
            current_steps=self.step_history,
            task_type=self.task_analyzer.task_type
        )
        
        # Incorporar recomendaciones RAG en la decisión
        if rag_context['recommendations']:
            logger.info(f"🧠 RAG Recommendations: {rag_context['recommendations']}")
        
        # Ajustar probabilidad de éxito basada en RAG
        progress.confidence_score = (progress.confidence_score + rag_context['success_probability']) / 2
        
        # ...resto del código de evaluación...
    
    def save_execution_memory(self, final_result: str, completion_percentage: float):
        """Guarda la memoria de la ejecución actual"""
        memory = ExecutionMemory(
            execution_id=f"exec_{int(time.time())}",
            original_task=self.original_task,
            task_type=self.task_analyzer.task_type,
            steps_taken=self.step_history,
            final_result=final_result,
            completion_percentage=completion_percentage,
            total_steps=self.step_count,
            duration_seconds=time.time() - self.start_time,
            issues_encountered=[],  # Se detectarían en análisis
            successful_patterns=[]   # Se detectarían en análisis
        )
        
        self.rag_service.add_execution_memory(memory)
        logger.info(f"🧠 RAG: Saved execution memory for {memory.execution_id}")
```

## 📋 Checklist de Implementación

- [ ] Crear `RAGService` clase principal
- [ ] Implementar `VectorStore` con embeddings
- [ ] Implementar `MemoryManager` para persistencia
- [ ] Crear `PatternRecognizer` para análisis de patrones
- [ ] Implementar `ContextRetriever` para búsqueda de contexto
- [ ] Integrar con `PlannerService`
- [ ] Crear base de datos de patrones conocidos
- [ ] Añadir tests para cada componente
- [ ] Validar con ejecuciones reales

## 🔗 Siguiente Paso
Proceder con `04-add-execution-controller.md` para el controlador de ejecución.
