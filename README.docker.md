# QAK Docker Configuration - Sin Next.js

## 🚀 Descripción

Esta configuración Docker proporciona una infraestructura completa para QAK sin el frontend de Next.js, optimizada para entornos de producción y desarrollo que solo requieren la API y el procesamiento en background.

## 📦 Servicios Incluidos

### 1. **Redis** (`qak-redis`)
- **Puerto:** 6379
- **Función:** Broker para Celery y almacenamiento de cache
- **Persistencia:** Volumen `redis_data` con AOF habilitado
- **Health Check:** `redis-cli ping`

### 2. **QAK API** (`qak-api`)
- **Puerto:** 8000
- **Función:** API principal FastAPI con todas las funcionalidades de QAK
- **Dockerfile:** `Dockerfile.api`
- **Health Check:** `curl -f http://localhost:8000/health`
- **Dependencias:** Redis

### 3. **Celery Worker** (`qak-celery-worker`)
- **Función:** Procesamiento de tareas en background
- **Dockerfile:** `Dockerfile.worker`
- **Health Check:** `celery inspect ping`
- **Dependencias:** Redis, QAK API

### 4. **Flower Monitor** (`qak-flower`) - Opcional
- **Puerto:** 5555
- **Función:** Monitoreo de tareas Celery
- **Perfil:** `monitoring` (solo se inicia con `--profile monitoring`)

## 🛠️ Instalación y Configuración

### Prerrequisitos

- Docker Desktop instalado y ejecutándose
- Al menos 4GB de RAM disponible
- Puertos 6379, 8000 y 5555 libres

### Configuración Rápida

1. **Clonar y navegar al proyecto:**
   ```bash
   cd /Users/<USER>/Proyectos/qak
   ```

2. **Configurar variables de entorno:**
   ```bash
   # El script copiará automáticamente .env.docker a .env si no existe
   cp .env.docker .env
   
   # Editar .env con tus API keys reales
   nano .env  # o tu editor preferido
   ```

3. **Configurar API Keys mínimas requeridas:**
   ```bash
   # En el archivo .env, configura al menos:
   OPENROUTER_API_KEY=tu_openrouter_api_key_aqui
   GOOGLE_API_KEY=tu_google_api_key_aqui
   MONGODB_URI=tu_mongodb_uri_aqui
   ```

4. **Iniciar servicios:**
   ```bash
   ./docker-start.sh start
   ```

## 🎯 Uso del Script de Gestión

El script `docker-start.sh` proporciona comandos simples para gestionar toda la infraestructura:

### Comandos Principales

```bash
# Iniciar todos los servicios
./docker-start.sh start

# Iniciar con monitoreo Flower
./docker-start.sh start monitoring

# Ver estado de servicios
./docker-start.sh status

# Ver logs en tiempo real
./docker-start.sh logs

# Reiniciar servicios
./docker-start.sh restart

# Detener servicios
./docker-start.sh stop

# Limpiar todo (contenedores, volúmenes, imágenes)
./docker-start.sh clean

# Mostrar ayuda
./docker-start.sh help
```

## 🌐 URLs de Servicios

Una vez iniciados los servicios, estarán disponibles en:

- **🚀 API Principal:** http://localhost:8000
- **📚 Documentación API:** http://localhost:8000/docs
- **🔍 Health Check:** http://localhost:8000/health
- **🌐 Browser Use UI:** http://localhost:8000/browser-use-ui
- **📊 Redis:** localhost:6379
- **🌸 Flower Monitor:** http://localhost:5555 (solo con perfil monitoring)

## 📁 Estructura de Archivos

```
.
├── Dockerfile.api              # Dockerfile para la API
├── Dockerfile.worker           # Dockerfile para Celery Worker
├── docker-compose.production.yml # Orquestación de servicios
├── docker-start.sh             # Script de gestión
├── .env.docker                 # Template de variables
├── .env                        # Variables de entorno (crear desde .env.docker)
└── data/                       # Datos persistentes
    ├── conversations/
    ├── codegen_sessions/
    ├── projects/
    ├── semantic_memories/
    ├── monitoring_data/
    ├── logs/
    ├── screenshots/
    └── exports/
```

## 🔧 Configuración Avanzada

### Variables de Entorno Importantes

```bash
# API Keys (REQUERIDAS)
OPENROUTER_API_KEY=tu_key_aqui
GOOGLE_API_KEY=tu_key_aqui

# Configuración híbrida de modelos
LLM_MODEL=google/gemini-2.0-flash-exp          # Modelo principal
BROWSER_USE_LLM_MODEL=openai/gpt-4.1-mini     # Para browser automation
AI_ANALYSIS_MODEL=openai/gpt-4.1-mini         # Para análisis de AI

# Base de datos
MONGODB_URI=tu_mongodb_uri_aqui
REDIS_URL=redis://redis:6379/0

# Almacenamiento de artifacts
R2_BUCKET_NAME=tu_bucket
R2_ACCESS_KEY_ID=tu_access_key
R2_SECRET_ACCESS_KEY=tu_secret_key

# Background jobs
USE_BACKGROUND_JOBS=true
CELERY_LOG_LEVEL=INFO

# Rate limiting optimizado
BROWSER_USE_OPENAI_RPM=30      # GPT-4.1-mini para browser-use
BROWSER_USE_OPENAI_TPM=2000000
AI_ANALYSIS_RPM=30             # GPT-4.1-mini para análisis
AI_ANALYSIS_TPM=2000000
```

### Personalización de Recursos

Puedes ajustar los recursos en `docker-compose.production.yml`:

```yaml
services:
  qak-api:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

## 🐛 Troubleshooting

### Problemas Comunes

1. **Error de conexión a Redis:**
   ```bash
   # Verificar que Redis esté ejecutándose
   docker-compose -f docker-compose.production.yml ps redis
   
   # Ver logs de Redis
   docker-compose -f docker-compose.production.yml logs redis
   ```

2. **API no responde:**
   ```bash
   # Verificar logs de la API
   docker-compose -f docker-compose.production.yml logs qak-api
   
   # Verificar health check
   curl http://localhost:8000/health
   ```

3. **Celery Worker no procesa tareas:**
   ```bash
   # Verificar logs del worker
   docker-compose -f docker-compose.production.yml logs celery-worker
   
   # Verificar conexión a Redis desde el worker
   docker-compose -f docker-compose.production.yml exec celery-worker redis-cli -h redis ping
   ```

4. **Problemas de permisos:**
   ```bash
   # Asegurar que los directorios de datos tengan permisos correctos
   sudo chown -R $USER:$USER data/
   chmod -R 755 data/
   ```

### Comandos de Diagnóstico

```bash
# Ver estado detallado de todos los servicios
docker-compose -f docker-compose.production.yml ps

# Ver logs de todos los servicios
docker-compose -f docker-compose.production.yml logs

# Ejecutar comando dentro del contenedor de la API
docker-compose -f docker-compose.production.yml exec qak-api bash

# Verificar conectividad de red
docker network ls
docker network inspect qak-network

# Ver uso de recursos
docker stats
```

## 🔄 Actualizaciones

Para actualizar a una nueva versión:

```bash
# Detener servicios
./docker-start.sh stop

# Actualizar código
git pull

# Reconstruir imágenes
docker-compose -f docker-compose.production.yml build --no-cache

# Iniciar servicios
./docker-start.sh start
```

## 📊 Monitoreo

### Con Flower (Celery)

```bash
# Iniciar con monitoreo
./docker-start.sh start monitoring

# Acceder a Flower
open http://localhost:5555
```

### Logs Centralizados

```bash
# Logs en tiempo real de todos los servicios
./docker-start.sh logs

# Logs específicos de un servicio
docker-compose -f docker-compose.production.yml logs -f qak-api
docker-compose -f docker-compose.production.yml logs -f celery-worker
docker-compose -f docker-compose.production.yml logs -f redis
```

## 🚀 Optimizaciones de Producción

### Para entornos de alta carga:

1. **Escalar Celery Workers:**
   ```bash
   docker-compose -f docker-compose.production.yml up -d --scale celery-worker=3
   ```

2. **Configurar Redis con más memoria:**
   ```yaml
   redis:
     command: redis-server --maxmemory 1gb --maxmemory-policy allkeys-lru
   ```

3. **Usar Redis Cluster para alta disponibilidad:**
   - Configurar múltiples instancias de Redis
   - Usar Redis Sentinel para failover automático

## 🧠 Estrategia Híbrida de Modelos LLM

Esta configuración Docker implementa una estrategia híbrida optimizada:

### 🎯 Asignación de Modelos por Funcionalidad

- **🌟 Gemini 2.0 Flash Exp** (`LLM_MODEL`): Modelo principal para operaciones generales
  - Generación de Gherkin desde user stories
  - Validación de resultados de tests
  - Traducción de prompts
  - Mejora y refinamiento de tests

- **🚀 OpenAI GPT-4.1-mini** (`BROWSER_USE_LLM_MODEL`): Automatización web
  - Browser automation con browser-use
  - Navegación web inteligente
  - Interacción con elementos DOM
  - Capturas y análisis de pantalla

- **🔍 OpenAI GPT-4.1-mini** (`AI_ANALYSIS_MODEL`): Análisis de código y tests
  - Análisis de resultados de tests
  - Generación de reportes de calidad
  - Detección de patrones en código
  - Optimización de test suites

### 💡 Beneficios de esta Estrategia

1. **Costo-Efectividad**: GPT-4.1-mini es más económico para tareas específicas
2. **Rendimiento**: Cada modelo optimizado para su caso de uso
3. **Rate Limits**: Distribución de carga entre diferentes proveedores
4. **Flexibilidad**: Fácil cambio de modelos por funcionalidad

## 📝 Notas Importantes

- **Sin Next.js:** Esta configuración NO incluye el frontend web
- **API Only:** Solo expone la API REST en el puerto 8000
- **Background Jobs:** Celery maneja todas las tareas asíncronas
- **Persistencia:** Los datos se almacenan en volúmenes Docker
- **Networking:** Todos los servicios están en la red `qak-network`
- **Health Checks:** Todos los servicios tienen verificaciones de salud
- **Modelos Híbridos:** Usa diferentes LLMs optimizados por funcionalidad

## 🤝 Contribuir

Para contribuir a esta configuración Docker:

1. Fork el repositorio
2. Crea una rama para tu feature
3. Realiza tus cambios
4. Prueba la configuración completa
5. Envía un Pull Request

---

**¿Necesitas ayuda?** Abre un issue en el repositorio con los logs relevantes y la descripción del problema.