#!/usr/bin/env python3
"""
Script para probar la migración del frontend al nuevo endpoint de analytics.
Verifica que el endpoint /analytics-v2 funcione correctamente y que el legacy
tenga los headers de deprecación apropiados.
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta


BASE_URL = "http://localhost:8000/api"


async def test_endpoint_headers(session: aiohttp.ClientSession, endpoint: str, params: dict = None):
    """Prueba un endpoint y verifica headers de respuesta."""
    try:
        url = f"{BASE_URL}{endpoint}"
        async with session.get(url, params=params) as response:
            headers = dict(response.headers)
            status = response.status
            
            if status == 200:
                data = await response.json()
                return {
                    "status": status,
                    "headers": headers,
                    "data_keys": list(data.keys()) if isinstance(data, dict) else None,
                    "data_source": data.get("dataSource") if isinstance(data, dict) else None,
                    "total_tests": data.get("totalTests") if isinstance(data, dict) else None
                }
            else:
                error_text = await response.text()
                return {
                    "status": status,
                    "headers": headers,
                    "error": error_text
                }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


async def main():
    """Función principal de prueba."""
    print("🧪 Testing Frontend Migration to Analytics v2")
    print("=" * 50)
    
    # Parámetros de prueba
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    test_params = {
        "start_date": start_date.strftime("%Y-%m-%d"),
        "end_date": end_date.strftime("%Y-%m-%d"),
        "detailed": "true"
    }
    
    async with aiohttp.ClientSession() as session:
        print(f"📅 Testing with date range: {start_date} to {end_date}")
        print(f"🔗 Base URL: {BASE_URL}")
        print()
        
        # Test 1: Nuevo endpoint /analytics-v2
        print("1️⃣ Testing NEW endpoint (/analytics-v2)...")
        v2_result = await test_endpoint_headers(session, "/analytics-v2", test_params)
        
        if v2_result["status"] == 200:
            print("   ✅ Status: 200 OK")
            print(f"   📊 Data source: {v2_result.get('data_source', 'Not specified')}")
            print(f"   📈 Total tests: {v2_result.get('total_tests', 'N/A')}")
            print(f"   🔑 Response keys: {v2_result.get('data_keys', [])}")
            
            # Check for new v2 fields
            if 'x-data-source' in v2_result["headers"]:
                print(f"   🏷️  Data source header: {v2_result['headers']['x-data-source']}")
        else:
            print(f"   ❌ Status: {v2_result['status']}")
            print(f"   🚨 Error: {v2_result.get('error', 'Unknown error')}")
        
        print()
        
        # Test 2: Endpoint legacy /analytics (deprecado)
        print("2️⃣ Testing LEGACY endpoint (/analytics) - Should show deprecation...")
        legacy_result = await test_endpoint_headers(session, "/analytics", test_params)
        
        if legacy_result["status"] == 200:
            print("   ✅ Status: 200 OK")
            print(f"   📈 Total tests: {legacy_result.get('total_tests', 'N/A')}")
            
            # Check deprecation headers
            headers = legacy_result["headers"]
            if 'x-deprecated' in headers:
                print(f"   ⚠️  Deprecated header: {headers['x-deprecated']}")
            if 'x-deprecated-message' in headers:
                print(f"   💬 Deprecation message: {headers['x-deprecated-message']}")
            if 'x-deprecated-replacement' in headers:
                print(f"   🔄 Replacement: {headers['x-deprecated-replacement']}")
            
            if not any(key.startswith('x-deprecated') for key in headers.keys()):
                print("   ⚠️  WARNING: No deprecation headers found!")
        else:
            print(f"   ❌ Status: {legacy_result['status']}")
            print(f"   🚨 Error: {legacy_result.get('error', 'Unknown error')}")
        
        print()
        
        # Test 3: Endpoint de comparación
        print("3️⃣ Testing COMPARISON endpoint (/analytics-comparison)...")
        comparison_result = await test_endpoint_headers(session, "/analytics-comparison", test_params)
        
        if comparison_result["status"] == 200:
            print("   ✅ Status: 200 OK")
            print(f"   🔑 Response keys: {comparison_result.get('data_keys', [])}")
        else:
            print(f"   ❌ Status: {comparison_result['status']}")
            print(f"   🚨 Error: {comparison_result.get('error', 'Unknown error')}")
        
        print()
        print("📋 SUMMARY")
        print("=" * 20)
        
        # Summary
        v2_ok = v2_result["status"] == 200
        legacy_ok = legacy_result["status"] == 200
        legacy_deprecated = any(key.startswith('x-deprecated') for key in legacy_result.get("headers", {}).keys())
        comparison_ok = comparison_result["status"] == 200
        
        print(f"✅ New endpoint (/analytics-v2): {'WORKING' if v2_ok else 'FAILED'}")
        print(f"⚠️  Legacy endpoint (/analytics): {'WORKING' if legacy_ok else 'FAILED'}")
        print(f"🏷️  Deprecation headers: {'PRESENT' if legacy_deprecated else 'MISSING'}")
        print(f"🔄 Comparison endpoint: {'WORKING' if comparison_ok else 'FAILED'}")
        
        print()
        if v2_ok and legacy_deprecated:
            print("🎉 MIGRATION SUCCESSFUL!")
            print("   - New endpoint is working")
            print("   - Legacy endpoint is properly deprecated")
            print("   - Frontend can now use /analytics-v2")
        else:
            print("⚠️  MIGRATION ISSUES DETECTED:")
            if not v2_ok:
                print("   - New endpoint /analytics-v2 is not working")
            if not legacy_deprecated:
                print("   - Legacy endpoint missing deprecation headers")
        
        print()
        print("🚀 Next steps:")
        print("   1. Start the frontend: cd web && npm run dev")
        print("   2. Navigate to analytics page")
        print("   3. Check browser console for any errors")
        print("   4. Verify data loads correctly")
        print("   5. Monitor server logs for deprecation warnings")


if __name__ == "__main__":
    asyncio.run(main())