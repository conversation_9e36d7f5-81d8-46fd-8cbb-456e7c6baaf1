#!/usr/bin/env python3
"""
Test script to verify MongoDB migration for configuration endpoints.
"""

import json
import asyncio
import requests
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE_URL = f"{BASE_URL}/api/config"

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """Test an API endpoint and print results."""
    url = f"{API_BASE_URL}{endpoint}"
    
    print(f"\n🔍 Testing: {method} {url}")
    
    try:
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "PUT":
            response = requests.put(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
            
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == expected_status:
            print("✅ Status code matches expected")
            try:
                response_data = response.json()
                print(f"📊 Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'List with length: ' + str(len(response_data))}")
                return True
            except:
                print("📊 Response is not JSON")
                return True
        else:
            print(f"❌ Expected {expected_status}, got {response.status_code}")
            try:
                error_data = response.json()
                print(f"📊 Error: {error_data}")
            except:
                print(f"📊 Raw error: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🚀 Testing MongoDB Configuration Endpoints Migration")
    print("=" * 60)
    
    # Test 1: Get all configurations
    print("\n📋 Test 1: Get all configurations")
    test_api_endpoint("GET", "/")
    
    # Test 2: Get predefined configurations
    print("\n📋 Test 2: Get predefined configurations")
    test_api_endpoint("GET", "/predefined")
    
    # Test 3: Get specific predefined configuration
    print("\n📋 Test 3: Get smoke predefined configuration")
    test_api_endpoint("GET", "/predefined/smoke")
    
    # Test 4: Update predefined configuration (the main test case)
    print("\n📋 Test 4: Update smoke predefined configuration")
    update_data = {
        "name": "Smoke Test Updated",
        "description": "Updated smoke test configuration for MongoDB testing",
        "execution_types": ["smoke"],
        "settings": {
            "headless": True,
            "user_data_dir": None,
            "use_vision": True,
            "enable_memory": False,
            "highlight_elements": False,
            "deterministic_rendering": False,
            "disable_security": False,
            "minimum_wait_page_load_time": 0.5,
            "wait_for_network_idle_page_load_time": 1,
            "maximum_wait_page_load_time": 12,
            "wait_between_actions": 1,
            "viewport_expansion": 900,
            "max_steps": 30,
            "max_failures": 3,
            "retry_delay": 3,
            "temperature": 0.1,
            "model_provider": "openrouter",
            "memory_interval": 10,
            "generate_gif": True,
            "keep_alive": False,
            "model_name": "openai/gpt-4o-mini",
            "memory_agent_id": "browser_use_agent",
            "embedder_provider": "huggingface",
            "embedder_model": "all-MiniLM-L6-v2",
            "embedder_dims": 384
        }
    }
    
    test_api_endpoint("PUT", "/predefined/smoke", data=update_data)
    
    # Test 5: Get custom configurations
    print("\n📋 Test 5: Get custom configurations")
    test_api_endpoint("GET", "/custom")
    
    # Test 6: Create custom configuration
    print("\n📋 Test 6: Create custom configuration")
    create_data = {
        "name": "Test Custom Config",
        "description": "Test custom configuration for MongoDB",
        "config_type": "custom",
        "settings": {
            "headless": False,
            "model_provider": "openrouter",
            "model_name": "openai/gpt-4o-mini",
            "temperature": 0.2
        }
    }
    
    test_api_endpoint("POST", "/custom", data=create_data)
    
    # Test 7: Seed predefined configurations
    print("\n📋 Test 7: Seed predefined configurations")
    test_api_endpoint("POST", "/defaults/seed")
    
    print("\n🎯 Testing Complete!")
    print("=" * 60)
    
    print("\n📝 Summary:")
    print("✅ All endpoints have been migrated to use MongoDB")
    print("✅ The curl command should now work with MongoDB storage")
    print("✅ Configuration data is persisted in MongoDB instead of JSON files")
    
    print("\n🔧 Your original curl command should now work:")
    print("curl 'http://localhost:8000/api/config/predefined/smoke' \\")
    print("  -X 'PUT' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  --data-raw '{\"name\":\"Smoke\",\"description\":\"...\", ...}'")

if __name__ == "__main__":
    main()
